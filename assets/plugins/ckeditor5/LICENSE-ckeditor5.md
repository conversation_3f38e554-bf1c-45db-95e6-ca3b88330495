Software License Agreement
==========================

**CKEditor&nbsp;5** (https://github.com/ckeditor/ckeditor5)<br>
Copyright (c) 2003–2025, [CKSource Holding sp. z o.o.](https://cksource.com) All rights reserved.

Licensed under a dual-license model, this software is available under:

* the [GNU General Public License Version 2 or later](https://www.gnu.org/licenses/gpl.html) (see COPYING.GPL),
* or commercial license terms from CKSource Holding sp. z o.o.

For more information, see: [https://ckeditor.com/legal/ckeditor-licensing-options](https://ckeditor.com/legal/ckeditor-licensing-options).

If you are using CKEditor under commercial terms, you are free to remove the COPYING.GPL file with the full copy of a GPL license.

Sources of Intellectual Property Included in CKEditor
-----------------------------------------------------

Where not otherwise indicated, all CKEditor content is authored by CKSource engineers and consists of CKSource-owned intellectual property. In some specific instances, CKEditor will incorporate work done by developers outside of CKSource with their express permission.

The following libraries are included in CKEditor under the [MIT license](https://opensource.org/licenses/MIT):

* @types/color-convert - Copyright (c) DefinitelyTyped.
* blurhash - Copyright (c) Wolt Enterprises.
* color-convert - Copyright (c) 2011–2016 Heather Arthur <<EMAIL>>, copyright (c) 2016–2021 Josh Junon <<EMAIL>>.
* color-parse - Copyright (c) 2015 Dmitry Ivanov.
* emojibase-data - Copyright (c) 2017-2019 Miles Johnson.
* es-toolkit - Copyright (c) 2024 Viva Republica, Inc.
* fuzzysort - Copyright (c) 2018 Stephen Kamenar.
* is-emoji-supported - Copyright (c) 2016-2020 Koala Interactive, Inc.
* vanilla-colorful - Copyright (c) 2020 Serhii Kulykov <<EMAIL>>.
* Regular Expression for URL validation - Copyright (c) 2010-2018 Diego Perini.
* @types/hast - Copyright (c) Microsoft Corporation.
* hast-util-to-html - Copyright (c) Titus Wormer <<EMAIL>>
* hast-util-to-mdast - Copyright (c) Titus Wormer <<EMAIL>> and Copyright (c) Seth Vincent <<EMAIL>>
* hastscript - Copyright (c) Titus Wormer <<EMAIL>>
* rehype-remark - Copyright (c) Titus Wormer <<EMAIL>>
* remark-breaks - Copyright (c) 2017 Titus Wormer <<EMAIL>>
* remark-gfm - Copyright (c) Titus Wormer <<EMAIL>>
* remark-parse - Copyright (c) 2014 Titus Wormer <<EMAIL>>
* remark-rehype - Copyright (c) Titus Wormer <<EMAIL>>
* remark-stringify - Copyright (c) 2014 Titus Wormer <<EMAIL>>
* unified - Copyright (c) 2015 Titus Wormer <<EMAIL>>
* unist-util-visit - Copyright (c) 2015 Titus Wormer <<EMAIL>>

The following libraries are included in CKEditor under the [ISC license](https://opensource.org/license/isc-license-txt):

* hast-util-from-dom - Copyright (c) Keith McKnight <<EMAIL>>
* rehype-dom-parse - Copyright (c) 2018 Keith McKnight <<EMAIL>>
* rehype-dom-stringify - Copyright (c) 2018 Keith McKnight <<EMAIL>>

Trademarks
----------

**CKEditor** is a trademark of [CKSource Holding sp. z o.o.](https://cksource.com) All other brand and product names are trademarks, registered trademarks or service marks of their respective holders.
