@media print {
	body {
		margin: 0 !important;
	}
}

:root {
	--ck-content-font-family: '<PERSON>to';
	--ck-editor-height: auto;
}

.main-container {
	font-family: var(--ck-content-font-family);
	width: fit-content;
	margin-left: auto;
	margin-right: auto;
    width: 96%;
}

.editor-container__editor-wrapper {
	display: flex;
	margin: 0 auto;
	justify-content: center;
}

.editor-container_document-editor {
	border: 1px solid var(--ck-color-base-border);
}

.editor-container_document-editor .editor-container__toolbar {
	display: flex;
	position: relative;
	box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
}

.editor-container_document-editor .editor-container__toolbar > .ck.ck-toolbar {
	flex-grow: 1;
	width: 0;
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0;
	border-top: 0;
	border-left: 0;
	border-right: 0;
}

.editor-container_document-editor .editor-container__menu-bar > .ck.ck-menu-bar {
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0;
	border-top: 0;
	border-left: 0;
	border-right: 0;
}

.editor-container_document-editor .editor-container__editor-wrapper {
	max-height: var(--ck-editor-height);
	min-height: var(--ck-editor-height);
	overflow-y: scroll;
	background: var(--ck-color-base-foreground);
}

.editor-container_document-editor .editor-container__editor {
	margin-top: 28px;
	margin-bottom: 28px;
	height: 100%;
}

.editor-container_document-editor .editor-container__editor .ck.ck-editor__editable {
	box-sizing: border-box;
	min-width: calc(210mm + 2px);
	max-width: calc(210mm + 2px);
	min-height: 297mm;
	height: fit-content;
	padding: 20mm 12mm;
	border: 1px hsl(0, 0%, 82.7%) solid;
	background: hsl(0, 0%, 100%);
	color: #000; /* 兜底：强制黑色文字，避免 WPS 粘贴文字看起来空白 */
	box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
	flex: 1 1 auto;
    margin: 20px auto;
    margin-bottom: 20px;
}

.editor-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-right: 40px;
}

.editor-actions .btn {
    margin: 0;
}

/* 大纲面板样式 */
.outline-panel {
    width: 300px;
    min-width: 300px;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: width 0.3s ease;
}

.outline-panel.collapsed {
    width: 40px;
    min-width: 40px;
}

.outline-header {
    padding: 12px 15px;
    background: #e9ecef;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 15px;
}

.outline-header h4 {
    margin: 0;
    font-size: 14px;
    color: #495057;
    white-space: nowrap;
    overflow: hidden;
}

.outline-toggle {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: all 0.2s;
}

.outline-toggle:hover {
    background: #dee2e6;
    color: #495057;
}

.outline-panel.collapsed .outline-toggle i {
    transform: rotate(180deg);
}

.outline-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.outline-panel.collapsed .outline-content {
    display: none;
}

.outline-empty {
    color: #6c757d;
    font-size: 12px;
    text-align: center;
    padding: 20px 10px;
}

.outline-item {
    padding: 6px 10px;
    margin: 2px 0;
    cursor: pointer;
    border-radius: 3px;
    font-size: 13px;
    line-height: 1.4;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.outline-item:hover {
    background: #e9ecef;
}

.outline-item.active {
    background: #007bff;
    color: white;
    border-left-color: #0056b3;
}

.outline-item.level-1 {
    font-weight: 600;
    padding-left: 10px;
}

.outline-item.level-2 {
    padding-left: 25px;
    font-size: 12px;
}

.outline-item.level-3 {
    padding-left: 40px;
    font-size: 11px;
}

.outline-item.level-4,
.outline-item.level-5,
.outline-item.level-6 {
    padding-left: 55px;
    font-size: 11px;
    color: #6c757d;
}

/* 编辑区域高亮样式 */
.editing-section {
    position: relative;
}

.editing-section::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #007bff, #0056b3);
    border-radius: 2px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

.editing-indicator {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
    to { transform: translateX(-50%) translateY(0); opacity: 1; }
}

/* 自适应屏幕高度的编辑器样式 */
.techbuy {
    height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 20px;
}

.techbuy-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.form-section {
    flex-shrink: 0;
    padding: 15px;
}

.main-container {
    flex: 1;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    padding: 0 15px;
    gap: 15px;
}

.editor-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border-radius: 4px;
}

.editor-container__toolbar {
    flex-shrink: 0;
}

.editor-container__editor-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
}

.editor-container__editor .ck-editor__editable {
    min-height: 100%;
    border: none;
    border-radius: 0;
}

.editor-footer {
    flex-shrink: 0;
    padding: 15px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

/* 响应式调整 */
@media (max-height: 600px) {
    .form-section {
        padding: 10px;
    }
    .editor-footer {
        padding: 10px;
    }
}

@media (max-width: 1024px) {
    .main-container {
        flex-direction: column;
    }
    
    .outline-panel {
        width: 100%;
        min-width: auto;
        max-height: 200px;
        order: -1;
    }
    
    .outline-panel.collapsed {
        max-height: 45px;
        width: 100%;
    }
    
    .outline-content {
        max-height: 150px;
    }
}

@media (max-width: 768px) {
    .outline-panel {
        max-height: 150px;
    }
    
    .outline-content {
        max-height: 100px;
    }
    
    .outline-header h4 {
        font-size: 12px;
    }
    
    .outline-item {
        font-size: 11px;
        padding: 4px 8px;
    }
}
