let page_techbuyquery = function () {
    let get_height = getHeight() - 133;
    let row_num = Math.floor(get_height / 33);

    let global = {
        filter_conditions: new Map(),
        filter_sqls: [],
    };

    let init_data = {
        container: '#table-stockout',
        url: `/get_product_info`,
        post_data: {
            id: "",
            name: '',
            sort: "name, material, size",
            rec: row_num,
            cate: "",
            filter: "",
        },
        edit: false,
        // 需要排序的字段，不排序的不写
        header_names: {
            "名称": "name",
            "技术编号": "tech_no",
            "牌号": "material",
            "规格": "size",
            "状态": "status",
            "供应商": "c.文本字段1",
        },

        row_fn: row_fn,
    };

    tool_table.table_init(init_data);
    tool_table.fetch_table(() => {
        make_filter();
    });


    function row_fn(tr) {

        return `<tr><td>${tr.序号}</td><td class="名称">${tr.名称}</td><td class="牌号">${tr.牌号}</td>
            <td class="规格">${tr.规格}</td><td class="状态">${tr.状态}</td><td class="技术编号">${tr.技术编号}</td>
            <td class="供应商">${tr.供应商}</td>
            <td class="库存支数">${tr.库存支数 == 0 ? '' : tr.库存支数}</td><td class="库存长度">${tr.库存长度 == 0 ? '' : tr.库存长度}</td>
            <td class="库存重量">${tr.库存重量 == 0 ? '' : Number(tr.库存重量).toFixed(1)}</td>
            <td class="安全库存预警">${tr.安全库存预警 || ''}</td><td class="最高库存预警">${tr.最高库存预警 || ''}</td>
            <td class="在途数量">${tr.在途数量 || ''}</td><td class="在途到货日">${tr.在途到货日 || ''}</td>
            <td class="备注">${tr.备注}</td><td class="id" hidden>${tr.id}</td></tr>`;
    }

    //点击搜索按钮
    document.querySelector('#serach-button').addEventListener('click', function () {
        let fields = document.querySelector('#search-fields').value;
        init_data.post_data.name = fields;
        init_data.post_data.page = 1;
        tool_table.fetch_table(() => {
            make_filter();
        });
    });

    // ------------------------------------ 过滤部分开始--------------------------------------------

    // 使用通用过滤器
    const tableFilter = initTableFilter({
        getThs: () => document.querySelectorAll('.table-container thead th'),
        has_filter: ['牌号', '规格', '状态', '技术编号', '供应商'],
        url: '/get_product_info_filter_items',
        state: global,
        thDecorator: (th) => { th.style.position = 'relative'; },
        position: 'relative',
        positionOffset: { top: 5, left: -50 },
        buildFetchPostData: ({ name, baseFilterSql }) => {
            let search = document.querySelector('#search-fields').value;
            return {
                name: search,
                filter_name: name,
                filter: baseFilterSql,
            };
        },
        onRefreshTable: (filterSql) => {
            document.querySelector('.f-choose').innerHTML = '';
            Object.assign(tool_table.table_data().post_data, { filter: filterSql, page: 1 });
            tool_table.fetch_table(() => {
                make_filter();
            });
        },
    });

    function make_filter() {
        tableFilter.ensureButtons();
        tableFilter.updateButtonColors();
    }

    // ------------------------------- 过滤部分结束 --------------------------------

    // 产品信息表单HTML生成函数
    function productInfoFormHtml(data) {
        const formHtml = `
            <form id="product-info-form">
                <div class="form-group">
                    <div class="form-label">                                    
                        <label style="margin-right: 0;">名称</label>
                    </div>
                    <select class="select-sm has-value" id="product-name">
                        <option value="圆钢">圆钢</option>
                        <option value="无缝钢管">无缝钢管</option>
                    </select>
                </div>
                <div class="form-group autocomplete" style="z-index: 1003;">
                    <label for="product-material">牌号</label>
                    <input type="text" class="form-control" id="product-material" placeholder="请输入牌号" value="${data?.material || ''}">
                </div>
                <div class="form-group">
                    <label for="product-size">规格</label>
                    <input type="text" class="form-control" id="product-size" placeholder="请输入规格" value="${data?.size || ''}">
                </div>
                <div class="form-group autocomplete" style="z-index: 1002;">
                    <label for="product-status">状态</label>
                    <input type="text" class="form-control" id="product-status" placeholder="请输入状态" value="${data?.status || ''}">
                </div>
                <div class="form-group autocomplete">
                    <label for="product-tech-no">技术编号</label>
                    <input type="text" class="form-control" id="product-tech-no" placeholder="请输入技术编号" value="${data?.tech_no || ''}">
                </div>
                <div class="form-group autocomplete">
                    <label for="supplier">供应商</label>
                    <input type="text" class="form-control" id="supplier" placeholder="请输入供应商" value="${data?.supplier || ''}">
                </div>
                <div class="form-group">
                    <label for="product-safety-stock">安全库存预警</label>
                    <input type="text" class="form-control" id="product-safety-stock" placeholder="请输入安全库存预警" value="${data?.safety_stock || ''}">
                </div>
                <div class="form-group">
                    <label for="product-max-stock">最高库存预警</label>
                    <input type="text" class="form-control" id="product-max-stock" placeholder="请输入最高库存预警" value="${data?.max_stock || ''}">
                </div>
                <div class="form-group">
                    <label for="product-note">备注</label>
                    <input type="text" class="form-control" id="product-note" placeholder="请输入备注" value="${data?.note || ''}">
                </div>
            </form>
        `;
        return formHtml;
    }

    // 增加按钮事件
    document.querySelector('#button-add').addEventListener('click', function () {
        document.querySelector('.modal-body').innerHTML = productInfoFormHtml();

        // 设置自动完成功能
        setTimeout(() => {
            set_auto();
        }, 100);

        document.querySelector('.modal-title').textContent = "增加产品信息";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";

        document.querySelector('.modal').style.display = "block";
        document.querySelector('.modal-body input').focus();
    });

    // 编辑按钮事件
    document.querySelector('#button-edit').addEventListener('click', function () {
        let focus = document.querySelector('#table-stockout .focus');
        if (!focus) {
            notifier.show('请先选择产品信息', 'danger');
            return false;
        }

        const data = {
            name: focus.querySelector('.名称').textContent,
            material: focus.querySelector('.牌号').textContent,
            size: focus.querySelector('.规格').textContent,
            status: focus.querySelector('.状态').textContent,
            tech_no: focus.querySelector('.技术编号').textContent,
            supplier: focus.querySelector('.供应商').textContent,
            safety_stock: focus.querySelector('.安全库存预警').textContent || '',
            max_stock: focus.querySelector('.最高库存预警').textContent || '',
            note: focus.querySelector('.备注').textContent,
        }

        document.querySelector('.modal-body').innerHTML = productInfoFormHtml(data);

        // 设置选中的产品名称
        document.querySelector('#product-name').value = data.name;

        // 设置自动完成功能
        setTimeout(() => {
            set_auto();
        }, 100);

        document.querySelector('.modal-title').textContent = "编辑产品信息";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";

        document.querySelector('.modal').style.display = "block";
        document.querySelector('.modal-body input').focus();
    });

    function set_auto() {
        const materialInput = document.querySelector('#product-material');
        const statusInput = document.querySelector('#product-status');
        const techNoInput = document.querySelector('#product-tech-no');
        const supplierInput = document.querySelector('#supplier');

        const modalRect = document.querySelector('.modal-dialog').getBoundingClientRect();

        // 牌号自动完成
        const materialRect = materialInput.getBoundingClientRect();
        const materialModalRelativeTop = materialRect.top - modalRect.top;
        const materialModalRelativeLeft = materialRect.left - modalRect.left;
        let materialAutoComp = new AutoInput(materialInput, "牌号", "/get_material_auto", '', '', materialModalRelativeTop + 32, materialModalRelativeLeft);
        materialAutoComp.init();

        // 状态自动完成
        const statusRect = statusInput.getBoundingClientRect();
        const statusModalRelativeTop = statusRect.top - modalRect.top;
        const statusModalRelativeLeft = statusRect.left - modalRect.left;
        let statusAutoComp = new AutoInput(statusInput, "状态", "/get_status_info_auto", '', '', statusModalRelativeTop + 32, statusModalRelativeLeft);
        statusAutoComp.init();

        // 技术编号自动完成
        const techNoRect = techNoInput.getBoundingClientRect();
        const techNoModalRelativeTop = techNoRect.top - modalRect.top;
        const techNoModalRelativeLeft = techNoRect.left - modalRect.left;
        let techNoAutoComp = new AutoInput(techNoInput, "技术编号", "/get_tech_no_auto", '', '', techNoModalRelativeTop + 32, techNoModalRelativeLeft);
        techNoAutoComp.init();

        // 供应商自动完成
        const supplierRect = supplierInput.getBoundingClientRect();
        const supplierModalRelativeTop = supplierRect.top - modalRect.top;
        const supplierModalRelativeLeft = supplierRect.left - modalRect.left;
        let supplierAutoComp = new AutoInput(supplierInput, "供应商", "/get_customer_auto", '', '', supplierModalRelativeTop + 32, supplierModalRelativeLeft);
        supplierAutoComp.init();
    }

    // Modal提交按钮处理已在模板中定义

    // 点击modal提交按钮
    document.addEventListener('click', function (e) {
        if (e.target && e.target.id === 'modal-sumit-button') {
            const modalTitle = document.querySelector('.modal-title').textContent.trim();

            if (modalTitle === "增加产品信息") {
                const data = {
                    name: document.querySelector('#product-name').value.trim(),
                    material: document.querySelector('#product-material').value.trim(),
                    size: document.querySelector('#product-size').value.trim(),
                    status: document.querySelector('#product-status').value.trim(),
                    tech_no: document.querySelector('#product-tech-no').value.trim(),
                    supplier: document.querySelector('#supplier').getAttribute("data"),
                    safety_stock: document.querySelector('#product-safety-stock').value.trim() || '0',
                    max_stock: document.querySelector('#product-max-stock').value.trim() || '0',
                    note: document.querySelector('#product-note').value.trim(),
                };

                // 验证必填字段
                if (!data.name || !data.material || !data.size || !data.tech_no) {
                    notifier.show('请填写名称、牌号、规格和技术编号', 'danger');
                    return false;
                }

                fetch("/add_product_info", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(data),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            tool_table.fetch_table(() => {
                                make_filter();
                            });
                            notifier.show('添加成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                        close_modal();
                    });

            } else if (modalTitle === "编辑产品信息") {
                let focus = document.querySelector('#table-stockout .focus');
                if (!focus) {
                    notifier.show('请先选择产品信息', 'danger');
                    return false;
                }

                const data = {
                    id: focus.querySelector('.id').textContent,
                    name: document.querySelector('#product-name').value.trim(),
                    material: document.querySelector('#product-material').value.trim(),
                    size: document.querySelector('#product-size').value.trim(),
                    status: document.querySelector('#product-status').value.trim(),
                    tech_no: document.querySelector('#product-tech-no').value.trim(),
                    supplier: document.querySelector('#supplier').getAttribute("data"),
                    safety_stock: document.querySelector('#product-safety-stock').value.trim() || '0',
                    max_stock: document.querySelector('#product-max-stock').value.trim() || '0',
                    note: document.querySelector('#product-note').value.trim(),
                };

                // 验证必填字段
                if (!data.name || !data.material || !data.size || !data.tech_no) {
                    notifier.show('请填写名称、牌号、规格和技术编号', 'danger');
                    return false;
                }

                fetch("/edit_product_info", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(data),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            tool_table.fetch_table(() => {
                                make_filter();
                            });
                            notifier.show('编辑成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                        close_modal();
                    });
            }
        }
    });

    // 处理导出按钮点击事件
    document.querySelector('#button-out').addEventListener('click', function () {
        // 获取当前的搜索条件和筛选条件
        const search = document.querySelector('#search-fields').value;
        let filter = "";
        if (global.filter_sqls.length > 0) {
            filter = global.filter_sqls[0].sql;
        }

        // 调用导出接口
        fetch('/export_product_info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                search: search,
                filter: filter
            })
        })
            .then(response => response.json())
            .then(fileName => {
                if (fileName === -1) {
                    notifier.show('导出失败，请检查权限', 'danger');
                    return;
                }

                // 下载文件
                download_file(`/download/${fileName}`);

                notifier.show('导出成功', 'success');
            })
            .catch(error => {
                console.error('Error:', error);
                notifier.show('导出失败', 'danger');
            });
    });

    modal_init();


}();