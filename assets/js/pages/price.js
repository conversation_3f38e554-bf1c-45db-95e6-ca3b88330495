let price_manage = function () {
    let get_height = window.innerHeight - 138 - 175;    
    let row_num = Math.floor(get_height / 30);
    let init_data = {};
    let global_data = {};

    function fetch_customer(name, func) {
        fetch(`/fetch_price_customer?customer=${name}`, {
            method: 'get',
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    if (typeof func === "function") {
                        func(content[0]);
                    }

                    let line = 1;
                    let trs = "";
                    for (let item of content) {
                        let focus = line == 1 ? "<tr class='focus'>" : "<tr>";
                        trs += `${focus}<td class='id' hidden>${item.id}</td><td class='customer_id' hidden>${item.customer_id}</td>
                                <td class='name'>${item.name}</td><td class='date'>${item.date}</td></tr>`;
                        line++;
                    }

                    document.querySelector('.table-price tbody').innerHTML = trs;
                    const rows = document.querySelectorAll('.table-price tbody tr');
                    rows.forEach(row => {
                        row.addEventListener('click', function () {
                            rows.forEach(r => r.classList.remove('focus'));
                            row.classList.add('focus');

                            document.querySelector('#customer-name').textContent = row.querySelector('.name').textContent + "　" +
                                row.querySelector('.date').textContent;

                            global_data.id = row.querySelector(".id").textContent;
                            global_data.customer_id = row.querySelector(".customer_id").textContent;
                            Object.assign(init_data.post_data, { id: global_data.id, page: 1 });
                            tool_table.fetch_table();
                        });
                    });
                }
            });
    }

    function first_fetch_customer(data) {
        init_data = {
            container: '.table-items',
            url: `/fetch_price_items`,
            post_data: {
                id: data.id.toString(),
                name: '',
                sort: "pc.ord, material",
                rec: row_num,
                cate: "",
            },
            edit: false,
            row_fn: table_row,
        };

        global_data.id = data.id.toString();
        global_data.customer_id = data.customer_id.toString();

        tool_table.table_init(init_data);
        tool_table.fetch_table(set_height);
        document.querySelector('#customer-name').textContent = data.name + "　" + data.date;
    }

    function table_row(item) {
        return `<tr><td class='id' hidden>${item.id}</td><td>${item.序号}</td><td class='cate' title='${item.cate}'>${item.cate}</td>
                <td class='name'>${item.name}</td><td class='material'>${item.material}</td><td class='size' title='${item.size}'>${item.size}</td>
                <td class='status' title='${item.status}'>${item.status}</td><td class='factory'>${item.factory}</td><td class='all_price'>${item.all_price}</td>
                <td class='retail_price'>${item.retail_price}</td><td class='cut_price'>${item.cut_price}</td>
                <td class='note'>${item.note}</td></tr>`;
    }

    function set_height() {
        document.querySelector('.table-price').style.height = document.querySelector('.table-items').clientHeight + "px";
    }

    fetch_customer("all", first_fetch_customer);

    // 左表操作 ----------------------------------------------------

    // 左表搜索
    document.querySelector('#search-customer-button').addEventListener('click', function () {
        let customer_name = document.querySelector('#search-input-customer').value.trim();
        fetch_customer(customer_name);
    });

    // 左表增加按键
    document.querySelector('#add-customer-button').addEventListener('click', function () {
        let focus = document.querySelector('.table-price .focus');
        if (!focus) {
            notifier.show('请先选择价格协议', 'danger');
            return false;
        }

        const name = focus.querySelector('.name').textContent;
        const date = focus.querySelector('.date').textContent;
        const customer_id = focus.querySelector('.customer_id').textContent;

        alert_confirm(`将以 ${name} ${date} 为模板增加，确认吗？`, {
            confirmCallBack: () => {
                const formHtml = `
                                <form id="category-form" style="margin-left: 30px;">
                                    <div class="form-group autocomplete">
                                        <label for="price-name" style="width: 80px;">客户名称</label>
                                        <input type="text" class="form-control" id="price-name" placeholder="请输入客户名称" value="${name}" data="${customer_id}" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="start-date" style="width: 80px;">开始日期</label>
                                        <input type="text" class="form-control" id="start-date" style="width: 215px;" placeholder="请输入开始日期" required>
                                    </div>
                                </form>
                            `;

                document.querySelector('.modal-body').innerHTML = formHtml;

                setTimeout(() => {
                    const price_name = document.querySelector('#price-name');
                    const modalRect = document.querySelector('.modal-dialog').getBoundingClientRect();
                    const rect1 = price_name.getBoundingClientRect();
                    // 相对于 modal 的位置
                    const modalRelativeTop1 = rect1.top - modalRect.top;
                    const modalRelativeLeft1 = rect1.left - modalRect.left;
                    let auto_comp = new AutoInput(price_name, "客户", "/get_customer_auto", '', '', modalRelativeTop1 + 32, modalRelativeLeft1);
                    auto_comp.init();
                }, 100);

                const start_date = document.querySelector('#start-date');
                start_date.value = new Date().Format("yyyy-MM-dd");

                laydate.render({
                    elem: start_date,
                    showBottom: false,
                });

                document.querySelector('.modal-title').textContent = "增加协议价格";
                document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";

                document.querySelector('.modal').style.display = "block";
                document.querySelector('.modal-body input').focus();
            }
        });
    });

    // 左表编辑按键
    document.querySelector('#edit-customer-button').addEventListener('click', function () {
        let focus = document.querySelector('.table-price .focus');
        if (!focus) {
            notifier.show('请先选择价格协议', 'danger');
            return false;
        }

        const name = focus.querySelector('.name').textContent;
        const customer_id = focus.querySelector('.customer_id').textContent;
        const date = focus.querySelector('.date').textContent;

        const formHtml = `
                        <form id="category-form" style="margin-left: 30px;">
                            <div class="form-group autocomplete">
                                <label for="price-name" style="width: 80px;">客户名称</label>
                                <input type="text" class="form-control" id="price-name" placeholder="请输入客户名称" value="${name}" data="${customer_id}" required>
                            </div>
                            <div class="form-group">
                                <label for="start-date" style="width: 80px;">开始日期</label>
                                <input type="text" class="form-control" id="start-date" style="width: 215px;" placeholder="请输入开始日期" value="${date}" required>
                            </div>
                        </form>
                    `;

        document.querySelector('.modal-body').innerHTML = formHtml;

        setTimeout(() => {
            const price_name = document.querySelector('#price-name');
            const modalRect = document.querySelector('.modal-dialog').getBoundingClientRect();
            const rect1 = price_name.getBoundingClientRect();
            // 相对于 modal 的位置
            const modalRelativeTop1 = rect1.top - modalRect.top;
            const modalRelativeLeft1 = rect1.left - modalRect.left;
            let auto_comp = new AutoInput(price_name, "客户", "/get_customer_auto", '', '', modalRelativeTop1 + 32, modalRelativeLeft1);
            auto_comp.init();
        }, 100);

        const start_date = document.querySelector('#start-date');

        laydate.render({
            elem: start_date,
            showBottom: false,
        });

        document.querySelector('.modal-title').textContent = "编辑协议";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";

        document.querySelector('.modal').style.display = "block";
    });

    // 左表删除按钮
    document.querySelector('#del-customer-button').onclick = function () {
        let focus = document.querySelector('.table-price .focus');
        if (!focus) {
            notifier.show('请先选择价格协议', 'danger');
            return false;
        }
        const id = focus.querySelector('.id').textContent;
        const name = focus.querySelector('.name').textContent;
        const date = focus.querySelector('.date').textContent;

        alert_confirm(`确认删除 ${name} ${date} 协议吗？`, {
            confirmCallBack: () => {
                fetch("/del_price_customer", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ id: id }),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            fetch_customer("all");
                            notifier.show('删除成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                    });
            }
        });
    };

    // 右表操控 ------------------------------------------------------  

    // 右表搜索
    document.querySelector('#serach-button').addEventListener('click', function () {
        let search_name = document.querySelector('#search-input').value.trim();
        Object.assign(init_data.post_data, { cate: search_name, page: 1 });
        tool_table.fetch_table();
    });

    function price_form_html(data) {
        const formHtml = `
            <form id="price-form">
                <div class="form-group">
                    <div class="form-label">                                    
                        <label style="margin-right: 0;">类别</label>
                    </div>
                    <select class="select-sm has-value" id="product-cate">
                    </select>
                </div>
                <div class="form-group">
                    <div class="form-label">                                    
                        <label style="margin-right: 0;">名称</label>
                    </div>
                    <select class="select-sm has-value" id="product-name">
                        <option value="圆钢">圆钢</option>
                        <option value="无缝钢管">无缝钢管</option>
                        <option value="棒">棒</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="product-material">钢种</label>
                    <input type="text" class="form-control" id="product-material" placeholder="请输入钢种" value="${data?.material || ''}">
                </div>
                <div class="form-group">
                    <label for="product-size">规格</label>
                    <input type="text" class="form-control" id="product-size" placeholder="请输入规格" value="${data?.size || ''}">
                </div>
                <div class="form-group">
                    <label for="product-status">状态</label>
                    <input type="text" class="form-control" id="product-status" placeholder="请输入状态" value="${data?.status || ''}">
                </div>
                <div class="form-group">
                    <label for="product-origin">产地</label>
                    <input type="text" class="form-control" id="product-origin" placeholder="请输入产地" value="${data?.factory || ''}">
                </div>
                <div class="form-group">
                    <label for="all-price">整支单价</label>
                    <input type="text" class="form-control" id="all-price" placeholder="请输入整支单价" value="${data?.all_price || ''}">
                </div>
                <div class="form-group">
                    <label for="retail-price">切分单价</label>
                    <input type="text" class="form-control" id="retail-price" placeholder="请输入切分单价" value="${data?.retail_price || ''}">
                </div>
                <div class="form-group">
                    <label for="cut-price">锯料费</label>
                    <input type="text" class="form-control" id="cut-price" placeholder="请输入锯料费" value="${data?.cut_price || ''}">
                </div>
                <div class="form-group">
                    <label for="price-note">备注</label>
                    <input type="text" class="form-control" id="price-note" placeholder="请输入备注" value="${data?.note || ''}">
                </div>
            </form>
        `;

        return formHtml;
    }

    // 右表增加按键
    document.querySelector('#add-button').addEventListener('click', async function () {
        document.querySelector('.modal-body').innerHTML = price_form_html();

        const response = await fetch(`/fetch_product_cate`);
        const data = await response.json();
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = item.name;
            document.querySelector('#product-cate').appendChild(option);
        });

        document.querySelector('.modal-title').textContent = "增加价格条目";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";

        document.querySelector('.modal').style.display = "block";
        document.querySelector('.modal-body input').focus();
    });

    // 右表编辑按键
    document.querySelector('#edit-button').addEventListener('click', async function () {
        let focus = document.querySelector('.table-items .focus');
        if (!focus) {
            notifier.show('请先选择条目', 'danger');
            return false;
        }

        const data = {
            name: focus.querySelector('.name').textContent,
            material: focus.querySelector('.material').textContent,
            size: focus.querySelector('.size').textContent,
            status: focus.querySelector('.status').textContent,
            factory: focus.querySelector('.factory').textContent,
            all_price: focus.querySelector('.all_price').textContent,
            retail_price: focus.querySelector('.retail_price').textContent,
            cut_price: focus.querySelector('.cut_price').textContent,
            note: focus.querySelector('.note').textContent,
        }

        document.querySelector('.modal-body').innerHTML = price_form_html(data);

        const response = await fetch(`/fetch_product_cate`);
        const data_cate = await response.json();
        data_cate.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = item.name;
            document.querySelector('#product-cate').appendChild(option);
        });

        document.querySelector('#product-cate').value = data_cate.find(item => item.name === focus.querySelector('.cate').textContent)?.id || '';
        document.querySelector('#product-name').value = data.name;


        document.querySelector('.modal-title').textContent = "编辑价格条目";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";

        document.querySelector('.modal').style.display = "block";
        document.querySelector('.modal-body input').focus();
    });

    // 右表删除按钮
    document.querySelector('#del-button').onclick = function () {
        let focus = document.querySelector('.table-items .focus');
        if (!focus) {
            notifier.show('请先选择条目', 'danger');
            return false;
        }
        const id = focus.querySelector('.id').textContent;

        alert_confirm(`确认删除所选的条目吗？`, {
            confirmCallBack: () => {
                fetch("/del_price_item", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ id: id }),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            tool_table.fetch_table();
                            notifier.show('删除成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                    });
            }
        });
    };

    // ------------------------------------------------------------

    // 点击 modal 提交按钮
    document.querySelector('#modal-sumit-button').onclick = function () {
        const cate = document.querySelector('.modal-title').textContent.trim();
        if (cate == "增加协议价格") {
            const focus = document.querySelector('.table-price .focus');
            const price_id = focus.querySelector('.id').textContent;
            const customer_id = document.querySelector('#price-name').getAttribute('data');
            const start_date = document.querySelector('#start-date').value.trim();

            if (!customer_id) {
                notifier.show('客户需从下拉菜单中选择', 'danger');
                return false;
            }

            if (!start_date) {
                notifier.show('请填写开始日期', 'danger');
                return false;
            }

            const data = {
                price_id: price_id,
                new_date: start_date,
                customer_id: customer_id
            }

            fetch("/add_price_customer", {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        notifier.show('添加成功', 'success');
                        fetch_customer("all");
                    } else {
                        notifier.show('操作失败', 'danger');
                    }

                    close_modal();
                });
        } else if (cate == "编辑协议") {
            const focus = document.querySelector('.table-price .focus');
            const price_id = focus.querySelector('.id').textContent;
            const customer_id = document.querySelector('#price-name').getAttribute('data');
            const start_date = document.querySelector('#start-date').value.trim();

            if (!customer_id) {
                notifier.show('客户需从下拉菜单中选择', 'danger');
                return false;
            }

            if (!start_date) {
                notifier.show('请填写开始日期', 'danger');
                return false;
            }

            const data = {
                price_id: price_id,
                new_date: start_date,
                customer_id: customer_id
            }

            fetch("/edit_price_customer", {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        notifier.show('编辑成功', 'success');
                        fetch_customer("all");
                    } else {
                        notifier.show('操作失败', 'danger');
                    }

                    close_modal();
                });
        } else if (cate == "增加价格条目") {
            const data = {
                price_id: global_data.id,
                cate_id: document.querySelector('#product-cate').value,
                name: document.querySelector('#product-name').value.trim(),
                material: document.querySelector('#product-material').value.trim(),
                size: document.querySelector('#product-size').value.trim(),
                status: document.querySelector('#product-status').value.trim(),
                factory: document.querySelector('#product-origin').value.trim(),
                all_price: document.querySelector('#all-price').value.trim(),
                cut_price: document.querySelector('#cut-price').value.trim(),
                retail_price: document.querySelector('#retail-price').value.trim(),
                note: document.querySelector('#price-note').value.trim(),
            }

            fetch("/add_price_item", {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        tool_table.fetch_table();
                        notifier.show('添加成功', 'success');
                    } else {
                        notifier.show('操作失败', 'danger');
                    }

                    close_modal();
                });
        } else if (cate == "编辑价格条目") {
            let focus = document.querySelector('.table-items .focus');

            const data = {
                id: focus.querySelector('.id').textContent,
                cate_id: document.querySelector('#product-cate').value,
                name: document.querySelector('#product-name').value.trim(),
                material: document.querySelector('#product-material').value.trim(),
                size: document.querySelector('#product-size').value.trim(),
                status: document.querySelector('#product-status').value.trim(),
                factory: document.querySelector('#product-origin').value.trim(),
                all_price: document.querySelector('#all-price').value.trim(),
                cut_price: document.querySelector('#cut-price').value.trim(),
                retail_price: document.querySelector('#retail-price').value.trim(),
                note: document.querySelector('#price-note').value.trim(),
            }

            fetch("/edit_price_item", {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        tool_table.fetch_table();
                        notifier.show('编辑成功', 'success');
                    } else {
                        notifier.show('操作失败', 'danger');
                    }

                    close_modal();
                });
        }
    }

    // 右表历史按钮
    document.querySelector('#history-button').onclick = function () {
        let focus = document.querySelector('.table-items .focus');
        if (!focus) {
            notifier.show('请先选择条目', 'danger');
            return false;
        }

        // 获取当前选中的价格协议的客户ID
        const priceFocus = document.querySelector('.table-price .focus');
        if (!priceFocus) {
            notifier.show('请先选择价格协议', 'danger');
            return false;
        }

        // 调用后台接口获取历史价格数据
        fetch(`/fetch_price_history`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                customer_id: global_data.customer_id,
                name: focus.querySelector('.name').textContent,
                material: focus.querySelector('.material').textContent,
                size: focus.querySelector('.size').textContent,
                status: focus.querySelector('.status').textContent,
                factory: focus.querySelector('.factory').textContent
            }),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1 && Array.isArray(content) && content.length === 4) {
                    showHistoryChart(content, {
                        name: focus.querySelector('.name').textContent,
                        material: focus.querySelector('.material').textContent,
                        size: focus.querySelector('.size').textContent,
                        status: focus.querySelector('.status').textContent,
                        factory: focus.querySelector('.factory').textContent
                    });
                } else {
                    notifier.show('获取历史价格失败', 'danger');
                }
            })
            .catch(error => {
                console.error('获取历史价格数据失败:', error);
                notifier.show('获取历史价格失败', 'danger');
            });
    };

    // 显示历史价格趋势图表
    function showHistoryChart(data, itemInfo) {
        // data格式: [[序号数组], [月份数组], [整支单价数组], [切分单价数组]]
        let [indexes, months, allPrices, retailPrices] = data;

        // 只保留最近12个月的数据
        if (months.length > 12) {
            const startIndex = months.length - 12;
            months = months.slice(startIndex);
            allPrices = allPrices.slice(startIndex);
            retailPrices = retailPrices.slice(startIndex);
        }

        // 创建图表容器HTML
        const chartHtml = `
            <div class="history-chart-container">
                <div class="chart-header">
                    <h4>${itemInfo.name} - ${itemInfo.material} - ${itemInfo.size}</h4>
                    <p class="chart-subtitle">状态: ${itemInfo.status} | 近12个月价格趋势</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="priceHistoryChart" width="600" height="400"></canvas>
                </div>
            </div>
        `;

        // 设置modal内容
        document.querySelector('.modal-body').innerHTML = chartHtml;
        document.querySelector('.modal-title').textContent = "价格历史趋势 - " + document.querySelector('.table-price .focus .name').textContent;
        document.querySelector('.modal-dialog').style.cssText = "max-width: 1000px; margin-top: 170px;";
        document.querySelector('#modal-sumit-button').style.cssText = "display: none;";

        // 显示modal
        document.querySelector('.modal').style.display = "block";

        // 等待DOM渲染完成后绘制图表
        setTimeout(() => {
            drawPriceChart(months, allPrices, retailPrices);
        }, 100);
    }

    // 绘制价格趋势图表
    function drawPriceChart(labels, allPrices, retailPrices) {
        const ctx = document.getElementById('priceHistoryChart').getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '整支单价',
                        data: allPrices,
                        backgroundColor: 'rgba(54, 162, 235, 0.5)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 2,
                        fill: false,
                        pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    },
                    {
                        label: '切分单价',
                        data: retailPrices,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 2,
                        fill: false,
                        pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true
                        }
                    }]
                },
                tooltips: {
                    enabled: false
                },
                hover: {
                    mode: null,
                    animationDuration: 0
                },
                animation: {
                    onComplete: function () {
                        var chartInstance = this;
                        let ctx = chartInstance.ctx;
                        ctx.fillStyle = "dark-gray";
                        ctx.textAlign = 'center';
                        ctx.textBaseline = 'bottom';

                        this.data.datasets.forEach(function (dataset, i) {
                            var meta = chartInstance.controller.getDatasetMeta(i);
                            meta.data.forEach(function (point, index) {
                                var data = dataset.data[index];
                                ctx.fillText(data + '元', point._model.x, point._model.y - 5);
                            });
                        });
                    }
                }
            }
        });
    }

    //数据导入和导出 ------------------------------------------------------------------------------

    // 导出到 excel
    document.querySelector('#data-out').addEventListener('click', function () {
        const data = {
            price_id: parseInt(global_data.id),
            name: document.querySelector('.table-price .focus .name').textContent,
            date: document.querySelector('.table-price .focus .date').textContent,
            search: document.querySelector('#search-input').value.trim(),
        }

        fetch(`/price_out`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    download_file(`/download/${content}`);
                    notifier.show('成功导出至 Excel 文件', 'success');
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    });

    // //批量导入
    // let fileBtn = document.getElementById('choose_file');

    // document.getElementById('data-in').addEventListener('click', function () {
    //     fileBtn.click();
    // });

    // fileBtn.addEventListener('change', () => {
    //     if (checkFileType(fileBtn)) {
    //         const fd = new FormData();
    //         fd.append('file', fileBtn.files[0]);
    //         fetch('/price_in', {
    //             method: 'POST',
    //             body: fd,
    //         })
    //             .then(res => res.json())
    //             .then(content => {
    //                 if (content != -1 && content != -2) {
    //                     fileBtn.value = "";

    //                 } else if (content == -1) {
    //                     notifier.show('缺少操作权限', 'danger');
    //                 } else {
    //                     notifier.show('excel 表列数不符合', 'danger');
    //                 }
    //             });
    //     } else {
    //         notifier.show('需要 excel 文件', 'danger');
    //     }
    // });

    modal_init();
}();
