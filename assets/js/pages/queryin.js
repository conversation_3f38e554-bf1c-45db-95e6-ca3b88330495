let page_documentquery = function () {
    let get_height = getHeight() - 168;
    let row_num = Math.floor(get_height / 30);

    let init_data = {
        container: '.table-documents',
        url: `/fetch_in_docs`,
        post_data: {
            id: "",
            name: '',
            sort: 'ds.单号 desc',
            rec: row_num,
            cate: '',
        },
        edit: false,
        header_names: {
            "入库单号": "ds.单号",
            "采购单号": "ds.文本字段6",
            "名称": "名称", 
            "材质": "材质", 
            "规格": "规格",
            "供应商": "供应商",
            "入库日期": "ds.日期",
            "到货日期": "ds.文本字段5",
            "来料重量": "实数字段1",
            "实际重量": "实数字段2",
            "理论重量": "实数字段3",
            "经办人": "经办人",
        },

        row_fn: table_row,
    };

    // 执行日期实例
    service.set_date();
    let date1 = document.querySelector('#search-date1').value;
    let date2 = document.querySelector('#search-date2').value;
    init_data.post_data.cate = `${date1}${SPLITER}${date2}`;

    tool_table.table_init(init_data);
    tool_table.fetch_table(fei_restore);

    function table_row(tr) {
        const shen = tr.提交审核 ? '是' : '否';
        const pic = tr.图片 ? `<a href='javascript:void(0)' class="pic-link" data-pic-url="${tr.图片}">查看图片</a>` : '无';
        return row = `<tr>
            <td>${tr.序号}</td><td class="入库单号"><a href="/material_in/${tr.入库单号}" target="_blank" title="查阅编辑">${tr.入库单号}</a></td>
            <td class="采购单号">${tr.采购单号}</td><td class="名称">${tr.名称}</td><td class="材质">${tr.材质}</td>
            <td class="规格">${tr.规格}</td><td class="供应商">${tr.供应商}</td><td class="入库日期">${tr.入库日期}</td>
            <td class="到货日期">${tr.到货日期}</td><td class="来料重量">${tr.来料重量}</td><td class="实际重量">${tr.实际重量}</td>
            <td class="理论重量">${Number(tr.理论重量).toFixed(0)}</td><td class="图片">${pic}</td>
            <td class="经办人">${tr.经办人}</td><td class="提交审核">${shen}</td><td class="审核">${tr.审核}</td><td class="区域">${tr.区域}</td>
            <td class="备注">${tr.备注}</td>
            </tr>`;
    }

    document.querySelector('#serach-button').addEventListener('click', function () {
        search_table();
    });

    function search_table() {
        let search = document.querySelector('#search-input').value;
        let date1 = document.querySelector('#search-date1').value;
        let date2 = document.querySelector('#search-date2').value;
        Object.assign(tool_table.table_data().post_data, { name: search, cate: `${date1}${SPLITER}${date2}`, page: 1 });
        tool_table.fetch_table(fei_restore);
    }

    // 作废按钮变换, 表格数据加载后调用
    function fei_restore(data) {
        let trs = document.querySelectorAll('tbody tr');
        let fei_button = document.querySelector('#fei-button');

        for (let i = 0; i < trs.length; i++) {
            trs[i].addEventListener('click', function () {
                if (this.classList.contains('void')) {
                    fei_button.disabled = true;
                    fei_button.style.background = 'gray';
                } else {
                    fei_button.disabled = false;
                    fei_button.style.background = '#2574a9';
                }
            });
        }

        // 更新合计
        let total_row = document.querySelector('.sum-data');
        if (total_row) {
            total_row.querySelector('.sum1').textContent = data[3].toFixed(0) + " KG";
            total_row.querySelector('.sum2').textContent = data[4].toFixed(0) + " KG";
            total_row.querySelector('.sum3').textContent = data[5].toFixed(0) + " KG";
        }

        // 签字图片查看
        let links = document.querySelectorAll('.pic-link');
        links.forEach(link => {
            link.addEventListener('click', function () {
                let imgUrl = this.getAttribute('data-pic-url');
                document.querySelector('.modal-title').textContent = '签字图预览';
                document.querySelector('.modal-body').innerHTML = `<img src="${imgUrl}" style="max-width:100%; display:block; margin:0 auto;">`;
                document.querySelector('.modal-dialog').style.cssText = "max-width: 1230px;"
                document.querySelector('.modal').style.display = "block";
                document.querySelector('#modal-sumit-button').style.display = "none";
            });
        });
    }

    //作废单据
    let fei_btn = document.querySelector('#fei-button');
    if (fei_btn) {
        fei_btn.addEventListener('click', function () {
            let chosed = document.querySelector('tbody .focus');
            if (chosed) {
                let dh = chosed.querySelector('td:nth-child(2)').textContent;
                let del = {
                    id: dh,
                    rights: "作废",
                    base: "products",
                }
                fetch(`/before_fei`, {
                    method: 'post',
                    body: dh,
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            alert_confirm(`单据 ${dh} 作废后无法恢复，确认作废吗？`, {
                                confirmCallBack: () => {
                                    fetch(`/documents_fei`, {
                                        method: 'post',
                                        headers: {
                                            "Content-Type": "application/json",
                                        },
                                        body: JSON.stringify(del),
                                    })
                                        .then(response => response.json())
                                        .then(content => {
                                            if (content != -1) {
                                                search_table();
                                            } else {
                                                notifier.show('权限不够，操作失败', 'danger');
                                            }
                                        });
                                }
                            });
                        } else {
                            notifier.show(content, 'danger', 4000, 280);
                        }
                    });
            } else {
                notifier.show('请先选择单据', 'danger');
            }
        });
    }

    modal_init();
}();