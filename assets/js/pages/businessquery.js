let page_businessquery = function () {
    let get_height = getHeight() - 138;
    let row_num = Math.floor(get_height / 33);

    //执行日期实例------------------------------------------------
    service.set_date();

    let date1 = document.querySelector('#search-date1').value;
    let date2 = document.querySelector('#search-date2').value;

    //表格搜索----------------------------------------------------
    let init_data = {
        container: '#table-business',
        url: `/fetch_business`,
        post_data: {
            id: "",
            name: '',
            sort: "单号 DESC",
            rec: row_num,
            cate: `${date1}${SPLITER}${date2}`,
        },
        edit: false,
        header_names: {
            "日期": "日期",
            "单号": "单号",
            "合同编号": "documents.文本字段6",
            "类别": "documents.类别",
            "客户名称": "customers.名称",
            "单据金额": "应结金额",
            "商品名称": "split_part(node_name,' ',2)",
            "材质": "split_part(node_name,' ',1)",
            "规格": "规格型号",
            "状态": "documents.文本字段2",
            "长度": "长度",
            "数量": "数量",
            "价格": "单价",
            "重量": "重量",
            "备注": "documents.备注"
        },
        row_fn: row_fn,
    };

    tool_table.table_init(init_data);
    tool_table.fetch_table();

    //点击搜索按钮
    document.querySelector('#serach-button').addEventListener('click', function () {
        let fields = document.querySelector('#search-fields').value;
        let date1 = document.querySelector('#search-date1').value;
        let date2 = document.querySelector('#search-date2').value;

        init_data.post_data.name = fields;
        init_data.post_data.cate = `${date1}${SPLITER}${date2}`;

        tool_table.table_init(init_data);
        tool_table.fetch_table();
    });

    function row_fn(tr) {
        let row = tr.split(SPLITER);
        return `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2]}</td><td>${row[3]}</td><td>${row[4]}</td><td>${row[5]}</td>
            <td>${row[6]}</td><td>${row[7]}</td><td>${row[8]}</td><td>${row[9]}</td><td>${row[10]}</td>
            <td>${row[11]}</td><td>${row[12]}</td><td>${row[13]}</td><td>${row[14]}</td><td>${row[15]}</td></tr>`;
    }

    // 导出数据
    document.querySelector('#data-out').addEventListener('click', () => {
        let da1 = document.querySelector('#search-date1').value;
        let da2 = document.querySelector('#search-date2').value;
        let name = document.querySelector('#search-fields').value;
        let data = `${da1}${SPLITER}${da2}${SPLITER}${name}`;
        fetch(`/business_excel`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    download_file(`/download/业务往来明细表.xlsx`);
                    notifier.show('成功导出至 Excel 文件', 'success');
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    });
}();
