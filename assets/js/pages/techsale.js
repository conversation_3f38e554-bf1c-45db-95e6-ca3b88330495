const {
    DecoupledEditor,
    Alignment,
    Autoformat,
    AutoImage,
    AutoLink,
    Autosave,
    BalloonToolbar,
    Bold,
    Bookmark,
    Code,
    Essentials,
    FindAndReplace,
    FontBackgroundColor,
    FontColor,
    FontFamily,
    FontSize,
    Fullscreen,
    Heading,
    HorizontalLine,
    ImageBlock,
    ImageCaption,
    ImageEditing,
    ImageInline,
    ImageInsert,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageTextAlternative,
    ImageToolbar,
    ImageUpload,
    ImageUtils,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    PageBreak,
    Paragraph,
    PasteFromOffice,
    RemoveFormat,
    SimpleUploadAdapter,
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    Strikethrough,
    Subscript,
    Superscript,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextTransformation,
    Underline,
    GeneralHtmlSupport
} = window.CKEDITOR || {};

// 全局变量声明
let doc_id, uuid, init_data;
let readonly = false;
const LICENSE_KEY = 'GPL';

// 默认模板内容
const defaultTemplate = "<h3>一、机械性能</h3><h3>二、其他</h3>";

// 初始化数据函数
async function initializeDocumentData() {
    const customer = document.getElementById('customer');
    const auto_comp2 = new AutoInput(customer, "客户", "/customer_auto");
    auto_comp2.init();

    const material = document.getElementById('material');
    const auto_comp1 = new AutoInput(material, "全部", "/get_material");
    auto_comp1.init();

    // 获取文档ID
    const doc_id_cate = document.querySelector('#document-id').textContent.trim();

    if (doc_id_cate === '新文档') {
        uuid = generateUUID();
        init_data = defaultTemplate;
        document.getElementById('save-button').style.display = 'block';
        document.getElementById('uppdf-button').style.display = 'block';
        document.getElementById('build-date').value = new Date().Format("yyyy-MM-dd");
    } else {
        try {
            doc_id = doc_id_cate.split("__")[0];
            uuid = doc_id;
            const doc_cate = doc_id_cate.split("__")[1];

            const response = await fetch(`/get_tech_sale_data?id=${doc_id}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.fei) {
                document.getElementById('fei-pic').style.display = 'block';
                setTimeout(() => {
                    disableAllInputs();
                }, 200);
            }

            if (data) {
                // 设置编辑器内容，处理转义字符
                init_data = unescapeHtml(data.content) || defaultTemplate;

                // 填充表单字段
                document.getElementById('customer').setAttribute("data", data.customer_id);
                document.getElementById('customer').value = data.customer;
                document.getElementById('product-type').value = data.p_type;
                document.getElementById('material').value = data.p_material;
                document.getElementById('spec-name').value = data.name;
                document.getElementById('spec-number').value = data.tech_no;
                document.getElementById('build-date').value = data.build_date;

                if (data.pdf_url) {
                    document.getElementById('customer-pdf').innerHTML = `<div><a href="${data.pdf_url}?${Math.random()}">客户协议PDF</a></div>`;
                } else {
                    document.getElementById('customer-pdf').innerHTML = "暂未上传协议";
                }
            } else {
                init_data = defaultTemplate;
            }
        } catch (error) {
            // 发生错误时使用默认模板
            init_data = defaultTemplate;

            // 显示错误提示
            if (typeof notifier !== 'undefined') {
                notifier.show('加载文档数据失败，已使用默认模板', 'warning');
            }
        }
    }
}

// 创建编辑器配置的函数
function createEditorConfig(initialData) {
    return {
        toolbar: {
            items: [
                'undo',
                'redo',
                '|',
                'heading',
                '|',
                'fontSize',
                'fontFamily',
                'fontColor',
                'fontBackgroundColor',
                '|',
                'bold',
                'italic',
                'underline',
                'alignment',
                '|',
                'bulletedList',
                'numberedList',
                'outdent',
                'indent',
                '|',
                'insertTable'
            ],
            shouldNotGroupWhenFull: false
        },
        plugins: [
            Alignment,
            Autoformat,
            AutoImage,
            AutoLink,
            Autosave,
            BalloonToolbar,
            Bold,
            Bookmark,
            Code,
            Essentials,
            FindAndReplace,
            FontBackgroundColor,
            FontColor,
            FontFamily,
            FontSize,
            Fullscreen,
            Heading,
            HorizontalLine,
            ImageBlock,
            ImageCaption,
            ImageEditing,
            ImageInline,
            ImageInsert,
            ImageInsertViaUrl,
            ImageResize,
            ImageStyle,
            ImageTextAlternative,
            ImageToolbar,
            ImageUpload,
            ImageUtils,
            Indent,
            IndentBlock,
            Italic,
            Link,
            LinkImage,
            List,
            PageBreak,
            Paragraph,
            PasteFromOffice,
            RemoveFormat,
            SimpleUploadAdapter,
            SpecialCharacters,
            SpecialCharactersArrows,
            SpecialCharactersCurrency,
            SpecialCharactersEssentials,
            SpecialCharactersLatin,
            SpecialCharactersMathematical,
            SpecialCharactersText,
            Strikethrough,
            Subscript,
            Superscript,
            Table,
            TableCaption,
            TableCellProperties,
            TableColumnResize,
            TableProperties,
            TableToolbar,
            TextTransformation,
            Underline
        ],
        balloonToolbar: ['bold', 'italic', '|', 'bulletedList', 'numberedList'],
        fontFamily: {
            supportAllValues: true
        },
        fontSize: {
            options: [10, 12, 14, 'default', 18, 20, 22],
            supportAllValues: true
        },
        fullscreen: {
            onEnterCallback: container =>
                container.classList.add(
                    'editor-container',
                    'editor-container_document-editor',
                    'editor-container_include-fullscreen',
                    'main-container'
                )
        },
        heading: {
            options: [
                {
                    model: 'paragraph',
                    title: 'Paragraph',
                    class: 'ck-heading_paragraph'
                },
                {
                    model: 'heading1',
                    view: 'h1',
                    title: 'Heading 1',
                    class: 'ck-heading_heading1'
                },
                {
                    model: 'heading2',
                    view: 'h2',
                    title: 'Heading 2',
                    class: 'ck-heading_heading2'
                },
                {
                    model: 'heading3',
                    view: 'h3',
                    title: 'Heading 3',
                    class: 'ck-heading_heading3'
                },
                {
                    model: 'heading4',
                    view: 'h4',
                    title: 'Heading 4',
                    class: 'ck-heading_heading4'
                },
                {
                    model: 'heading5',
                    view: 'h5',
                    title: 'Heading 5',
                    class: 'ck-heading_heading5'
                },
                {
                    model: 'heading6',
                    view: 'h6',
                    title: 'Heading 6',
                    class: 'ck-heading_heading6'
                }
            ]
        },
        image: {
           toolbar: [
                'toggleImageCaption',
                'imageTextAlternative',
                '|',
                'imageStyle:inline',
                'imageStyle:wrapText',
                'imageStyle:breakText',
                '|',
                'resizeImage'
            ]
        },
        initialData: initialData,
        licenseKey: LICENSE_KEY,
        language: {
            ui: 'zh-cn',
            content: 'zh-cn'
        },
        link: {
            addTargetToExternalLinks: true,
            defaultProtocol: 'https://',
            decorators: {
                toggleDownloadable: {
                    mode: 'manual',
                    label: 'Downloadable',
                    attributes: {
                        download: 'file'
                    }
                }
            }
        },
        placeholder: '请输入内容',
        table: {
           contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
       },
       htmlSupport: {
           allow: [
               { name: /.*/, attributes: true, classes: true, styles: true }
           ]
       }
   };
}

let editorInstance = null;

// 处理 WPS 粘贴导致文字不可见的问题（比如粘贴过来的文本样式是 white 或透明）
function normalizeWpsHtml(html) {
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        // 移除 WPS/Office 特有的 mso-* 样式以及可能导致不可见的颜色
        const all = doc.body.querySelectorAll('*');
        all.forEach(el => {
            const style = el.getAttribute('style');
            if (!style) return;
            let newStyle = style;
            // 去除 mso- 前缀样式
            newStyle = newStyle
                .replace(/(^|;\s*)(mso-[^:]+:[^;]+;?)/gi, ';')
                .replace(/(^|;\s*)(tab-stops:[^;]+;?)/gi, ';');
            // 规范 color，避免白色或透明
            const colorMatch = newStyle.match(/color\s*:\s*([^;]+)\s*;?/i);
            if (colorMatch) {
                const color = colorMatch[1].trim().toLowerCase();
                if (color === '#fff' || color === '#ffffff' || color === 'white' ||
                    color === 'rgba(255,255,255,1)' || color === 'rgba(255, 255, 255, 1)' ||
                    color === 'rgb(255,255,255)' || color === 'rgb(255, 255, 255)' ||
                    color === 'transparent') {
                    newStyle = newStyle.replace(colorMatch[0], 'color: #000;');
                }
            }
            // 可能存在将字体颜色写成 background-clip 等影响显示的情况，强制修正
            const fgMatches = ['-webkit-text-fill-color', 'text-fill-color'];
            fgMatches.forEach(p => {
                const re = new RegExp(p + '\\s*:\\s*([^;]+)\\s*;?', 'i');
                const m = newStyle.match(re);
                if (m) {
                    const v = (m[1] || '').toLowerCase();
                    if (v.includes('white') || v.includes('#fff') || v.includes('#ffffff') || v.includes('transparent')) {
                        newStyle = newStyle.replace(re, p + ': #000;');
                    }
                }
            });

            // 清理多余分号
            newStyle = newStyle.replace(/;;+/g, ';').replace(/^;|;$/g, '');

            if (newStyle.trim()) el.setAttribute('style', newStyle);
            else el.removeAttribute('style');
        });

        return doc.body.innerHTML;
    } catch (_) {
        return html;
    }
}

let lastSavedContent = '';
let hasUnsavedChanges = false;

function markSaveButtonUnsaved() {
    const btn = document.getElementById('save-button');
    if (!btn) return;
    if (btn.disabled) return;
    btn.style.backgroundColor = 'red';
    btn.style.borderColor = 'red';
}

function resetSaveButtonState() {
    const btn = document.getElementById('save-button');
    if (!btn) return;
    btn.style.backgroundColor = '';
    btn.style.borderColor = '';
}

function setupSaveButtonDirtyTracking(editor) {
    try {
        // 初始化保存的内容为当前内容
        lastSavedContent = editor.getData();
        hasUnsavedChanges = false;

        editor.model.document.on('change:data', () => {
            // 仅根据编辑器内容变化提示
            const current = editor.getData();
            const changed = current !== lastSavedContent;
            if (changed && !hasUnsavedChanges) {
                hasUnsavedChanges = true;
                markSaveButtonUnsaved();
            } else if (!changed && hasUnsavedChanges) {
                hasUnsavedChanges = false;
                resetSaveButtonState();
            }
        });
    } catch (_) {
        // ignore
    }
}

// 创建编辑器的异步函数
async function createEditor() {
    try {
        // 确保 init_data 已经被设置
        if (!init_data) {
            throw new Error('初始数据未设置');
        }

        // 使用动态创建的配置
        const editorConfig = createEditorConfig(init_data);
        const editor = await DecoupledEditor.create(document.querySelector('#editor'), editorConfig);

        // 粘贴修复：拦截 WPS 的 HTML 数据并规范化
        const clipboardPipeline = editor.plugins.get('ClipboardPipeline');
        clipboardPipeline.on('inputTransformation', (evt, data) => {
            try {
                const html = data?.dataTransfer?.getData('text/html');
                if (!html) return;
                // WPS 常见标记：xmlns:wps、meta name="Generator" content="WPS"、w:*, o:*
                const isWps = /xmlns:\w+=\"urn:schemas-microsoft-com|wps\.|<meta[^>]+WPS|<\w+:[^>]+>/i.test(html);
                if (!isWps) return;
                const normalized = normalizeWpsHtml(html);
                if (normalized && normalized !== html) {
                    data.content = editor.data.htmlProcessor.toView(normalized);
                }
            } catch (_) { /* ignore */ }
        }, { priority: 'high' });

        document.querySelector('#editor-toolbar').appendChild(editor.ui.view.toolbar.element);
        editorInstance = editor;
        setupSaveButtonDirtyTracking(editor);

        // 设置编辑器自适应高度
        adjustEditorHeight();

        // 监听窗口大小变化
        window.addEventListener('resize', adjustEditorHeight);

        return editor;
    } catch (error) {
        if (typeof notifier !== 'undefined') {
            notifier.show('编辑器初始化失败', 'danger');
        }
        throw error;
    }
}

// 自适应高度调整函数
function adjustEditorHeight() {
    const editorElement = document.querySelector('.ck-editor__editable');
    if (editorElement) {
        // 计算可用高度
        const windowHeight = window.innerHeight;
        const topTitle = document.querySelector('.top-title');
        const formSection = document.querySelector('.form-section');
        const toolbar = document.querySelector('.editor-container__toolbar');
        const footer = document.querySelector('.editor-footer');

        let usedHeight = 0;
        if (topTitle) usedHeight += topTitle.offsetHeight;
        if (formSection) usedHeight += formSection.offsetHeight;
        if (toolbar) usedHeight += toolbar.offsetHeight;
        if (footer) usedHeight += footer.offsetHeight;

        // 预留更多边距空间，让编辑器不那么满
        const availableHeight = windowHeight - usedHeight - 200;

        // 设置最小高度
        const minHeight = Math.max(availableHeight, 250);
        editorElement.style.minHeight = minHeight + 'px';
    }
}

class TechBuyEditor {
    constructor() {
        this.init();
    }

    init() {
        this.setDate();
        this.bindEvents();
    }

    setDate() {
        let date = document.querySelector('#build-date');
        laydate.render({
            elem: date,
            showBottom: false,
        });
    }

    bindEvents() {
        // 表单按钮事件
        document.getElementById('save-button')?.addEventListener('click', () => {
            this.saveDocument();
        });

        document.getElementById('uppdf-button')?.addEventListener('click', (e) => {
            e.preventDefault();
            if (document.querySelector('#document-id').textContent.trim() == "新文档") {
                notifier.show('请先保存文档', 'danger');
                return;
            }
            document.querySelector('#pdf_upload').click();
        });

        document.querySelector('#pdf_upload')?.addEventListener('change', (e) => {
            this.uploadPDF();
        });
    }

    uploadPDF() {
        const fileInput = document.querySelector('#pdf_upload');
        const formData = new FormData();

        if (fileInput.files.length > 0) {
            formData.append('file', fileInput.files[0]);
        }

        // 自定义文件名：公司名称_规范名称_规范编号
        const company = (document.getElementById('customer')?.value || '').trim();
        const specName = (document.getElementById('spec-name')?.value || '').trim();
        const specNo = (document.getElementById('spec-number')?.value || '').trim();
        let name = `${company}_${specName}_${specNo}`.replace(/\s+/g, '_');
        fetch(`/pdf_save?name=${encodeURIComponent(name)}`, {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(url => {
                if (url != "-3") {
                    fetch('/pdf_save_sale', {
                        method: 'POST',
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            id: uuid,
                            pdf: url
                        })
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data != -1) {
                                document.getElementById('customer-pdf').innerHTML = `<div><a href="${url}">客户协议PDF</a></div>`;
                                notifier.show('PDF上传成功', 'success');
                            }
                            else {
                                notifier.show('PDF上传失败', 'danger');
                            }
                        });
                }
                else {
                    notifier.show('PDF上传失败', 'danger');
                }
            });
    }

    getFormData() {
        return {
            customer: document.getElementById('customer').getAttribute("data"),
            productType: document.getElementById('product-type').value,
            material: document.getElementById('material').value,
            specName: document.getElementById('spec-name').value,
            specNumber: document.getElementById('spec-number').value,
            buildDate: document.getElementById('build-date').value,
            content: editorInstance ? editorInstance.getData() : ''
        };
    }

    validateForm() {
        const data = this.getFormData();
        const errors = [];
        if (!data.customer) errors.push('请选择客户');
        if (!data.productType) errors.push('请选择产品类型');
        if (!data.material.trim()) errors.push('请输入牌号');
        if (!data.specName.trim()) errors.push('请输入规范名称');
        if (!data.specNumber.trim()) errors.push('请输入规范编号');
        if (!data.content.trim() || data.content === '<p><br></p>' || data.content === '') errors.push('请输入技术规范内容');

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    saveDocument() {
        const validation = this.validateForm();

        if (!validation.isValid) {
            notifier.show(validation.errors[0], 'danger');
            return;
        }

        const data = this.getFormData();
        data.id = uuid;

        // 这里应该调用后端API保存数据
        fetch(`/save_techsale`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    document.getElementById('document-id').textContent = uuid;
                    // 保存成功后重置保存按钮状态
                    if (editorInstance) {
                        lastSavedContent = editorInstance.getData();
                    }
                    hasUnsavedChanges = false;
                    resetSaveButtonState();
                    notifier.show('客户协议保存成功', 'success');
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    }
}

// 设置所有输入框为只读
function disableAllInputs() {
    // 禁用所有按钮
    document.getElementById('save-button').disabled = true;
    document.getElementById('uppdf-button').disabled = true;

    // 禁用所有表单输入
    document.getElementById('customer').disabled = true;
    document.getElementById('product-type').disabled = true;
    document.getElementById('material').disabled = true;
    document.getElementById('spec-name').disabled = true;
    document.getElementById('spec-number').disabled = true;
    document.getElementById('build-date').disabled = true;

    // 禁用编辑器
    if (editorInstance) {
        editorInstance.enableReadOnlyMode('editor-locked');

        // 隐藏工具栏
        const toolbar = document.querySelector('#editor-toolbar');
        if (toolbar) {
            toolbar.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function () {
    try {
        // 1. 初始化文档数据
        await initializeDocumentData();

        // 2. 创建TechBuyEditor实例
        const techBuyEditor = new TechBuyEditor();

        // 3. 确保编辑器容器存在且为空
        const editorContainer = document.querySelector('#editor');
        if (!editorContainer) {
            throw new Error('找不到编辑器容器');
        }

        // 清空容器内容
        editorContainer.innerHTML = '';

        // 4. 创建编辑器
        const editor = await DecoupledEditor.create(
           editorContainer,
           createEditorConfig(init_data || defaultTemplate)
       );

       // 粘贴修复：拦截 WPS 的 HTML 数据并规范化
       const clipboardPipeline = editor.plugins.get('ClipboardPipeline');
       clipboardPipeline.on('inputTransformation', (evt, data) => {
           try {
               const html = data?.dataTransfer?.getData('text/html');
               if (!html) return;
               const isWps = /xmlns:\w+=\"urn:schemas-microsoft-com|wps\.|<meta[^>]+WPS|<\w+:[^>]+>/i.test(html);
               if (!isWps) return;
               const normalized = normalizeWpsHtml(html);
               if (normalized && normalized !== html) {
                   data.content = editor.data.htmlProcessor.toView(normalized);
               }
           } catch (_) { /* ignore */ }
       }, { priority: 'high' });

        // 5. 设置工具栏
        const toolbar = document.querySelector('#editor-toolbar');
        if (toolbar) {
            toolbar.innerHTML = '';
            toolbar.appendChild(editor.ui.view.toolbar.element);
        }

        // 6. 保存编辑器实例
        editorInstance = editor;
        setupSaveButtonDirtyTracking(editor);

        // 7. 设置编辑器高度
        adjustEditorHeight();

        // 8. 初始化其他功能
        window.addEventListener('resize', adjustEditorHeight);
    } catch (error) {
        const editorContainer = document.querySelector('#editor');
        if (editorContainer) {
            editorContainer.innerHTML = `
                <div class="editor-error" style="
                    padding: 20px;
                    color: #721c24;
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    margin: 10px 0;
                ">
                    <h4>编辑器初始化失败</h4>
                    <p>${error.message || '发生未知错误'}</p>
                    <button onclick="window.location.reload()" class="btn btn-danger btn-sm mt-2">
                        刷新页面重试
                    </button>
                </div>
            `;
        }
        if (typeof notifier !== 'undefined') {
            notifier.show('编辑器初始化失败，请刷新重试', 'danger');
        }
    }
});

