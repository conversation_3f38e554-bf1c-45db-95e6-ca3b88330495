let page_customer = function () {
    let cus_cate = document.querySelector('#category').textContent;

    let global = {
        row_id: 0,
        edit: 0,
        eidt_cate: "",
    }

    let get_height = getHeight() - 198;
    let row_num = Math.floor(get_height / 30);

    let table_name = {
        name: cus_cate,
    };

    let table_fields;

    let init_data = {
        container: '.table-customer',
        url: `/fetch_customer`,
        post_data: {
            id: "",
            name: '',
            sort: "名称 ASC",
            rec: row_num,
            cate: cus_cate,
        },
        edit: false,
        row_fn: table_row,
    };

    fetch(`/fetch_fields`, {
        method: 'post',
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(table_name),
    })
        .then(response => response.json())
        .then(content => {
            if (content != -1) {
                table_fields = content[0].filter((item) => {
                    return item.is_show;
                });

                let table = document.querySelector('.table-customer');
                let custom_fields = [{ name: '序号', width: 3 }]
                let data = service.build_table_header(table, custom_fields, table_fields, "", "customers");
                table.querySelector('thead tr').innerHTML = data.th_row;

                init_data.header_names = data.header_names;

                tool_table.table_init(init_data);
                tool_table.fetch_table();
            }
        });

    function table_row(tr) {
        let rec = tr.split(SPLITER);
        let row = `<tr><td style="text-align: center;">${rec[1]}</td><td hidden>${rec[0]}</td>`;
        return service.build_row_from_string(rec, row, table_fields, 2);
    }

    //搜索客户供应商
    let search_input = document.querySelector('#search-input');

    let auto_comp = new AutoInput(search_input, document.querySelector('#category'), "customer_auto", () => {
        search_table();
    });

    auto_comp.init();

    modal_init();
    document.querySelector('#serach-button').addEventListener('click', function () {
        search_table();
    });

    if (document.querySelector('#cate-select')) {
        document.querySelector('#cate-select').addEventListener('change', function () {
            Object.assign(tool_table.table_data().post_data, { cate: cus_cate + "#" + this.value, name: '', page: 1 });
            tool_table.fetch_table();
        });
    }

    function search_table() {
        let search = document.querySelector('#search-input').value;
        Object.assign(tool_table.table_data().post_data, { name: search, page: 1 });
        tool_table.fetch_table();
    }

    //增加按键
    document.querySelector('#add-button').addEventListener('click', function () {
        global.eidt_cate = "add";

        document.querySelector('.modal-body').innerHTML = service.build_add_form(table_fields);

        document.querySelector('.modal-title').textContent = "增加" + cus_cate;
        document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;";

        document.querySelector('.modal').style.display = "block";
        document.querySelector('.modal-body input').focus();

        // submit_func();
        leave_alert();
    });

    //编辑按键
    document.querySelector('#edit-button').addEventListener('click', function () {
        global.eidt_cate = "edit";

        let chosed = document.querySelector('tbody .focus');
        let id = chosed ? chosed.querySelector('td:nth-child(2)').textContent : "";
        if (id != "") {
            global.row_id = id;

            document.querySelector('.modal-body').innerHTML = service.build_edit_form(3, table_fields, chosed); //3 是起始位置

            document.querySelector('.modal-title').textContent = "编辑" + cus_cate;
            document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;";
            document.querySelector('.modal').style.display = "block";
            document.querySelector('.modal-body input').focus();

            if (document.querySelector('#category').textContent.trim() == "客户") {
                document.querySelector('.modal-body .收货人').disabled = true;
                document.querySelector('.modal-body .收货电话').disabled = true;
                document.querySelector('.modal-body .收货地址').disabled = true;
            }
            // submit_func();
            leave_alert();
        } else {
            notifier.show('请先选择' + cus_cate, 'danger');
        }
    });

    //编辑收货地址
    if (document.querySelector('#address-button')) {
        document.querySelector('#address-button').addEventListener('click', function () {
            global.eidt_cate = "edit";

            let chosed = document.querySelector('tbody .focus');
            let id = chosed ? chosed.querySelector('td:nth-child(2)').textContent : "";
            const name = chosed ? chosed.querySelector('td:nth-child(3)').textContent : "";
            if (id != "") {
                global.row_id = id;
                global.name = name;
                fetch_address(id, name);
            } else {
                notifier.show('请先选择' + cus_cate, 'danger');
            }
        })
    }

    function fetch_address(id, name) {
        fetch("/get_customer_address", {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: id,
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    let trs = "";
                    let num = 1;

                    for (let tr of content) {
                        trs += `<tr><td hidden>${tr.id}</td><td>${num++}</td><td>${tr.address}</td><td>${tr.name}</td>
                                <td>${tr.phone}</td><td>${tr.default ? "是" : ""}</td></tr>`;
                    }

                    let html = `
                    <div class="table-container table-address">
                            <table>
                                <thead>
                                    <tr><th width="7%">序号</th><th width="57%">收货地址</th><th width="11%">收货人</th><th width="15%">收货电话</th>
                                    <th width="10%">设为默认</th></tr>
                                </thead>
                                <tbody>
                                    ${trs}
                                </tbody>
                            </table>                        
                        </div>
                    `;
                    document.querySelector('.modal-body').innerHTML = html;
                    document.querySelector('.modal-body').style.cssText = "height:300px";
                    document.querySelector('.modal-title').textContent = `${name} - 收货信息`;
                    document.querySelector('.modal-dialog').style.cssText = "max-width: 800px;margin-top: 200px;";
                    document.querySelector('.modal').style.display = "block";
                    document.querySelector('#modal-buttons').style.display = "none";
                    document.querySelector('#address-buttons').style.display = "flex";
                    document.querySelector('#address-buttons-add').style.display = "none";
                    document.querySelector('#address-buttons-edit').style.display = "none";

                    document.querySelectorAll(".table-address tbody tr").forEach(tr => {
                        tr.addEventListener('click', function () {
                            document.querySelector('.modal-focus')?.classList.remove('modal-focus');
                            this.classList.add('modal-focus');
                        });
                    });

                    // address_func();
                    modal_init();
                    leave_alert();
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    }

    //重置密码
    if (document.querySelector('#reset-button')) {
        document.querySelector('#reset-button').addEventListener('click', function () {
            let chosed = document.querySelector('tbody .focus');
            let id = chosed ? chosed.querySelector('td:nth-child(2)').textContent : "";
            const name = chosed ? chosed.querySelector('td:nth-child(3)').textContent : "";
            if (id != "") {
                alert_confirm(`确认重置 ${name} 的密码吗？`, {
                    confirmCallBack: () => {
                        fetch("/reset_customer_password", {
                            method: 'post',
                            headers: {
                                "Content-Type": "application/json",
                            },
                            body: id,
                        })
                            .then(response => response.json())
                            .then(content => {
                                if (content == 1) {
                                    notifier.show('密码重置成功', 'success');
                                } else {
                                    notifier.show('操作失败', 'danger');
                                }
                            });
                    }
                });

            } else {
                notifier.show('请先选择客户', 'danger');
            }
        })
    }

    document.querySelector('#modal-add-button').onclick = function () {
        const tr = `
                <tr><td width="7%"></td>
                    <td width="57%"><input class='form-control input-sm' type="text" style="width: 90%;"></td>
                    <td width="11%"><input class='form-control input-sm' type="text" style="width: 80%;"></td>
                    <td width="15%"><input class='form-control input-sm' type="text" style="width: 80%;"></td>
                    <td width="10%"><label class="check-radio"><input type="checkbox">
                        <span class="checkmark"></span></label>
                    </td>
                </tr>`;
        document.querySelector('.table-address tbody').insertAdjacentHTML('beforeend', tr);
        document.querySelector('#address-buttons').style.display = "none";
        document.querySelector('#address-buttons-add').style.display = "flex";

        document.querySelector('.table-address tbody tr:last-child').querySelector('td:nth-child(2) input').focus();

        document.querySelector('#modal-saveadd-button').onclick = function () {
            const tr = document.querySelector('.table-address tbody tr:last-child');
            const address = tr.querySelector('td:nth-child(2) input').value.trim();
            const name = tr.querySelector('td:nth-child(3) input').value.trim();
            const phone = tr.querySelector('td:nth-child(4) input').value.trim();
            const dfault = tr.querySelector('td:nth-child(5) input').checked;
            if (address == "") {
                notifier.show('收货地址不能为空', 'danger');
                return false;
            } else {
                fetch("/change_customer_address", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ cate: 'add', cusid: Number(global.row_id), address: address, name: name, phone: phone, default: dfault }),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            fetch_address(global.row_id, global.name);
                            notifier.show('操作成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                    });
            }
        };
    };

    document.querySelector('#modal-addcancel-button').onclick = function () {
        fetch_address(global.row_id, global.name);
    };

    document.querySelector('#modal-edit-button').onclick = function () {
        const trs = document.querySelectorAll('.table-address tbody tr');
        if (trs.length > 1 && !document.querySelector('.table-address tbody tr.modal-focus')) {
            notifier.show('请先选择信息条目', 'danger');
            return false;
        }

        if (trs.length == 1) {
            trs[0].classList.add('modal-focus');
        }

        const tr = document.querySelector('.table-address tbody tr.modal-focus');
        const address = tr.querySelector('td:nth-child(3)').textContent.trim();
        const name = tr.querySelector('td:nth-child(4)').textContent.trim();
        const phone = tr.querySelector('td:nth-child(5)').textContent.trim();
        const df = tr.querySelector('td:nth-child(6)').textContent.trim();
        const dfault = df == "是" ? true : false;

        tr.querySelector('td:nth-child(3)').innerHTML = `<input class='form-control input-sm' type="text" style="width: 90%;" value="${address}">`;
        tr.querySelector('td:nth-child(4)').innerHTML = `<input class='form-control input-sm' type="text" style="width: 80%;" value="${name}">`;
        tr.querySelector('td:nth-child(5)').innerHTML = `<input class='form-control input-sm' type="text" style="width: 80%;" value="${phone}">`;
        tr.querySelector('td:nth-child(6)').innerHTML = `<label class="check-radio"><input type="checkbox" ${dfault ? "checked" : ""}>
                        <span class="checkmark"></span></label>`;

        document.querySelector('#address-buttons').style.display = "none";
        document.querySelector('#address-buttons-edit').style.display = "flex";

        tr.querySelector('td:nth-child(3) input').focus();

        document.querySelector('#modal-saveedit-button').onclick = function () {
            const tr = document.querySelector('.table-address tbody tr.modal-focus');
            const id = tr.querySelector('td:first-child').textContent;
            const address = tr.querySelector('td:nth-child(3) input').value.trim();
            const name = tr.querySelector('td:nth-child(4) input').value.trim();
            const phone = tr.querySelector('td:nth-child(5) input').value.trim();
            const dfault = tr.querySelector('td:nth-child(6) input').checked;
            if (address == "") {
                notifier.show('收货地址不能为空', 'danger');
                return false;
            } else {
                fetch("/change_customer_address", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ cate: "edit", id: id, cusid: Number(global.row_id), address: address, name: name, phone: phone, default: dfault }),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            fetch_address(global.row_id, global.name);
                            notifier.show('操作成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                    });
            }
        };
    };


    document.querySelector('#modal-editcancel-button').onclick = function () {
        fetch_address(global.row_id, global.name);
    };

    document.querySelector('#modal-del-button').onclick = function () {
        if (!document.querySelector('.table-address tbody tr.modal-focus')) {
            notifier.show('请先选择信息条目', 'danger');
            return false;
        }
        const tr = document.querySelector('.table-address tbody tr.modal-focus');
        const id = tr.querySelector('td:first-child').textContent;
        alert_confirm('确认删除吗？', {
            confirmCallBack: () => {
                fetch("/del_customer_address", {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: id,
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            fetch_address(global.row_id, global.name);
                            notifier.show('操作成功', 'success');
                        } else {
                            notifier.show('操作失败', 'danger');
                        }
                    });
            }
        });
    };

    document.querySelector('#address-close-button').onclick = function () {
        close_modal();
    };

    //提交按键
    document.querySelector('#modal-sumit-button').onclick = function () {
        if (global.eidt_cate == "add" || global.eidt_cate == "edit") {
            let all_input = document.querySelectorAll('.has-value');
            let num = 0;
            for (let input of all_input) {
                if (input.value.trim() == '' && input.parentNode.querySelector('label').textContent != "备注" &&
                    input.parentNode.querySelector('label').textContent != "行号") {
                    notifier.show('除了行号和备注外，不能为空', 'danger');
                    return false;
                }

                if (table_fields[num].data_type == "整数" && !regInt.test(input.value)
                    || table_fields[num].data_type == "实数" && !regReal.test(input.value)) {
                    notifier.show('数字字段输入错误', 'danger');
                    return false;
                }
                num++;
            }
            let customer = `${global.row_id}${SPLITER}${global.row_id}${SPLITER}`;

            let n = 0;
            for (let input of all_input) {
                let value;
                if (table_fields[n].data_type != "布尔") {
                    value = input.value;
                } else {
                    let v = table_fields[n].option_value.split("_");
                    value = input.checked ? v[0] : v[1];
                }

                customer += `${value}${SPLITER}`;
                n++;
            }

            let data = {
                data: customer,
                cate: cus_cate,
            };

            let url = global.eidt_cate == "edit" ? `/update_customer` : `/add_customer`;

            fetch(url, {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            })
                .then(response => response.json())
                .then(content => {
                    if (content != -1) {
                        global.edit = 0;
                        modal_out_data.edit = 0;
                        notifier.show(cus_cate + '操作成功', 'success');
                        tool_table.fetch_table();

                        if (document.querySelector('#category').textContent.trim() == "客户" && global.eidt_cate == "add") {
                            const name = document.querySelector('.modal-body .收货人').value.trim();
                            const phone = document.querySelector('.modal-body .收货电话').value.trim();
                            const address = document.querySelector('.modal-body .收货地址').value.trim();
                            fetch("/change_customer_address", {
                                method: 'post',
                                headers: {
                                    "Content-Type": "application/json",
                                },
                                body: JSON.stringify({
                                    cate: "add", cusid: Number(content), address: address, name: name, phone: phone, default: true
                                }),
                            });
                        }

                        close_modal();
                    } else {
                        notifier.show('权限不够，操作失败', 'danger');
                    }
                });

        } else {
            let url = global.eidt_cate == "批量导入" ? `/customer_addin` : `/customer_updatein`;
            fetch(url, {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ cate: cus_cate }),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        notifier.show('批量操作成功', 'success');
                        tool_table.fetch_table();
                        close_modal();
                    } else {
                        notifier.show('权限不够，操作失败', 'danger');
                    }
                });
        }
    };

    //数据导入和导出 ------------------------------------------------------------------------------

    document.querySelector('#data-out').addEventListener('click', function () {
        fetch(`/customer_out`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ cate: cus_cate }),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    download_file(`/download/${content}.xlsx`);
                    notifier.show('成功导出至 Excel 文件', 'success');
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    });

    //批量导入
    let fileBtn = document.getElementById('choose_file');
    document.getElementById('data-in').addEventListener('click', function () {
        fileBtn.click();
    });
    fileBtn.addEventListener('change', () => {
        data_in(fileBtn, "将追加", "追加新数据，同时保留原数据", "批量导入");
    });
    //批量更新
    let fileBtn_update = document.getElementById('choose_file2');
    document.getElementById('data-update').addEventListener('click', function () {
        fileBtn_update.click();
    });
    fileBtn_update.addEventListener('change', () => {
        data_in(fileBtn_update, "将更新", "更新数据，原数据将被替换，请谨慎操作！", "批量更新");
    });

    function data_in(fileBtn, info1, info2, cate) {
        if (checkFileType(fileBtn)) {
            const fd = new FormData();
            fd.append('file', fileBtn.files[0]);
            let url = cus_cate == "客户" ? `/customer_in` : `/supplier_in`;

            fetch(url, {
                method: 'POST',
                body: fd,
            })
                .then(res => res.json())
                .then(content => {
                    if (content != -1 && content != -2) {
                        let rows = "<div class='table-container table-customer'><table style='font-size: 12px;'><thead>";
                        let n = 1;
                        for (let item of content[0]) {
                            let arr_p = item.split(SPLITER);
                            let row = `<tr>`;
                            if (n == 1) {
                                for (let i = 0; i < arr_p.length - 1; i++) {
                                    row += `<th>${arr_p[i]}</th}>`;
                                }
                                row += "</tr></thead><tbody>";
                                n = 2;
                            } else {
                                for (let i = 0; i < arr_p.length - 1; i++) {
                                    row += `<td>${arr_p[i]}</td>`;
                                }
                                row += "</tr>";
                            }

                            rows += row;
                        }
                        rows += "</tbody></table></div>";
                        document.querySelector('.modal-body').innerHTML = rows;

                        let message = content[1] > 50 ? " (仅显示前 50 条）" : "";
                        document.querySelector('.modal-title').innerHTML = `${cate}信息${info1} ${content[1]} 条数据${message}：`;
                        document.querySelector('#modal-info').innerHTML = `${cate}信息${info2}`;

                        global.eidt_cate = cate;

                        document.querySelector('.modal-dialog').style.cssText = "max-width: 1200px;";
                        document.querySelector('.modal').style.cssText = "display: block";
                        fileBtn.value = "";

                    } else if (content == -1) {
                        notifier.show('缺少操作权限', 'danger');
                    } else {
                        notifier.show('excel 表列数不符合', 'danger');
                    }
                });
        } else {
            notifier.show('需要 excel 文件', 'danger');
        }
    }
}();
