let page_material = function () {
    let document_table_fields, table_lines, show_names, edited;
    let document_bz = document.querySelector('#document-bz').textContent.trim();
    let dh_div = document.querySelector('#dh');

    //单据顶部信息构造显示，并添加事件处理 -----------------------------------------------------------
    let document_name = "入库单据";

    //获取单据表头部分的字段（字段设置中的右表内容）
    fetch(`/fetch_inout_fields`, {
        method: 'post',
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(document_name),
    })
        .then(response => response.json())
        .then(content => {
            if (content != -1) {
                document_table_fields = content;
                if (dh_div.textContent != "新单据") {
                    fetch(`/fetch_document_rkd`, {
                        method: 'post',
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            cate: document_name,
                            dh: dh_div.textContent,
                        }),
                    })
                        .then(response => response.json())
                        .then(data => {
                            let html = service.build_inout_form(document_table_fields, data);
                            document_top_handle(html, true);
                            let dh = `${document.querySelector("#文本字段6").value}#${dh_div.textContent}`;
                            build_items(dh);

                            let real_1 = document.querySelector('#实数字段1');
                            real_1.value = Number(real_1.value).toFixed(1);
                            real_1 = document.querySelector('#实数字段2');
                            real_1.value = Number(real_1.value).toFixed(1);
                            real_1 = document.querySelector('#实数字段3');
                            real_1.value = Number(real_1.value).toFixed(1);

                            let pic = data.图片.replace("pic_", "min_");
                            if (pic.startsWith("/upload")) {
                                document.querySelector('#upload-pic').setAttribute('src', `${pic}?${Math.random()}`);
                            }

                            document.querySelector('#文本字段6').disabled = true;

                            let set_data = {
                                content: data,
                                readonly_fun: set_readonly,
                            }
                            service.set_shens_owner(set_data);
                            service.fei_readonly(data.作废, "buy-content");
                        });
                } else {
                    let html = service.build_inout_form(content);
                    document_top_handle(html, false);
                    document.querySelector('#remember-button').textContent = '审核';
                    setTimeout(() => {
                        document.querySelector('#文本字段6').disabled = true;
                    }, 200);
                }
            }
        });

    let standart = document.querySelector('#执行标准');
    let auto_comp = new AutoInput(standart, "执行标准", "/get_status_auto");
    auto_comp.init();

    function set_readonly() {
        let all_edit = document.querySelectorAll('.fields-show input');
        for (let edit of all_edit) {
            if (edit.id == "备注") {
                continue;
            }
            edit.disabled = true;
        }
        document.querySelector('#material-add').setAttribute("disabled", true);
        document.querySelector('#pic-button').setAttribute("disabled", true);

        setTimeout(() => {
            document.querySelectorAll('.table-items tbody input').forEach((input) => {
                input.disabled = true;
            });

            document.querySelectorAll('.table-items tbody select').forEach((input) => {
                input.disabled = true;
            });
        }, 100);

        service.edit_button_disabled();
    }

    function document_top_handle(html, has_date) {
        let fields_show = document.querySelector('.fields-show .table-head');
        fields_show.innerHTML = html;

        let date = document.querySelector('#日期');

        if (!has_date) {
            date.value = new Date().Format("yyyy-MM-dd");
        }

        //执行一个laydate实例
        laydate.render({
            elem: date,
            showBottom: false,
        });

        if (document.querySelector('#文本字段5')) {
            let da = document.querySelector('#文本字段5');
            laydate.render({
                elem: da,
                showBottom: false,
            })
        }

        // 回车和方向键的移动控制
        let all_input = document.querySelectorAll('.fields-show input');
        let form = document.querySelector('.fields-show');
        set_key_move(all_input, form, 7);
        service.set_sumit_shen();

        //提交审核
        document.querySelector('#sumit-shen').addEventListener('click', function () {
            let shen_data = {
                button: this,
                dh: dh_div.textContent,
                document_name: document_name,
                edited: edited || edit_table.input_table_outdata().edited,
            }
            service.sumit_shen(shen_data);
        });
    }

    service.get_materials_docs("/materialin_docs", "材料采购", build_items);

    fetch('/materialin_saved_docs', {
        method: 'post',
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify("材料采购"),
    })
        .then(response => response.json())
        .then(content => {
            let title = document.querySelector(".table-save thead th");
            title.innerHTML = title.textContent + " " + content.length + " 单";

            let tr = "";
            content.forEach(obj => {
                tr += `<tr><td>${obj.label}</td><td hidden>${obj.id}</td></tr>`;
            });

            document.querySelector(".table-save tbody").innerHTML = tr;

            let lines = document.querySelectorAll(".table-save tbody tr");
            for (let l of lines) {
                l.addEventListener("dblclick", () => {
                    // if (document.querySelector('#remember-button').textContent == "已审核" ||
                    //     document.querySelector('#save-button').disabled == true) {
                    //     return false;
                    // }
                    let dh = l.querySelector('td:nth-child(2)').textContent.trim();
                    window.location.href = "/material_in/" + dh;
                });
            }
        });

    // 点击上传炉批号质保书
    let lu_upload = document.querySelector('#lu_upload');
    document.querySelector('#lu_button').addEventListener('click', (e) => {
        let lu = document.querySelector('#炉批号').value.trim();
        if (lu == "") {
            notifier.show('请先输入炉批号', 'danger');
            return false;
        }

        e.preventDefault();
        lu_upload.click();
    });

    //上传 pdf 文件
    lu_upload.addEventListener('change', () => {
        let lu_btn = document.querySelector('#lu_button');
        let lh = `${document.querySelector('#材质').value.trim()}_${document.querySelector('#规格').value.trim()}_${document.querySelector('#炉批号').value.trim()}__${document.querySelector('#生产厂家').value.trim()}`;
        lu_btn.disabled = true;
        const fd = new FormData();
        fd.append('file', lu_upload.files[0]);
        fetch(`/pdf_in`, {
            method: 'POST',
            body: fd,
        })
            .then(res => res.json())
            .then(content => {
                if (content == -3) {
                    notifier.show('文件大小不能超过1M', 'danger');
                    lu_button.disabled = "";
                    return false;
                }

                fetch(`/pdf_in_save`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(lh)
                })
                    .then(response => response.json())
                    .then(content => {
                        document.querySelector('#lu_id').textContent = content;
                        lu_btn.classList.add('remembered');
                        notifier.show('质保书成功保存', 'success');
                        lu_btn.disabled = "";
                    });
            });
    });

    function build_items(dh) {
        fetch('/get_items', {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(dh),
        })
            .then(response => response.json())
            .then(content => {
                let tr = "";
                content.forEach(obj => {
                    let material = obj.split(`${SPLITER}`);
                    let done = material[3] == 'true' ? "class='red'" : "";
                    tr += `<tr ${done}><td hidden>${material[0]}</td><td>${material[1]}</td>
                    <td hidden>${material[2]}</td></tr>`;
                });

                document.querySelector(".table-history tbody").innerHTML = tr;

                let lines = document.querySelectorAll(".table-history tbody tr");
                for (let l of lines) {
                    l.addEventListener("dblclick", () => {
                        if (document.querySelector('#remember-button').textContent == '已审核' ||
                            document.querySelector('#save-button').disabled == true) {
                            return false;
                        }
                        document.querySelector('#m_id').value = l.querySelector('td:nth-child(1)').textContent;
                        document.querySelector('#d_id').value = l.querySelector('td:nth-child(3)').textContent;

                        let na = l.querySelector('td:nth-child(2)').textContent.split('　');
                        document.querySelector('#名称').value = na[0];
                        document.querySelector('#材质').value = na[1];
                        document.querySelector('#规格').value = na[2];
                        document.querySelector('#状态').value = na[3];
                        document.querySelector('#生产厂家').value = na[4];
                        document.querySelector('#执行标准').value = na[5];
                        document.querySelector('#炉批号').focus();
                        // document.querySelector("#文本字段6").click();
                    })
                }
            });
    }

    //构建商品规格表字段，字段设置中的右表数据 --------------------------

    show_names = {
        "序号": { width: 10, class: "序号", type: "普通输入", editable: false, is_save: true, default: "" },
        "名称": { width: 40, class: "名称", type: "普通输入", editable: false, is_save: false, default: "" },
        "材质": { width: 60, class: "材质", type: "普通输入", editable: false, is_save: false, default: "" },
        "规格": { width: 50, class: "规格", type: "普通输入", editable: false, is_save: true, default: "" },
        "状态": { width: 80, class: "状态", type: "普通输入", editable: false, is_save: true, default: "" },
        "炉批号": { width: 100, class: "炉批号", type: "普通输入", editable: false, is_save: true, default: "" },
        "执行标准": { width: 120, class: "执行标准", type: "普通输入", editable: false, is_save: true, default: "" },
        "生产厂家": { width: 70, class: "生产厂家", type: "普通输入", editable: false, is_save: true, default: "" },
        "物料号": { width: 60, class: "物料号", type: "普通输入", editable: true, is_save: true, default: "" },
        "长度": { width: 30, class: "长度", type: "普通输入", editable: true, is_save: true, default: "" },
        "重量": { width: 30, class: "重量", type: "普通输入", editable: false, is_save: true, default: "" },
        "外径壁厚": { width: 30, class: "外径壁厚", type: "普通输入", editable: true, is_save: true, default: "" },
        "库存类别": {
            width: 50, class: "库存类别", type: "下拉列表", value: '销售', editable: true, is_save: true, default: "销售_自用_不合格"
        },
        "备注": {
            width: 90, class: "备注", type: "普通输入", editable: true, is_save: true, default: "",
            css: 'style="border-right:none"'
        },
        "m_id": {
            width: 0, class: "m_id", type: "普通输入", editable: false, is_save: true, default: "",
            css: 'style="width:0%; border-left:none; border-right:none; color:white"',
        },
        "d_id": {
            width: 0, class: "d_id", type: "普通输入", editable: false, is_save: true,
            css: 'style="width:0%; border-left:none; border-right:none; color:white"',
        },
        "lu_id": {
            width: 0, class: "lu_id", type: "普通输入", editable: false, is_save: true,
            css: 'style="width:0%; border-left:none; color:white"',
        },
    };

    //计算表格行数，33 为 lineHeight （行高）
    table_lines = Math.floor((document.querySelector('body').clientHeight - 395) / 33);

    if (dh_div.textContent == "新单据") {
        let data = {
            show_names: show_names,
            lines: table_lines,
            dh: dh_div.textContent,
            document: document_name,
            calc_func: get_weight,
            // del_func: sum_weight,  //删除表格行时, 需重算合计重量, 在这里运行回调函数
        }

        edit_table.build_blank_table(data);
    } else {
        fetch("/fetch_document_items_rk", {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                cate: document_name,
                dh: dh_div.textContent,
            }),
        })
            .then(response => response.json())
            .then(content => {
                let data = {
                    show_names: show_names,
                    rows: content,
                    lines: table_lines,
                    dh: dh_div.textContent,
                    document: document_name,
                    calc_func: get_weight,
                    // del_func: sum_weight,  //删除表格行时, 需重算合计重量, 在这里运行回调函数
                }
                edit_table.build_items_table(data);
            });
    }

    function get_weight(input_row) {
        input_row.querySelector('.长度').onblur = function () {
            weight(input_row);
            // sum_weight();
        };
    }

    // 理论重量计算
    function weight(input_row) {
        let data = {
            long: input_row.querySelector('.长度').value.trim(),
            num: 1,
            name: input_row.querySelector('.名称').textContent.trim(),
            cz: input_row.querySelector('.材质').textContent.trim(),
            gg: input_row.querySelector('.规格').textContent.trim(),
        }

        if (regInt.test(data.long) && regInt.test(data.num)) {
            input_row.querySelector('.重量').textContent = service.calc_weight(data);
        } else {
            input_row.querySelector('.重量').textContent = 0;
        }
    }

    //计算合计理论重量
    function sum_weight() {
        let all_input = document.querySelectorAll('.has-input');
        let sum = 0;
        for (let i = 0; i < all_input.length; i++) {
            let wtEl = all_input[i].querySelector('.重量');
            if (!wtEl) continue;
            let txt = wtEl.textContent.trim();
            if (!txt) continue;
            // allow numbers with comma thousands separators
            let val = parseFloat(txt.replace(/,/g, ''));
            if (isNaN(val)) continue;
            sum += val;
        }

        let out = document.querySelector('#实数字段3');
        if (out) {
            out.value = sum.toFixed(1);
        }
    }

    // 加入表单
    document.querySelector("#material-add").addEventListener('click', function () {
        if (!document.querySelector('#名称').value) {
            notifier.show('采购条目输入有错误', 'danger');
            return false;
        }

        let n = document.querySelector("#数量").value;
        if (!n || !regInt.test(n) || n <= 0) {
            notifier.show('数量输入有错误', 'danger');
            return false;
        }

        fetch(`/fetch_max_num`, {
            method: 'get',
        })
            .then(response => response.json())
            .then(content => {
                //在表内寻找最大值
                let max_num = content;
                let nums = document.querySelectorAll('.table-items .has-input .物料号');
                nums.forEach(num => {
                    let v = Number(num.value.replace('M', ''));
                    if (max_num < v) {
                        max_num = v;
                    }
                });

                show_names.名称.value = document.querySelector('#名称').value;
                show_names.材质.value = document.querySelector('#材质').value;
                show_names.规格.value = document.querySelector('#规格').value;
                show_names.状态.value = document.querySelector('#状态').value;
                show_names.炉批号.value = document.querySelector('#炉批号').value;
                show_names.执行标准.value = document.querySelector('#执行标准').value;
                show_names.生产厂家.value = document.querySelector('#生产厂家').value;
                show_names.m_id.value = document.querySelector('#m_id').value;
                show_names.d_id.value = document.querySelector('#d_id').value;
                show_names.lu_id.value = document.querySelector('#lu_id').textContent;

                let data = {
                    show_names: show_names,
                    show_names_fn: function (n) {
                        this.show_names.物料号.value = `M${padZero(this.material_num + n + 1, 6)}`;
                    },
                    num: n,
                    lines: table_lines,
                    dh: dh_div.textContent,
                    document: document_name,
                    material_num: max_num,
                }

                edit_table.build_out_table(data);
                edited = 1;

                document.querySelector('#名称').value = '';
                document.querySelector('#材质').value = '';
                document.querySelector('#规格').value = '';
                document.querySelector('#状态').value = '';
                document.querySelector('#炉批号').value = '';
                document.querySelector('#执行标准').value = '';
                document.querySelector('#生产厂家').value = '';
                document.querySelector('#m_id').value = '';
                document.querySelector('#d_id').value = '';
                document.querySelector('#lu_id').textContent = '';
            });
    });
    // 图片处理 -----------------------------------------------------------------
    service.handle_pic(dh_div, "/pic_in_save");
    modal_init();

    //保存、打印、质检、审核 -------------------------------------------------------------------

    //保存
    document.querySelector('#save-button').addEventListener('click', function () {
        //错误勘察
        if (!error_check()) {
            return false;
        }

        service.confirm_save(save_data);
    });

    function save_data() {        
        sum_weight();
        
        let all_values = document.querySelectorAll('.document-value');

        //构建表头存储字符串，将存入单据中
        let save_str = `${document_bz}${SPLITER}${dh_div.textContent}${SPLITER}`;

        let n = 0;
        for (let f of document_table_fields) {
            if (f.data_type == "文本") {
                let value = f.show_name.indexOf("单号") == -1 ? all_values[n].value : all_values[n].value.split('　')[0];
                save_str += `${value}${SPLITER}`;
            } else if (f.data_type == "整数" || f.data_type == "实数") {
                let value = all_values[n].value ? all_values[n].value : 0;
                save_str += `${value}${SPLITER}`;
            } else {
                save_str += `${all_values[n].checked ? "是" : "否"}${SPLITER}`;
            }
            n++;
        }

        // 构建字符串数组，将存入单据明细中
        let table_data = [];
        let all_rows = document.querySelectorAll('.table-items .has-input');
        for (let row of all_rows) {
            if (row.querySelector('td:nth-child(1)').textContent != "") {
                let save_str = '';
                save_str += service.build_save_items("序号", row, show_names);
                table_data.push(save_str);
            }
        }

        let data = {
            rights: document_bz,
            document: save_str,
            remember: document.querySelector('#remember-button').textContent,
            items: table_data,
        }

        // console.log(data);

        fetch(`/save_material`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    dh_div.textContent = content;
                    notifier.show('单据保存成功', 'success');
                    edited = false;
                    edit_table.input_table_outdata().edited = false;
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    }

    //打印
    document.querySelector('#print-button').addEventListener('click', function () {
        //错误勘察
        if (!error_check()) {
            return false;
        }

        document.querySelector('#print .print-title').textContent = "五星（天津）石油装备有限公司-原材料入库单";
        document.querySelector('#p-block1').innerHTML = `<p> 单号：${document.querySelector('#dh').textContent}</p>`;
        document.querySelector('#p-block2').innerHTML = `<p>日期：${document.querySelector('#日期').value}</p>`;

        var th = `<tr class="print-table-head">
                    <th width="3%">序号</th>
                    <th width="5%">物料号</th>
                    <th width="5%">材质</th>
                    <th width="7%">规格</th>
                    <th width="7%">状态</th>
                    <th width="6%">炉批号</th>
                    <th width="4%">长度</th>
                    <th width="3%">支数</th>
                    <th width="9%">执行标准</th>
                    <th width="6%">生产厂家</th>
                    <th width="6%">重量</th>
                </tr>`;

        document.querySelector('.print-table thead').innerHTML = th;

        let sum_weight = 0, sum_long = 0;

        let all_rows = document.querySelectorAll('.table-items .has-input');
        let trs = '';
        for (let row of all_rows) {
            trs += `<tr><td>${row.querySelector('.序号').textContent}</td>
                <td>${row.querySelector('.物料号').value}</td>
                <td>${row.querySelector('.材质').textContent}</td>
                <td>${row.querySelector('.规格').textContent}</td>
                <td>${row.querySelector('.状态').textContent}</td>
                <td>${row.querySelector('.炉批号').textContent}</td>
                <td>${row.querySelector('.长度').value == '0' ? "" : row.querySelector('.长度').value}</td>
                <td>1</td>
                <td>${row.querySelector('.执行标准').textContent}</td>
                <td>${row.querySelector('.生产厂家').textContent}</td>
                <td>${Number(row.querySelector('.重量').textContent) == 0 ? "" : row.querySelector('.重量').textContent}</td> `;

            trs += '</tr>';

            sum_weight += Number(row.querySelector(`.重量`).textContent);
            sum_long += Number(row.querySelector(`.长度`).value);
        }

        // 补空行
        let len = 9 - all_rows.length;
        trs += append_blanks(len, 11);

        trs += `<tr style="height: 50px"><td colspan="6"></td><td>${sum_long == 0 ? "" : sum_long}</td><td>${all_rows.length}</td>
            <td style="white-space: normal">来料重量：<br> ${document.querySelector('#实数字段1').value}</td>
            <td>实际重量：<br> ${document.querySelector('#实数字段2').value}</td><td>理论重量：<br> ${sum_weight == 0 ? "" : sum_weight.toFixed(1)}</td>`;

        document.querySelector('.print-table tbody').innerHTML = trs;

        document.querySelector('#p-block5').innerHTML = '<p>采购：</p>';
        document.querySelector('#p-block6').innerHTML = '<p>财务：</p>';
        document.querySelector('#p-block7').innerHTML = '<p>质检：</p>';
        document.querySelector('#p-block8').innerHTML = '<p>仓库：</p>';

        document.querySelector('#print').hidden = false;
        Print('#print', {});
        document.querySelector('#print').hidden = true;
    });

    //审核单据
    document.querySelector('#remember-button').addEventListener('click', function () {
        let formal_data = {
            button: this,
            dh: dh_div.textContent,
            document_name: document_name,
            edited: edited || edit_table.input_table_outdata().edited,
            readonly_fun: set_readonly,
            xsdh: document.querySelector('#文本字段6').value,    //销售单号，用于确认出库完成
            after_func: make_complete  //审核后，确认入库完成
        }
        service.make_formal(formal_data);
    });

    //审核时，将对采购单做入库完成的确认
    function make_complete(dh) {
        //与销售单共用
        fetch(`/make_rk_complete`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(dh),
        });
    }

    //共用事件和函数 ---------------------------------------------------------------------

    //保存、打印和审核前的错误检查
    function error_check() {
        let all_rows = document.querySelectorAll('.table-items .has-input');
        //检查表头的错误
        if (!service.header_error_check(document_table_fields, all_rows)) {
            return false;
        }

        if (document.querySelector('#文本字段5').value.trim() == "") {
            notifier.show(`到货日期不能为空`, 'danger');
            return false;
        }

        //检查明细
        for (let row of all_rows) {
            if (row.querySelector('td:nth-child(1)').textContent != "") {
                if (row.querySelector('.物料号').value.trim() == "") {
                    notifier.show(`物料号不能为空`, 'danger');
                    return false;
                }

                if (row.querySelector('.长度').value && !regInt.test(row.querySelector('.长度').value)) {
                    notifier.show(`长度输入错误`, 'danger');
                    return false;
                } else if (!row.querySelector('.长度').value) {
                    row.querySelector('.长度').value = 0;
                }

                if (row.querySelector('.重量').textContent.trim() == "") {
                    row.querySelector('.重量').textContent = 0;
                }
            }
        }
        return true;
    }

    window.onbeforeunload = function (e) {
        if (edited || edit_table.input_table_outdata().edited) {
            var e = window.event || e;
            e.returnValue = ("编辑未保存提醒");
        }
    }
}();