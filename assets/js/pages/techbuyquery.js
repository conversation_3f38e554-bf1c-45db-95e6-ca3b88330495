let page_techbuyquery = function () {
    let get_height = getHeight() - 133;
    let row_num = Math.floor(get_height / 33);

    let init_data = {
        container: '#table-stockout',
        url: `/get_tech_buy`,
        post_data: {
            id: "",
            name: '',
            sort: "build_date DESC",
            rec: row_num,
            cate: "",
        },
        edit: false,
        header_names: {
            "id": "id",
            "名称": "name",
            "说明": "note",
            "编号": "tech_no",
            "产品类型": "p_type",
            "牌号": "p_material",
            "编辑日期": "build_date",
            "编辑人": "editor",
            "提交审核": "sumit_review",
            "审核人": "review_name",
            "生效日期": "review_date",
            "供应商协议1": "s.docs[1]",
            "供应商协议2": "s.docs[2]",
            "供应商协议3": "s.docs[3]",
        },

        row_fn: row_fn,
    };

    tool_table.table_init(init_data);
    tool_table.fetch_table();

    //点击搜索按钮
    document.querySelector('#serach-button').addEventListener('click', function () {
        let fields = document.querySelector('#search-fields').value;
        init_data.post_data.name = fields;
        init_data.post_data.page = 1;
        tool_table.fetch_table();
    });

    function row_fn(tr) {
        const fei = tr.作废 ? " class='fei'" : "";
        return `<tr${fei}><td>${tr.序号}
            </td><td class="名称" title="查阅编辑"><a href="/tech_buy/${tr.id}__编辑" target="_blank">${tr.名称}</a>
            </td><td class="说明">${tr.说明}</td><td class="编号">${tr.编号}</td><td class="产品类型">${tr.产品类型}</td>
            <td class="牌号">${tr.牌号}</td><td class="供应商1"><a href='${tr.PDF1}'>${tr.供应商1}</a></td>
            <td class="供应商2"><a href='${tr.PDF2}'>${tr.供应商2}</a></td><td class="供应商3">
            <a href='${tr.PDF3}'>${tr.供应商3}</a></td>
            <td class="编辑人">${tr.编辑人}</td><td class="编辑日期">${tr.编辑日期}</td>
            <td class="提交审核">${tr.提交审核 == true ? '是' : ''}</td><td class="审核人">${tr.审核人}</td>
            <td class="生效日期">${tr.生效日期}</td><td class="id" hidden>${tr.id}</td></tr>`;
    }

    //修订按键
    document.querySelector('#button-revise').addEventListener('click', function () {
        let chosed = document.querySelector('tbody .focus');

        if (!chosed) {
            notifier.show('请先选择协议', 'danger');
            return;
        }

        const shen = chosed.querySelector('.审核人').textContent.trim();

        if (shen === '') {
            notifier.show('协议未审核', 'danger');
            return;
        }

        alert_confirm('确认修订此协议吗？', {
            confirmCallBack: () => {
                const id = chosed.querySelector('td:last-child').textContent.trim();
                const data = { id: id };

                fetch(`/revise_techbuy`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(data),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content != -1) {
                            let address = '/tech_buy/' + content + "__编辑";
                            window.open(address);
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            },
        });
    });

    //审核协议按键
    document.querySelector('#button-shen').addEventListener('click', function () {
        let chosed = document.querySelector('tbody .focus');

        if (!chosed) {
            notifier.show('请先选择协议', 'danger');
            return;
        }

        const sumit_shen = chosed.querySelector('.提交审核').textContent.trim();

        if (sumit_shen === '') {
            notifier.show('协议未提交审核', 'danger');
            return;
        }

        let id = chosed.querySelector('td:last-child').textContent.trim();
        let address = '/tech_buy/' + id + "__审核";
        window.open(address);
    });

    //作废按键
    document.querySelector('#button-fei').addEventListener('click', function () {
        let chosed = document.querySelector('tbody .focus');

        if (!chosed) {
            notifier.show('请先选择协议', 'danger');
            return;
        }

        alert_confirm('确认作废此协议吗？', {
            confirmCallBack: () => {
                const id = chosed.querySelector('td:last-child').textContent.trim();

                const data = { id: id };

                fetch(`/fei_techbuy`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(data),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content != -1) {
                            chosed.style.color = "lightgray";
                            chosed.style.textDecoration = "line-through";
                            notifier.show('协议已作废', 'success');
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            },
        });
    });
}();