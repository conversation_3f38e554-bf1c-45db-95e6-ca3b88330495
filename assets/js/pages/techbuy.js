const {
    DecoupledEditor,
    Alignment,
    Autoformat,
    AutoImage,
    AutoLink,
    Autosave,
    BalloonToolbar,
    Bold,
    Bookmark,
    Code,
    Essentials,
    FindAndReplace,
    FontBackgroundColor,
    FontColor,
    FontFamily,
    FontSize,
    Fullscreen,
    Heading,
    HorizontalLine,
    ImageBlock,
    ImageCaption,
    ImageEditing,
    ImageInline,
    ImageInsert,
    ImageInsertViaUrl,
    ImageResize,
    ImageStyle,
    ImageTextAlternative,
    ImageToolbar,
    ImageUpload,
    ImageUtils,
    Indent,
    IndentBlock,
    Italic,
    Link,
    LinkImage,
    List,
    PageBreak,
    Paragraph,
    PasteFromOffice,
    RemoveFormat,
    SimpleUploadAdapter,
    SpecialCharacters,
    SpecialCharactersArrows,
    SpecialCharactersCurrency,
    SpecialCharactersEssentials,
    SpecialCharactersLatin,
    SpecialCharactersMathematical,
    SpecialCharactersText,
    Strikethrough,
    Subscript,
    Superscript,
    Table,
    TableCaption,
    TableCellProperties,
    TableColumnResize,
    TableProperties,
    TableToolbar,
    TextTransformation,
    Underline,
    GeneralHtmlSupport
} = window.CKEDITOR || {};

// 全局变量声明
let doc_id, uuid, init_data, supplier_data;
let readonly = false;
const LICENSE_KEY = 'GPL';

// 默认模板内容
const defaultTemplate = `<h3>1.范围</h3>
<h3>2.引用标准</h3>
<h3>3.尺寸、外协、重量及允许偏差</h3>
<h4>3.1尺寸允许的偏差</h4>
<h4>3.2长度</h4>
<h4>3.3外形</h4>
<h5>3.3.1弯曲度</h5>
<h5>3.3.2端部外形</h5>
<h4>3.4重量</h4>
<h4>3.5其他自定义项 +</h4>
<h3>4技术要求</h3>
<h4>4.1钢的牌号及化学成分表</h4>
<h4>4.2制造方法</h4>
<h5>4.2.1冶炼方法</h5>
<h5>4.2.2压缩比</h5>
<h5>4.2.3自定义项 +</h5>
<h4>4.3交货状态</h4>
<h4>4.4用途</h4>
<h4>4.5热处理要求</h4>
<h5>4.5.1热处理炉要求</h5>
<h5>4.5.2热处理温度要求及去应力要求</h5>
<h5>4.5.3自定义项 +</h5>
<h4>4.6机械性能</h4>
<h5>4.6.1取样</h5>
<h5>4.6.2制样及检验</h5>
<h5>4.6.3机械性能要求表</h5>
<h5>4.6.4自定义项 +</h5>
<h4>4.7低倍组织要求</h4>
<h4>4.8非金属夹杂物</h4>
<h4>4.9晶粒度</h4>
<h4>4.10铁素体</h4>
<h4>4.11象限硬度</h4>
<h4>4.12表面硬度</h4>
<h4>4.13无损检测</h4>
<h3>5.表面质量</h3>
<h3>6.试验方法表</h3>
<h3>7.检验规则</h3>
<h5>7.1检查和验收</h5>
<h5>7.2组批规则</h5>
<h5>7.3其他</h5>
<h3>8.包装标志和质量证明书</h3>
<h4>8.1包装要求</h4>
<h4>8.2标识要求</h4>
<h4>8.3质量证明书要求</h4>
<h4>8.4自定义项</h4>
<h3>9.实施日期</h3>
<h3>10.自定义项</h3>`;

// 初始化数据函数
async function initializeDocumentData() {
    const material = document.getElementById('material');
    const auto_comp1 = new AutoInput(material, "全部", "/get_material");
    auto_comp1.init();

    // 获取文档ID
    const doc_id_cate = document.querySelector('#document-id').textContent.trim();

    if (doc_id_cate === '新文档') {
        uuid = generateUUID();
        init_data = defaultTemplate;
        document.getElementById('save-button').style.display = 'block';
        document.getElementById('word-button').style.display = 'block';
        document.getElementById('submit-button').style.display = 'block';
        document.getElementById('supplier-button').style.display = 'block';

        document.getElementById('build-date').value = new Date().Format("yyyy-MM-dd");
        document.getElementById('version-number').textContent = 'A';
    } else {
        try {
            doc_id = doc_id_cate.split("__")[0];
            uuid = doc_id;
            const doc_cate = doc_id_cate.split("__")[1];

            const response = await fetch(`/get_tech_buy_data?id=${doc_id}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.fei) {
                document.getElementById('fei-pic').style.display = 'block';
                setTimeout(() => {
                    disableAllInputs();
                }, 200);
            }

            if (data) {
                // 设置编辑器内容，处理转义字符
                init_data = unescapeHtml(data.content) || defaultTemplate;

                // 填充表单字段
                document.getElementById('product-type').value = data.p_type;
                document.getElementById('material').value = data.p_material;
                document.getElementById('spec-name').value = data.name;
                document.getElementById('spec-note').value = data.note;
                document.getElementById('spec-number').value = data.tech_no;
                document.getElementById('build-date').value = data.build_date;
                document.getElementById('version-number').textContent = data.ver;

                document.getElementById('version-status').textContent = data.review_name != "" ?
                    `　生效日期：${data.review_date}` : "（未生效）";

                if (data.ver !== "A") {
                    document.getElementById('revise-button').style.display = 'block';
                }

                readonly = data.sumit_review;

                if (readonly == false) {
                    document.getElementById('save-button').style.display = 'block';
                    document.getElementById('word-button').style.display = 'block';
                    document.getElementById('submit-button').style.display = 'block';
                    document.getElementById('supplier-button').style.display = 'block';

                    if (data.ver != "A") {
                        document.getElementById('product-type').disabled = true;
                        document.getElementById('material').disabled = true;
                        document.getElementById('spec-name').disabled = true;
                        document.getElementById('spec-number').disabled = true;
                    }
                } else {
                    if (doc_cate == "编辑") {
                        document.getElementById('save-button').style.display = 'block';
                        document.getElementById('supplier-button').style.display = 'block';
                        document.getElementById('pdf-button').style.display = 'block';
                        document.getElementById('word-button').style.display = 'block';
                    } else if (doc_cate == "审核") {
                        document.getElementById('pass-button').style.display = 'block';
                        document.getElementById('reject-button').style.display = 'block';
                        document.getElementById('revise-button').style.display = 'none';
                    }

                    setTimeout(() => {
                        disableAllInputs();
                        document.getElementById('save-button').disabled = false;
                    }, 200);
                }
            } else {
                init_data = defaultTemplate;
            }

            fetch_suppliers();
        } catch (error) {
            // 发生错误时使用默认模板
            init_data = defaultTemplate;

            // 显示错误提示
            if (typeof notifier !== 'undefined') {
                notifier.show('加载文档数据失败，已使用默认模板', 'warning');
            }
        }
    }
}

// 获取关联供应商
function fetch_suppliers() {
    fetch(`/fetch_suppliers`, {
        method: 'post',
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(doc_id),
    })
        .then(response => response.json())
        .then(data => {
            if (data != -1) {
                supplier_data = data;
                let html = '<div>供应商协议：</div>';
                for (let da of data) {
                    if (da.pdf) {
                        html += `<div><a href="${da.pdf}?${Math.random()}" target="_blank">${da.name}_${da.doc_no}</a></div>`;
                    } else {
                        html += `<div>${da.name}_${da.doc_no} (无PDF)</div>`;
                    }
                }
                document.getElementById('supplier-list').innerHTML = html;
            } else {
                notifier.show('权限不够，操作失败', 'danger');
            }
        });
}

// 创建编辑器配置的函数
function createEditorConfig(initialData) {
    return {
        toolbar: {
            items: [
                'undo',
                'redo',
                '|',
                'heading',
                '|',
                'fontSize',
                'fontFamily',
                'fontColor',
                'fontBackgroundColor',
                '|',
                'bold',
                'italic',
                'underline',
                'alignment',
                '|',
                'bulletedList',
                'numberedList',
                'outdent',
                'indent',
                '|',
                'insertTable'
            ],
            shouldNotGroupWhenFull: false
        },
        plugins: [
            Alignment,
            Autoformat,
            AutoImage,
            AutoLink,
            Autosave,
            BalloonToolbar,
            Bold,
            Bookmark,
            Code,
            Essentials,
            FindAndReplace,
            FontBackgroundColor,
            FontColor,
            FontFamily,
            FontSize,
            Fullscreen,
            Heading,
            HorizontalLine,
            ImageBlock,
            ImageCaption,
            ImageEditing,
            ImageInline,
            ImageInsert,
            ImageInsertViaUrl,
            ImageResize,
            ImageStyle,
            ImageTextAlternative,
            ImageToolbar,
            ImageUpload,
            ImageUtils,
            Indent,
            IndentBlock,
            Italic,
            Link,
            LinkImage,
            List,
            PageBreak,
            Paragraph,
            PasteFromOffice,
            RemoveFormat,
            SimpleUploadAdapter,
            SpecialCharacters,
            SpecialCharactersArrows,
            SpecialCharactersCurrency,
            SpecialCharactersEssentials,
            SpecialCharactersLatin,
            SpecialCharactersMathematical,
            SpecialCharactersText,
            Strikethrough,
            Subscript,
            Superscript,
            Table,
            TableCaption,
            TableCellProperties,
            TableColumnResize,
            TableProperties,
            TableToolbar,
            TextTransformation,
            Underline
        ],
        balloonToolbar: ['bold', 'italic', '|', 'bulletedList', 'numberedList'],
        fontFamily: {
            supportAllValues: true
        },
        fontSize: {
            options: [10, 12, 14, 'default', 18, 20, 22],
            supportAllValues: true
        },
        fullscreen: {
            onEnterCallback: container =>
                container.classList.add(
                    'editor-container',
                    'editor-container_document-editor',
                    'editor-container_include-fullscreen',
                    'main-container'
                )
        },
        heading: {
            options: [
                {
                    model: 'paragraph',
                    title: 'Paragraph',
                    class: 'ck-heading_paragraph'
                },
                {
                    model: 'heading1',
                    view: 'h1',
                    title: 'Heading 1',
                    class: 'ck-heading_heading1'
                },
                {
                    model: 'heading2',
                    view: 'h2',
                    title: 'Heading 2',
                    class: 'ck-heading_heading2'
                },
                {
                    model: 'heading3',
                    view: 'h3',
                    title: 'Heading 3',
                    class: 'ck-heading_heading3'
                },
                {
                    model: 'heading4',
                    view: 'h4',
                    title: 'Heading 4',
                    class: 'ck-heading_heading4'
                },
                {
                    model: 'heading5',
                    view: 'h5',
                    title: 'Heading 5',
                    class: 'ck-heading_heading5'
                },
                {
                    model: 'heading6',
                    view: 'h6',
                    title: 'Heading 6',
                    class: 'ck-heading_heading6'
                }
            ]
        },
        image: {
            toolbar: [
                'toggleImageCaption',
                'imageTextAlternative',
                '|',
                'imageStyle:inline',
                'imageStyle:wrapText',
                'imageStyle:breakText',
                '|',
                'resizeImage'
            ]
        },
        initialData: initialData,
        licenseKey: LICENSE_KEY,
        language: {
            ui: 'zh-cn',
            content: 'zh-cn'
        },
        link: {
            addTargetToExternalLinks: true,
            defaultProtocol: 'https://',
            decorators: {
                toggleDownloadable: {
                    mode: 'manual',
                    label: 'Downloadable',
                    attributes: {
                        download: 'file'
                    }
                }
            }
        },
        placeholder: '请输入内容',
        table: {
            contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells', 'tableProperties', 'tableCellProperties']
        },
        htmlSupport: {
            allow: [
                {
                    name: /.*/,
                    attributes: true,
                    classes: true,
                    styles: true
                }
            ]
        }
    };
}

let editorInstance = null;

// 处理 WPS 粘贴导致文字不可见的问题（比如粘贴过来的文本样式是 white 或透明）
function normalizeWpsHtml(html) {
    try {
        const doc = new DOMParser().parseFromString(html, 'text/html');
        // 移除 WPS/Office 特有的 mso-* 样式以及可能导致不可见的颜色
        const all = doc.body.querySelectorAll('*');
        all.forEach(el => {
            const style = el.getAttribute('style');
            if (!style) return;
            let newStyle = style;
            // 去除 mso- 前缀样式
            newStyle = newStyle
                .replace(/(^|;\s*)(mso-[^:]+:[^;]+;?)/gi, ';')
                .replace(/(^|;\s*)(tab-stops:[^;]+;?)/gi, ';');
            // 规范 color，避免白色或透明
            const colorMatch = newStyle.match(/color\s*:\s*([^;]+)\s*;?/i);
            if (colorMatch) {
                const color = colorMatch[1].trim().toLowerCase();
                if (color === '#fff' || color === '#ffffff' || color === 'white' ||
                    color === 'rgba(255,255,255,1)' || color === 'rgba(255, 255, 255, 1)' ||
                    color === 'rgb(255,255,255)' || color === 'rgb(255, 255, 255)' ||
                    color === 'transparent') {
                    newStyle = newStyle.replace(colorMatch[0], 'color: #000;');
                }
            }
            // 可能存在将字体颜色写成 background-clip 等影响显示的情况，强制修正
            const fgMatches = ['-webkit-text-fill-color', 'text-fill-color'];
            fgMatches.forEach(p => {
                const re = new RegExp(p + '\\s*:\\s*([^;]+)\\s*;?', 'i');
                const m = newStyle.match(re);
                if (m) {
                    const v = (m[1] || '').toLowerCase();
                    if (v.includes('white') || v.includes('#fff') || v.includes('#ffffff') || v.includes('transparent')) {
                        newStyle = newStyle.replace(re, p + ': #000;');
                    }
                }
            });

            // 清理多余分号
            newStyle = newStyle.replace(/;;+/g, ';').replace(/^;|;$/g, '');

            if (newStyle.trim()) el.setAttribute('style', newStyle);
            else el.removeAttribute('style');
        });

        return doc.body.innerHTML;
    } catch (_) {
        return html;
    }
}

let lastSavedContent = '';
let hasUnsavedChanges = false;

function markSaveButtonUnsaved() {
    const btn = document.getElementById('save-button');
    if (!btn) return;
    if (btn.disabled) return;
    btn.style.backgroundColor = 'red';
    btn.style.borderColor = 'red';
}

function resetSaveButtonState() {
    const btn = document.getElementById('save-button');
    if (!btn) return;
    btn.style.backgroundColor = '';
    btn.style.borderColor = '';
}

function setupSaveButtonDirtyTracking(editor) {
    try {
        lastSavedContent = editor.getData();
        hasUnsavedChanges = false;

        editor.model.document.on('change:data', () => {
            const current = editor.getData();
            const changed = current !== lastSavedContent;
            if (changed && !hasUnsavedChanges) {
                hasUnsavedChanges = true;
                markSaveButtonUnsaved();
            } else if (!changed && hasUnsavedChanges) {
                hasUnsavedChanges = false;
                resetSaveButtonState();
            }
        });
    } catch (_) {
        // ignore
    }
}

// 创建编辑器的异步函数
async function createEditor() {
    try {
        // 确保 init_data 已经被设置
        if (!init_data) {
            throw new Error('初始数据未设置');
        }

        // 使用动态创建的配置
        const editorConfig = createEditorConfig(init_data);
        const editor = await DecoupledEditor.create(document.querySelector('#editor'), editorConfig);

        // 粘贴修复：拦截 WPS 的 HTML 数据并规范化
        const clipboardPipeline = editor.plugins.get('ClipboardPipeline');
        clipboardPipeline.on('inputTransformation', (evt, data) => {
            try {
                const html = data?.dataTransfer?.getData('text/html');
                if (!html) return;
                // WPS 常见标记：xmlns:wps、meta name="Generator" content="WPS"、w:*, o:*
                const isWps = /xmlns:\w+=\"urn:schemas-microsoft-com|wps\.|<meta[^>]+WPS|<\w+:[^>]+>/i.test(html);
                if (!isWps) return;
                const normalized = normalizeWpsHtml(html);
                if (normalized && normalized !== html) {
                    data.content = editor.data.htmlProcessor.toView(normalized);
                }
            } catch (_) { /* ignore */ }
        }, { priority: 'high' });

        document.querySelector('#editor-toolbar').appendChild(editor.ui.view.toolbar.element);
        editorInstance = editor;

        // 设置编辑器自适应高度
        adjustEditorHeight();

        // 监听窗口大小变化
        window.addEventListener('resize', adjustEditorHeight);

        // 初始化大纲功能
        initOutlinePanel(editor);

        return editor;
    } catch (error) {
        if (typeof notifier !== 'undefined') {
            notifier.show('编辑器初始化失败', 'danger');
        }
        throw error;
    }
}

// 自适应高度调整函数
function adjustEditorHeight() {
    const editorElement = document.querySelector('.ck-editor__editable');
    if (editorElement) {
        // 计算可用高度
        const windowHeight = window.innerHeight;
        const topTitle = document.querySelector('.top-title');
        const formSection = document.querySelector('.form-section');
        const toolbar = document.querySelector('.editor-container__toolbar');
        const footer = document.querySelector('.editor-footer');

        let usedHeight = 0;
        if (topTitle) usedHeight += topTitle.offsetHeight;
        if (formSection) usedHeight += formSection.offsetHeight;
        if (toolbar) usedHeight += toolbar.offsetHeight;
        if (footer) usedHeight += footer.offsetHeight;

        // 预留更多边距空间，让编辑器不那么满
        const availableHeight = windowHeight - usedHeight - 200;

        // 设置最小高度
        const minHeight = Math.max(availableHeight, 250);
        editorElement.style.minHeight = minHeight + 'px';
    }
}

// 大纲功能初始化
function initOutlinePanel(editor) {
    const outlineContent = document.getElementById('outline-content');
    const outlineToggle = document.getElementById('outline-toggle');
    const outlinePanel = document.getElementById('outline-panel');

    // 大纲面板折叠/展开功能
    outlineToggle.addEventListener('click', () => {
        outlinePanel.classList.toggle('collapsed');
    });

    // 监听编辑器内容变化，更新大纲
    editor.model.document.on('change:data', () => {
        updateOutline(editor, outlineContent);
    });

    // 初始化大纲
    updateOutline(editor, outlineContent);
}

// 更新大纲
function updateOutline(editor, outlineContent) {
    const editorData = editor.getData();
    const headings = extractHeadings(editorData);

    if (headings.length === 0) {
        outlineContent.innerHTML = '<div class="outline-empty" style="font-size: 14px; padding: 10px; color: #999;">暂无标题</div>';
        return;
    }

    const outlineHTML = headings.map((heading, index) => {
        // 计算缩进：每级缩进16px，从h1开始
        const indentLevel = Math.max(0, heading.level - 1);
        const paddingLeft = 8 + (indentLevel * 16);

        return `<div class="outline-item level-${heading.level}" data-heading-id="${heading.id}" data-index="${index}" style="font-size: 14px; line-height: 1.4; padding: 6px 8px 6px ${paddingLeft}px; cursor: pointer; border-radius: 4px; margin: 1px 0;">
            ${heading.text}
        </div>`;
    }).join('');

    outlineContent.innerHTML = outlineHTML;

    // 为大纲项添加点击事件和悬停效果
    outlineContent.querySelectorAll('.outline-item').forEach(item => {
        // 添加悬停效果
        item.addEventListener('mouseenter', () => {
            if (!item.classList.contains('active')) {
                item.style.backgroundColor = '#f5f5f5';
            }
        });

        item.addEventListener('mouseleave', () => {
            if (!item.classList.contains('active')) {
                item.style.backgroundColor = '';
            }
        });

        // 点击事件
        item.addEventListener('click', () => {
            const headingId = item.dataset.headingId;

            // 执行滚动
            try {
                simpleScrollToHeading(editor, headingId);
            } catch (error) {
                scrollToHeading(editor, headingId);
            }

            // 更新活动状态
            outlineContent.querySelectorAll('.outline-item').forEach(i => {
                i.classList.remove('active');
                i.style.backgroundColor = '';
                i.style.color = '';
                i.style.fontWeight = '';
            });

            // 设置当前项为激活状态
            item.classList.add('active');
            item.style.backgroundColor = '#e3f2fd';
            item.style.color = '#1976d2';
            item.style.fontWeight = '500';
        });
    });
}

// 提取标题
function extractHeadings(htmlContent) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    const headingElements = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');

    return Array.from(headingElements).map((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        const text = heading.textContent.trim();
        const id = `heading-${index}`;

        return {
            level,
            text,
            id,
            element: heading
        };
    });
}

// 高亮标题及其内容
function highlightHeadingContent(heading) {
    // 清除之前的高亮
    clearPreviousHighlight();

    // 高亮标题本身
    heading.style.backgroundColor = '#fff3cd';
    heading.style.border = '2px solid #ffc107';
    heading.style.borderRadius = '4px';
    heading.style.padding = '8px';
    heading.style.margin = '4px 0';
    heading.classList.add('editing-section');

    // 收集标题后的内容直到下一个同级或更高级标题
    const headingLevel = parseInt(heading.tagName.charAt(1));
    const contentElements = [];
    let nextElement = heading.nextElementSibling;

    while (nextElement) {
        const isHeading = /^H[1-6]$/.test(nextElement.tagName);
        if (isHeading) {
            const nextLevel = parseInt(nextElement.tagName.charAt(1));
            if (nextLevel <= headingLevel) {
                break; // 遇到同级或更高级标题，停止
            }
        }
        contentElements.push(nextElement);
        nextElement = nextElement.nextElementSibling;
    }

    // 高亮相关内容
    contentElements.forEach(element => {
        element.style.backgroundColor = '#f8f9fa';
        element.style.border = '1px solid #dee2e6';
        element.style.borderRadius = '4px';
        element.style.padding = '4px';
        element.style.margin = '2px 0';
        element.classList.add('editing-section');
    });

    // 显示编辑指示器
    showEditingIndicator(heading.textContent.trim());

    // 保存当前高亮的元素，以便后续清除
    window.currentHighlightedElements = [heading, ...contentElements];

    // 1秒后移除高亮
    setTimeout(() => {
        clearPreviousHighlight();
    }, 1000);
}

// 清除之前的高亮
function clearPreviousHighlight() {
    if (window.currentHighlightedElements) {
        window.currentHighlightedElements.forEach(element => {
            element.style.backgroundColor = '';
            element.style.border = '';
            element.style.borderRadius = '';
            element.style.padding = '';
            element.style.margin = '';
            element.classList.remove('editing-section');
        });
        window.currentHighlightedElements = null;
    }

    // 移除编辑指示器
    const indicator = document.querySelector('.editing-indicator');
    if (indicator) {
        indicator.remove();
    }
}

// 显示编辑指示器
function showEditingIndicator(headingText) {
    // 移除现有指示器
    const existingIndicator = document.querySelector('.editing-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // 创建新指示器
    const indicator = document.createElement('div');
    indicator.className = 'editing-indicator';
    indicator.innerHTML = `<i class="fa fa-edit"></i> 正在编辑: ${headingText}`;

    document.body.appendChild(indicator);

    // 2秒后自动移除
    setTimeout(() => {
        if (indicator.parentNode) {
            indicator.remove();
        }
    }, 2000);
}

// 简化的滚动函数
function simpleScrollToHeading(editor, headingId) {
    const editorElement = document.querySelector('.ck-editor__editable');
    if (!editorElement) return;

    const headings = editorElement.querySelectorAll('h1, h2, h3, h4, h5, h6');
    const targetHeading = Array.from(headings).find((h, index) => `heading-${index}` === headingId);

    if (targetHeading) {
        // 直接使用浏览器原生滚动
        targetHeading.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });

        // 延迟执行高亮和光标定位
        setTimeout(() => {
            highlightHeadingContent(targetHeading);
            editor.editing.view.focus();
        }, 500);
    }
}

class TechBuyEditor {
    constructor() {
        this.init();
    }

    init() {
        this.setDate();
        this.bindEvents();
    }

    setDate() {
        let date = document.querySelector('#build-date');
        laydate.render({
            elem: date,
            showBottom: false,
        });
    }

    bindEvents() {
        // 表单按钮事件
        document.getElementById('product-type')?.addEventListener('change', () => {
            this.productChange();
        });

        document.getElementById('save-button')?.addEventListener('click', () => {
            this.saveDocument();
        });

        document.getElementById('submit-button')?.addEventListener('click', () => {
            this.submitForReview();
        });

        document.getElementById('word-button')?.addEventListener('click', () => {
            this.exportToWord();
        });

        document.getElementById('pdf-button')?.addEventListener('click', () => {
            this.exportToPDF();
        });

        document.getElementById('pass-button')?.addEventListener('click', () => {
            this.passDocument();
        });

        document.getElementById('reject-button')?.addEventListener('click', () => {
            this.rejectDocument();
        });

        document.getElementById('supplier-button')?.addEventListener('click', () => {
            if (document.querySelector('#document-id').textContent.trim() == "新文档") {
                notifier.show('请先保存文档', 'danger');
                return;
            }
            this.supplierDocument();
        });

        document.getElementById('revise-button')?.addEventListener('click', () => {
            this.reviseDocument();
        });

        document.getElementById('modal-sumit-button').addEventListener('click', () => {
            if (document.querySelector('.modal-title').textContent.trim() != "修订履历") {
                this.submitSupplier();
            } else {
                this.submitRevision();
            }
        });
    }

    productChange() {
        const productType = document.getElementById('product-type').value;
        const specNumber = document.getElementById('spec-number');
        const p_type = productType == "圆钢" ? "B" : "P";

        let tech_no = "FS-T-M-" + p_type;

        if (specNumber.value.startsWith("FS-T-M")) {
            alert_confirm('规范编号已生成，需重新生成吗？', {
                confirmCallBack: () => {
                    fetch(`/get_number`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            productType: p_type,
                        })
                    }).then(response => response.json())
                        .then(data => {
                            tech_no += padZero(data, 4);
                            specNumber.value = tech_no + 'A';
                        });
                }
            });
        } else {
            fetch(`/get_number`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    productType: p_type,
                })
            }).then(response => response.json())
                .then(data => {
                    tech_no += padZero(data, 4);
                    specNumber.value = tech_no + 'A';
                });
        }
    }

    reviseDocument() {
        fetch(`/get_revision?id=${doc_id}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        }).then(response => response.json())
            .then(data => {
                let da = data;
                if (data == 0) {
                    da = {
                        section: "",
                        content: "",
                        note: ""
                    }
                }
                const form = `<div class="form-group autocomplete" style="z-index: 1006;">
                            <div class="form-label">
                                <label>原文件章节</label>
                            </div>
                            <input class="form-control input-sm has-value" id="old_section" type="text" style="width: 300px;" value="${da.section}" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>修订内容</label>
                            </div>
                            <textarea class="form-control input-sm has-value" rows='3' id="revision_content" style="width: 305px; height: 70px;"></textarea>
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>修订说明</label>
                            </div>
                            <input class="form-control input-sm has-value" id="revision_note" type="text" style="width: 300px;" value="${da.note}" />
                        </div>
                        `;

                document.querySelector('.modal-body').innerHTML = form;

                // 将字符串中的 \n 转换为实际的换行符
                document.querySelector('.modal-body textarea').value = da.content.replace(/\\n/g, '\n');

                document.querySelector('.modal-title').textContent = "修订履历";
                document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;";
                document.querySelector('.modal').style.top = "50px";
                document.querySelector('.modal').style.display = "block";
                document.querySelector('.modal-body input').focus();
            });
    }

    submitRevision() {
        const old_section = document.querySelector('#old_section').value.trim();
        const revision_content = document.querySelector('#revision_content').value.trim();
        const revision_note = document.querySelector('#revision_note').value.trim();

        if (!old_section || !revision_content) {
            notifier.show('请填写完整的修订信息', 'danger');
            return;
        }

        fetch(`/submit_revision`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                tech_id: doc_id,
                section: old_section,
                content: revision_content,
                note: revision_note
            }),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    notifier.show('保存成功', 'success');
                    document.querySelector('.modal').style.display = "none";
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });

    }

    submitSupplier() {
        const suplier_name1 = document.querySelector('#suplier1').value.trim();
        const doc_no1 = document.querySelector('#doc_no1').value.trim();
        const pdf1 = document.querySelector('#pdf_save1').textContent.trim();

        const suplier_name2 = document.querySelector('#suplier2').value.trim();
        const doc_no2 = document.querySelector('#doc_no2').value.trim();
        const pdf2 = document.querySelector('#pdf_save2').textContent.trim();

        const suplier_name3 = document.querySelector('#suplier3').value.trim();
        const doc_no3 = document.querySelector('#doc_no3').value.trim();
        const pdf3 = document.querySelector('#pdf_save3').textContent.trim();

        if (!suplier_name1 && !suplier_name2 && !suplier_name3) {
            alert_confirm('将删除全部关联供应商，确认吗？', {
                confirmCallBack: () => {
                    fetch(`/delete_suppliers`, {
                        method: 'post',
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify(doc_id),
                    })
                        .then(response => response.json())
                        .then(content => {
                            if (content != -1) {
                                notifier.show('操作成功', 'success');
                                document.querySelector('.modal').style.display = "none";
                                fetch_suppliers();
                            } else {
                                notifier.show('权限不够，操作失败', 'danger');
                            }
                        });
                }
            });
        } else {
            fetch(`/delete_suppliers`, {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(doc_id),
            })
                .then(response => response.json())
                .then(content => {
                    if (content != -1) {
                        if (suplier_name1) {
                            const suplier1 = document.querySelector('#suplier1').getAttribute('data');
                            this.addSupplier(suplier1, doc_no1, pdf1);
                        }
                        if (suplier_name2) {
                            const suplier2 = document.querySelector('#suplier2').getAttribute('data');
                            this.addSupplier(suplier2, doc_no2, pdf2);
                        }
                        if (suplier_name3) {
                            const suplier3 = document.querySelector('#suplier3').getAttribute('data');
                            this.addSupplier(suplier3, doc_no3, pdf3);
                        }
                        setTimeout(() => {
                            fetch_suppliers();
                        }, 1000);

                        notifier.show('操作成功', 'success');
                        document.querySelector('.modal').style.display = "none";
                    } else {
                        notifier.show('权限不够，操作失败', 'danger');
                    }
                });
        }
    }

    addSupplier(suplier, doc_no, pdf) {
        fetch(`/add_supplier`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                doc_id: doc_id,
                suplier_id: suplier,
                doc_no: doc_no,
                pdf: pdf
            }),
        })
            .then(response => response.json())
            .then(content => {
                if (content == -1) {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    }

    supplierDocument() {
        const form = `<div class="form-group autocomplete" style="z-index: 1006;">
                            <div class="form-label">
                                <label>供应商1</label>
                            </div>
                            <input class="form-control input-sm has-value" id="suplier1" type="text" style="width: 300px;" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>协议号</label>
                            </div>
                            <input class="form-control input-sm has-value" id="doc_no1" type="text" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>协议PDF</label>
                            </div>
                            <button class="btn btn-info btn-sm" id="pdf_button1" style="margin-right: 10px">上传协议书</button>
                            <input type="file" id="pdf_upload1" accept="application/pdf" hidden="hidden"/>
                            <span id="pdf_save1" hidden></span><span id="done1"></span>
                        </div>
                        <div style="border-top: 1px solid #e5e5e5; margin: 20px 0;"></div>
                        <div class="form-group autocomplete" style="z-index: 1005;">
                            <div class="form-label">
                                <label>供应商2</label>
                            </div>
                            <input class="form-control input-sm has-value" id="suplier2" type="text" style="width: 300px;" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>协议号</label>
                            </div>
                            <input class="form-control input-sm has-value" id="doc_no2" type="text" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>协议PDF</label>
                            </div>
                            <button class="btn btn-info btn-sm" id="pdf_button2" style="margin-right: 10px">上传协议书</button>
                            <input type="file" id="pdf_upload2" accept="application/pdf" hidden="hidden"/>
                            <span id="pdf_save2" hidden></span><span id="done2"></span>
                        </div>
                        <div style="border-top: 1px solid #e5e5e5; margin: 20px 0;"></div>
                        <div class="form-group autocomplete" style="z-index: 1004;">
                            <div class="form-label">
                                <label>供应商3</label>
                            </div>
                            <input class="form-control input-sm has-value" id="suplier3" type="text" style="width: 300px;" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>协议号</label>
                            </div>
                            <input class="form-control input-sm has-value" id="doc_no3" type="text" />
                        </div>
                        <div class="form-group">
                            <div class="form-label">
                                <label>协议PDF</label>
                            </div>                            
                            <button class="btn btn-info btn-sm" id="pdf_button3" style="margin-right: 10px">上传协议书</button>
                            <input type="file" id="pdf_upload3" accept="application/pdf" hidden="hidden"/>
                            <span id="pdf_save3" hidden></span><span id="done3"></span>
                        </div>
                        `;

        document.querySelector('.modal-body').innerHTML = form;

        if (supplier_data) {
            if (supplier_data[0]) {
                document.querySelector('#suplier1').value = supplier_data[0].name;
                document.querySelector('#suplier1').setAttribute('data', supplier_data[0].supplier_id);
                document.querySelector('#doc_no1').value = supplier_data[0].doc_no;
                document.querySelector('#pdf_save1').textContent = supplier_data[0].pdf;
                if (supplier_data[0].pdf) {
                    document.querySelector('#done1').textContent = '上传完成';
                    document.querySelector('#done1').style.color = "#4CAF50";
                }
            }
            if (supplier_data[1]) {
                document.querySelector('#suplier2').value = supplier_data[1].name;
                document.querySelector('#suplier2').setAttribute('data', supplier_data[1].supplier_id);
                document.querySelector('#doc_no2').value = supplier_data[1].doc_no;
                document.querySelector('#pdf_save2').textContent = supplier_data[1].pdf;
                if (supplier_data[1].pdf) {
                    document.querySelector('#done2').textContent = '上传完成';
                    document.querySelector('#done2').style.color = "#4CAF50";
                }
            }
            if (supplier_data[2]) {
                document.querySelector('#suplier3').value = supplier_data[2].name;
                document.querySelector('#suplier3').setAttribute('data', supplier_data[2].supplier_id);
                document.querySelector('#doc_no3').value = supplier_data[2].doc_no;
                document.querySelector('#pdf_save3').textContent = supplier_data[2].pdf;
                if (supplier_data[2].pdf) {
                    document.querySelector('#done3').textContent = '上传完成';
                    document.querySelector('#done3').style.color = "#4CAF50";
                }
            }
        }

        // 等待 modal 显示完成后再计算位置
        setTimeout(() => {
            // 获取 modal 的位置
            const modalRect = document.querySelector('.modal-dialog').getBoundingClientRect();

            const suplier1 = document.querySelector('#suplier1');
            const rect1 = suplier1.getBoundingClientRect();
            // 相对于 modal 的位置
            const modalRelativeTop1 = rect1.top - modalRect.top;
            const modalRelativeLeft1 = rect1.left - modalRect.left;
            let auto_comp1 = new AutoInput(suplier1, "供应商", "/get_suplier_auto", '', '', modalRelativeTop1 + 32, modalRelativeLeft1);
            auto_comp1.init();

            const suplier2 = document.querySelector('#suplier2');
            const rect2 = suplier2.getBoundingClientRect();
            const modalRelativeTop2 = rect2.top - modalRect.top;
            const modalRelativeLeft2 = rect2.left - modalRect.left;
            let auto_comp2 = new AutoInput(suplier2, "供应商", "/get_suplier_auto", '', '', modalRelativeTop2 + 32, modalRelativeLeft2);
            auto_comp2.init();

            const suplier3 = document.querySelector('#suplier3');
            const rect3 = suplier3.getBoundingClientRect();
            const modalRelativeTop3 = rect3.top - modalRect.top;
            const modalRelativeLeft3 = rect3.left - modalRect.left;
            let auto_comp3 = new AutoInput(suplier3, "供应商", "/get_suplier_auto", '', '', modalRelativeTop3 + 32, modalRelativeLeft3);
            auto_comp3.init();
        }, 100);

        pdf_button1.addEventListener('click', function (e) {
            e.preventDefault();
            pdf_upload1.click();
        });

        pdf_button2.addEventListener('click', function (e) {
            e.preventDefault();
            pdf_upload2.click();
        });

        pdf_button3.addEventListener('click', function (e) {
            e.preventDefault();
            pdf_upload3.click();
        });

        //上传 pdf 文件
        pdf_upload1.addEventListener('change', () => {
            if (pdf_upload1.files.length > 0) {
                const save_dom = document.querySelector('#pdf_save1');
                const done_dom = document.querySelector('#done1');
                this.up_pdf(pdf_button1, pdf_upload1, save_dom, done_dom, 1);
            } else {
                notifier.show('请先选择文件', 'warning');
            }
        });

        pdf_upload2.addEventListener('change', () => {
            if (pdf_upload2.files.length > 0) {
                const save_dom = document.querySelector('#pdf_save2');
                const done_dom = document.querySelector('#done2');
                this.up_pdf(pdf_button2, pdf_upload2, save_dom, done_dom, 2);
            } else {
                notifier.show('请先选择文件', 'warning');
            }
        });

        pdf_upload3.addEventListener('change', () => {
            if (pdf_upload3.files.length > 0) {
                const save_dom = document.querySelector('#pdf_save3');
                const done_dom = document.querySelector('#done3');
                this.up_pdf(pdf_button3, pdf_upload3, save_dom, done_dom, 3);
            } else {
                notifier.show('请先选择文件', 'warning');
            }
        });

        document.querySelector('.modal-title').textContent = "关联供应商";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;";
        document.querySelector('.modal').style.top = "30px";
        document.querySelector('.modal').style.display = "block";
        document.querySelector('.modal-body input').focus();
    }

    up_pdf(pdf_button, pdf_upload, save_dom, done_dom, idx) {
        pdf_button.disabled = true;
        const fd = new FormData();
        fd.append('file', pdf_upload.files[0]);
        // 组装自定义文件名：供应商 + 协议号
        let name = '';
        if (idx === 1) {
            name = `${document.querySelector('#suplier1').value.trim()}_${document.querySelector('#doc_no1').value.trim()}`;
        } else if (idx === 2) {
            name = `${document.querySelector('#suplier2').value.trim()}_${document.querySelector('#doc_no2').value.trim()}`;
        } else if (idx === 3) {
            name = `${document.querySelector('#suplier3').value.trim()}_${document.querySelector('#doc_no3').value.trim()}`;
        }
        name = name.replace(/\s+/g, '_');

        fetch(`/pdf_save?name=${encodeURIComponent(name)}`, {
            // 增大超时时间，部分网络环境下 2MB+ 文件更耗时
            // 使用默认 fetch 无法直接改超时，依赖后端 PayloadConfig 承载
            
            method: 'POST',
            body: fd,
        })
            .then(res => res.json())
            .then(content => {
                if (content == -3) {
                    notifier.show('文件大小不能超过10M', 'danger');
                    pdf_button.disabled = "";
                    return false;
                }
                save_dom.textContent = content;
                done_dom.textContent = "协议已上传";
                done_dom.style.color = "#4CAF50";
                notifier.show('协议pdf成功保存', 'success');
                pdf_button.disabled = "";
            });
    }

    getFormData() {
        return {
            productType: document.getElementById('product-type').value,
            material: document.getElementById('material').value,
            specName: document.getElementById('spec-name').value,
            ver: document.getElementById('version-number').textContent.trim(),
            specNumber: document.getElementById('spec-number').value,
            buildDate: document.getElementById('build-date').value,
            content: editorInstance ? editorInstance.getData() : '',
            specNote: document.getElementById('spec-note').value
        };
    }

    validateForm() {
        const data = this.getFormData();
        const errors = [];

        if (!data.productType) errors.push('请选择产品类型');
        if (!data.material.trim()) errors.push('请输入牌号');
        if (!data.specName.trim()) errors.push('请输入规范名称');
        if (!data.specNumber.trim()) errors.push('请输入规范编号');
        if (!data.content.trim() || data.content === '<p><br></p>' || data.content === '') errors.push('请输入技术规范内容');

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    saveDocument() {
        const validation = this.validateForm();

        if (!validation.isValid) {
            notifier.show(validation.errors[0], 'danger');
            return;
        }

        const data = this.getFormData();
        data.id = uuid;

        // 这里应该调用后端API保存数据
        fetch(`/save_techbuy`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    document.getElementById('document-id').textContent = uuid;
                    doc_id = uuid;
                    // 保存成功后重置保存按钮状态
                    if (editorInstance) {
                        lastSavedContent = editorInstance.getData();
                    }
                    hasUnsavedChanges = false;
                    resetSaveButtonState();
                    notifier.show('技术协议保存成功', 'success');
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    }

    submitForReview() {
        if (document.querySelector('#document-id').textContent.trim() == "新文档") {
            notifier.show('请先保存文档', 'danger');
            return;
        }

        alert_confirm('提交审核后将无法修改，确认提交吗？', {
            confirmCallBack: () => {
                const data = this.getFormData();
                data.id = uuid;

                fetch(`/shen_techbuy`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(data),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content != -1) {
                            disableAllInputs();
                            notifier.show('提交审核成功', 'success');
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            },
        });
    }

    // ------------- 导出 Word 和 PDF ---------------------

    // 计算文档页数
    // 统一页数计算逻辑：用与 PDF 切片一致的几何关系来估算页数，避免 dpi/缩放差异导致的页数偏大
    calculatePageCount(content) {
        // 创建一个临时容器来测量内容高度
        const tempDiv = document.createElement('div');
        tempDiv.style.cssText = `
            position: absolute;
            left: -9999px;
            width: 816px; /* A4 宽度 210mm ≈ 8.27in ≈ 816px @96dpi */
            padding: 72px; /* 统一边距为 0.75in */
            font-family: SimSun;
            font-size: 12pt;
            line-height: 1.5;
            visibility: hidden;
            box-sizing: border-box;
        `;
        tempDiv.innerHTML = content;
        document.body.appendChild(tempDiv);

        // 使用与 PDF 导出相同的几何：A4(mm) -> 可用内容高度(mm) -> 换算成 DOM 像素高度
        const pageWidthMM = 210; // A4 宽度(mm)
        const pageHeightMM = 297; // A4 高度(mm)
        const margin = 15; // 上下左右边距(mm)
        const headerHeightMM = 10; // 页眉高度(mm)

        const imgWidthMM = pageWidthMM - margin * 2;
        const usablePageHeightMM = pageHeightMM - margin * 2 - headerHeightMM;

        // 依据测量容器的实际宽度换算出一页可容纳的 DOM 像素高度
        const pageHeightDomPx = tempDiv.offsetWidth * (usablePageHeightMM / imgWidthMM);

        // 获取实际内容高度（用 scrollHeight 更稳妥）
        const contentHeight = tempDiv.scrollHeight;
        const pageCount = Math.max(1, Math.ceil(contentHeight / pageHeightDomPx));

        // 清理临时元素
        document.body.removeChild(tempDiv);

        return pageCount;
    }

    // 将图片转换为 base64，并返回 Word 兼容的图片标签
    async getImageBase64(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = () => {
                const maxWidth = 90; // a reasonable max width
                const scale = maxWidth / img.width;
                const width = maxWidth;
                const height = img.height * scale;

                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);
                const dataURL = canvas.toDataURL('image/png');
                resolve(`<img src="${dataURL}" width="${width}" height="${height}">`);
            };
            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = url;
        });
    }

    exportTable(data, signImage) {
        const infoTable = `            
            <table style="table-layout: fixed; width: 100%;">
                <tbody>
                    <tr>
                        <td rowspan="2" style="width:18.56%;">
                            <p>规范名称:</p>
                        </td>
                        <td colspan="2" rowspan="2" style="width:51.39%; text-align:left;"><p>${data.specName}</p></td>
                        <td style="width:12.86%;">
                            <p>编号：</p>
                        </td>
                        <td style="width:22.19%;"><p>${data.specNumber}</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="width:12.86%;">
                            <p>日期：</p>
                        </td>
                        <td style="width:22.19%;"><p>${data.buildDate}</p></td>
                    </tr>
                    <tr>
                        <td style="width:18.56%;">
                            <p>部门：</p>
                        </td>
                        <td style="width:24.73%;"><p>原料事业部</p></td>
                        <td style="width:23.66%;"><p id="page-count">页数：${data.pageCount || 1}</p></td>
                        <td style="width:12.86%;"><p>批准：</p></td>
                        <td style="width:22.19%;">${signImage}</td>
                    </tr>
                    <tr>
                        <td colspan="5" style="text-align:left;">
                            <p style="font-size:12px;">注意：本文档中包含的内容归五星石油所有。未经五星石油授权，不得复制或使用这些内容，并且这些内容仅在借阅条件下提供，需应五星石油的要求归还。</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        `;

        return infoTable;
    }

    async reviseTable(data) {
        const response = await fetch(`/get_revise_data`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: data.specName,
                tech_no: data.specNumber.replace(/.$/, ""),
                date: data.buildDate
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reviseData = await response.json();
        let revisedContent = '';
        for (let da of reviseData) {
            revisedContent += `
                <tr>
                    <td><p>${da.ver}</p></td>
                    <td><p>${da.section}</p></td>
                    <td><p style="text-align:left;">${da.content.replace(/\\n/g, '\n').split('\n').join('<br>')}</p></td>
                    <td><p>${da.date}</p></td>
                    <td><p>${da.note}</p></td>
                </tr>
            `;
        }

        const table = `
            <div style="font-weight: bold; margin-top: 10pt; margin-bottom: 4pt; text-align: center;">文件修订履历表</div>
            <table style="table-layout: fixed; width: 100%;">
                <thead>
                    <tr>
                        <th style="width:10%;"><p>版次</p></th>
                        <th style="width:15%;"><p>原文件章节</p></th>
                        <th style="width:35%;"><p>修订内容</p></th>
                        <th style="width:15%;"><p>实施日期</p></th>
                        <th style="width:15%;"><p>修订说明</p></th>
                    </tr>
                </thead>
                <tbody>
                ${revisedContent}
                </tbody>
            </table>
        `;

        return table
    }

    async exportToWord() {
        const validation = this.validateForm();

        if (!validation.isValid) {
            alert('请完善以下信息后再导出:\n' + validation.errors.join('\n'));
            return;
        }

        const data = this.getFormData();

        // 预加载并转换签名图片
        let signImage = await this.getImageBase64('/assets/img/sign_name.png');

        // 创建临时容器来构建导出内容
        const exportContainer = document.createElement('div');

        // 添加文档样式
        const styleSheet = document.createElement('style');
        styleSheet.textContent = `
            body { font-family: "SimSun", serif; font-size: 12pt; line-height: 1.5; }
            h1 { font-size: 18pt; font-weight: bold; text-align: center; margin: 24pt 0; line-height: 1.5; }
            h2 { font-size: 16pt; font-weight: bold; margin: 18pt 0 12pt 0; line-height: 1.5; }
            h4 { font-size: 12pt; font-weight: bold; margin: 12pt 0 8pt 0; line-height: 1.5; }
            h5 { font-size: 12pt; font-weight: bold; margin: 10pt 0 6pt 0; line-height: 1.5; }
            table { table-layout: fixed; width: 100%; border-collapse: collapse; margin: 12pt 0; }
            th, td { border: 1px solid black; padding: 3pt; text-align: center; vertical-align: middle; line-height: 18pt; }
        `;

        const infoTable = this.exportTable(data, signImage);
        const reviseTable = await this.reviseTable(data);

        exportContainer.innerHTML = styleSheet.outerHTML +
            '<div class="WordSection1">' +
            this.processContent(infoTable) +
            this.processContent(reviseTable) +
            '<div class="content">' +
            this.processContent(data.content) +
            '</div>' +
            '</div>';

        // 计算并更新页数
        const pageCount = this.calculatePageCount(exportContainer.innerHTML);
        const pageCountSpan = exportContainer.querySelector('#page-count');
        if (pageCountSpan) {
            pageCountSpan.textContent = "页数：" + pageCount;
        }

        // 兼容 Word 转换：将 colgroup 宽度映射到单元格上（html-docx 不完全支持 colgroup）
        (function applyColgroupWidths(root) {
            const tables = root.querySelectorAll('table');
            tables.forEach(table => {
                const cols = table.querySelectorAll('colgroup col');
                if (!cols.length) return;
                const widths = Array.from(cols).map(col => {
                    const style = col.getAttribute('style') || '';
                    const m = style.match(/width\s*:\s*([0-9.]+)(px|%)/i);
                    if (m) return { value: parseFloat(m[1]), unit: m[2] };
                    const wAttr = col.getAttribute('width');
                    if (wAttr) {
                        if (/^\d+(\.\d+)?%$/.test(wAttr)) return { value: parseFloat(wAttr), unit: '%' };
                        if (/^\d+(\.\d+)?$/.test(wAttr)) return { value: parseFloat(wAttr), unit: 'px' };
                    }
                    return null;
                });
                // 确保 table 固定布局
                table.style.tableLayout = 'fixed';
                table.style.width = table.style.width || '100%';
                const rows = table.tBodies[0] ? table.tBodies[0].rows : table.rows;
                Array.from(rows).forEach(row => {
                    let colIdx = 0;
                    Array.from(row.cells).forEach(cell => {
                        const span = parseInt(cell.getAttribute('colspan') || '1');
                        const slice = widths.slice(colIdx, colIdx + span).filter(Boolean);
                        if (slice.length) {
                            if (slice.every(w => w.unit === '%')) {
                                const sum = slice.reduce((s, w) => s + w.value, 0);
                                cell.style.width = sum + '%';
                            } else {
                                const sumPx = slice.reduce((s, w) => s + (w.unit === 'px' ? w.value : 0), 0);
                                if (sumPx > 0) cell.style.width = sumPx + 'px';
                            }
                        }
                        colIdx += span;
                    });
                });
            });
        })(exportContainer);

        // 兼容 Word 转换：将 tr/td 的行高显式应用到单元格与段落，避免行高丢失
        (function applyRowHeights(root) {
            const toPt = (value, unit) => {
                if (unit === 'pt') return value;
                if (unit === 'px') return value * 0.75; // 1pt ≈ 1.333px
                return null;
            };
            const getHeightFromStyle = (el) => {
                const style = el.getAttribute('style') || '';
                const m = style.match(/height\s*:\s*([0-9.]+)\s*(px|pt)/i);
                if (m) return { pt: toPt(parseFloat(m[1]), m[2].toLowerCase()) };
                const attr = el.getAttribute('height');
                if (attr && /^\d+(\.\d+)?$/.test(attr)) return { pt: toPt(parseFloat(attr), 'px') };
                if (attr && /pt$/i.test(attr)) return { pt: toPt(parseFloat(attr), 'pt') };
                if (attr && /px$/i.test(attr)) return { pt: toPt(parseFloat(attr), 'px') };
                return null;
            };

            root.querySelectorAll('table').forEach(table => {
                table.querySelectorAll('tr').forEach(tr => {
                    // 优先从 tr 取得行高，否则看首个单元格
                    let h = getHeightFromStyle(tr);
                    if (!h) {
                        const firstCell = tr.cells && tr.cells[0];
                        if (firstCell) h = getHeightFromStyle(firstCell);
                    }

                    // 如果没有设置高度，则提供一个默认值，例如 30pt
                    const hPt = (h && h.pt) ? Math.max(h.pt, 1) : 25;

                    Array.from(tr.cells).forEach(cell => {
                        // 将行高作用到单元格与其中段落，保证行高一致性
                        const cellHeight = hPt;
                        const lineHeight = Math.floor(cellHeight * 0.8); // 调整行高以获得更好的垂直对齐

                        cell.style.height = cellHeight + 'pt';
                        cell.style.minHeight = cellHeight + 'pt';
                        cell.style.lineHeight = lineHeight + 'pt';
                        cell.style.paddingTop = '6pt';
                        cell.style.paddingBottom = '6pt';
                        cell.style.verticalAlign = 'middle'; // 确保垂直居中

                        cell.querySelectorAll('p, div').forEach(p => {
                            p.style.lineHeight = lineHeight + 'pt';
                            p.style.margin = '0';
                            p.style.padding = '0';
                        });
                    });
                });
            });
        })(exportContainer);

        try {
            // Word文档配置
            const options = {
                orientation: 'portrait',
                // 边距设置（单位：缇，1英寸=1440缇）
                margins: {
                    top: 1440,     // 上边距1英寸
                    right: 1440,   // 右边距1英寸
                    bottom: 1440,  // 下边距1英寸
                    left: 1440,    // 左边距1英寸
                    footer: 720    // 页脚到页面底部的距离（0.5英寸）
                }
            };

            const handleBlob = (blob) => {
                if (!blob) {
                    throw new Error('生成的文档数据为空');
                }
                const url = window.URL.createObjectURL(blob);
                download_file(url);
                window.URL.revokeObjectURL(url);
                notifier.show('Word文档导出成功', 'success');
            };

            try {
                const result = htmlDocx.asBlob(exportContainer.innerHTML, options);
                if (result && typeof result.then === 'function') {
                    result.then(handleBlob).catch((error) => {
                        notifier.show('文档导出失败，请重试', 'danger');
                    });
                } else {
                    handleBlob(result);
                }
            } catch (error) {
                notifier.show('文档导出失败，请重试', 'danger');
            }
        } catch (error) {
            notifier.show('文档导出失败，请重试', 'danger');
        }
    }

    // Word 导出内容处理，添加表格、列表和段落样式
    processContent(content) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');

        // 处理表格
        doc.querySelectorAll('table').forEach(table => {
            // 添加边框和样式
            table.style.borderCollapse = 'collapse';
            table.style.width = '100%';
            table.style.marginTop = '12pt';
            table.style.marginBottom = '12pt';

            // 处理表格单元格
            table.querySelectorAll('td, th').forEach(cell => {
                cell.style.border = '1px solid black';
                cell.style.padding = '3pt';
                cell.style.verticalAlign = 'middle';
                // cell.style.lineHeight = '16pt'; // 使用固定的pt值
            });
        });

        // 处理列表
        doc.querySelectorAll('ul, ol').forEach(list => {
            list.style.marginTop = '6pt';
            list.style.marginBottom = '6pt';
            list.querySelectorAll('li').forEach(item => {
                item.style.marginBottom = '3pt';
            });
        });

        // 处理段落
        doc.querySelectorAll('p').forEach(p => {
            if (!p.textContent.trim()) {
                p.parentNode?.removeChild(p);
            } else {
                p.style.margin = '6pt 0';
                p.style.lineHeight = '18pt'; // 使用固定的pt值
            }
        });

        // 处理标题
        const headers = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
        headers.forEach(h => {
            doc.querySelectorAll(h).forEach(header => {
                header.style.fontWeight = 'bold';
                switch (h) {
                    case 'h1':
                        header.style.fontSize = '18pt';
                        header.style.margin = '24pt 0';
                        break;
                    case 'h2':
                        header.style.fontSize = '16pt';
                        header.style.margin = '18pt 0 12pt 0';
                        break;
                    case 'h3':
                        header.style.fontSize = '14pt';
                        header.style.margin = '14pt 0 10pt 0';
                        break;
                    default:
                        header.style.fontSize = '12pt';
                        header.style.margin = '12pt 0 8pt 0';
                }
            });
        });

        return doc.body.innerHTML;
    }

    // 导出 PDF（按 A4 自动分页）
    async exportToPDF() {
        const validation = this.validateForm();
        if (!validation.isValid) {
            alert('请完善以下信息后再导出:\n' + validation.errors.join('\n'));
            return;
        }

        const data = this.getFormData();

        // 构建一个用于导出的临时容器，包含基本信息和正文内容
        const exportContainer = document.createElement('div');
        exportContainer.id = 'techbuy-print';
        exportContainer.style.width = '650px';
        exportContainer.style.padding = '20px';
        exportContainer.style.boxSizing = 'border-box';
        exportContainer.style.background = '#ffffff';

        // 计算页数
        const pageCount = this.calculatePageCount(data.content);
        data.pageCount = pageCount; // 保存页数供后续使用

        // 样式，尽量与 Word 保持一致，便于阅读和截图
        const style = document.createElement('style');
        style.textContent = `
            #techbuy-print { font-family: "SimSun", serif; color: #000; }
            #techbuy-print h1 { font-size: 20pt; text-align: center; margin: 0 0 18pt 0; }
            #techbuy-print h2 { font-size: 16pt; margin: 14pt 0 10pt 0; }
            #techbuy-print h3 { font-size: 14pt; margin: 12pt 0 8pt 0; }
            #techbuy-print h4, #techbuy-print h5, #techbuy-print h6 { font-size: 12pt; margin: 10pt 0 6pt 0; }
            #techbuy-print table { border-collapse: collapse; width: 100%; margin: 12pt 0; }
            #techbuy-print th, td { border: 1px solid black; padding: 3pt; text-align: center; vertical-align: middle; line-height: 18pt; }            
            #techbuy-print p { margin: 0; }
        `;

        const signImage = `<img src="/assets/img/sign_name.png" width="90" />`; // 固定宽度，避免签名过大撑破布局
        // 确保页数被正确设置在导出表格中
        const infoTableHtml = this.exportTable({ ...data, pageCount: data.pageCount }, signImage);
        const reviseTable = await this.reviseTable(data);

        exportContainer.innerHTML = `
            ${style.outerHTML}
            ${infoTableHtml}
            ${reviseTable}
            <div class="content">
                ${data.content}
            </div>
        `;

        // 将临时容器加入 DOM，但保持不可见，确保能被 html2canvas 渲染
        exportContainer.style.position = 'fixed';
        exportContainer.style.left = '-9999px';
        document.body.appendChild(exportContainer);

        // 使用与 PDF 切片一致的比例来计算页数，避免与 A4 假定像素的偏差
        try {
            const pageWidthMM = 210; // A4 宽度(mm)
            const pageHeightMM = 297; // A4 高度(mm)
            const margin = 15; // 上下左右边距 (mm)
            const headerHeightMM = 10; // 页眉高度 (mm)
            const imgWidthMM = pageWidthMM - margin * 2;
            const usablePageHeightMM = pageHeightMM - margin * 2 - headerHeightMM;

            // 依据导出容器的实际宽度换算出一页可容纳的 DOM 像素高度
            const pageHeightDomPx = exportContainer.offsetWidth * (usablePageHeightMM / imgWidthMM);
            // scrollHeight 能更完整反映内容高度
            const totalHeightPx = exportContainer.scrollHeight;
            const pages = Math.max(1, Math.ceil(totalHeightPx / pageHeightDomPx));

            const pageCountSpan = exportContainer.querySelector('#page-count');
            if (pageCountSpan) {
                pageCountSpan.textContent = `页数：${pages}`;
            }
        } catch (e) {
            // 忽略页数预估错误，不影响后续导出
        }

        try {
            const print = exportContainer;
            const canvas = await html2canvas(print, { scale: 2, useCORS: true, backgroundColor: '#ffffff', letterRendering: true });

            // 使用 A4 尺寸并自动分页
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({ orientation: 'portrait', unit: 'mm', format: [297, 210] });

            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();
            const margin = 15; // 上下左右边距 (mm)
            const headerHeightMM = 10; // 页眉高度 (mm)

            const imgWidth = pageWidth - margin * 2; // 内容区域宽度 (mm)
            const usablePageHeight = pageHeight - margin * 2 - headerHeightMM; // 内容区域高度 (mm)，预留页眉

            // 一页可容纳的像素高度（将 usablePageHeight(mm) 转换成 canvas 像素）
            const pageHeightPx = Math.floor(canvas.width * (usablePageHeight / imgWidth));
            let position = 0; // 已处理的像素高度
            let pageIndex = 0;

            // 为像素读取优化上下文
            const fullCtx = canvas.getContext('2d', { willReadFrequently: true });

            // 计算一个较安全的分页位置：在目标分页附近寻找更“白”的空行，避免把文字切断
            function findSafeCutY(posStart, tentativeEnd) {
                const searchRange = Math.max(12, Math.floor(pageHeightPx * 0.02)); // 在分页附近上下各扫约2%高度，至少12px
                const startY = Math.max(posStart + Math.floor(pageHeightPx * 0.6), posStart + 50); // 避免页首过早截断
                const minY = Math.max(posStart + 10, tentativeEnd - searchRange);
                const maxY = Math.min(canvas.height - 1, tentativeEnd + searchRange);

                // 计算某一行的“黑度”（非白像素比例），越小越接近空白
                function rowInkRatio(y) {
                    const sampleStepX = Math.max(1, Math.floor(canvas.width / 200)); // 横向抽样，最多~200个点
                    const data = fullCtx.getImageData(0, y, canvas.width, 1).data;
                    let nonWhite = 0, total = 0;
                    for (let x = 0; x < canvas.width; x += sampleStepX) {
                        const idx = (x << 2); // y固定，步进x
                        const r = data[idx];
                        const g = data[idx + 1];
                        const b = data[idx + 2];
                        // 使用亮度阈值判断是否接近白色
                        const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;
                        if (luminance < 250) nonWhite++;
                        total++;
                    }
                    return nonWhite / Math.max(1, total);
                }

                let bestY = tentativeEnd;
                let bestInk = Number.POSITIVE_INFINITY;

                // 先在向下区间搜索，再在向上区间搜索，优先不减少可用空间
                for (let y = tentativeEnd; y <= maxY; y++) {
                    const ink = rowInkRatio(y);
                    if (ink < bestInk) {
                        bestInk = ink;
                        bestY = y;
                        if (ink <= 0.01) break; // 几乎全白，足够安全
                    }
                }
                if (bestInk > 0.02) {
                    for (let y = tentativeEnd - 1; y >= minY; y--) {
                        const ink = rowInkRatio(y);
                        if (ink < bestInk) {
                            bestInk = ink;
                            bestY = y;
                            if (ink <= 0.01) break;
                        }
                    }
                }

                // 确保不要把切点放在本页太靠上位置，至少留出一定高度
                const minSlice = Math.max(80, Math.floor(pageHeightPx * 0.5));
                if (bestY - posStart < minSlice && maxY - posStart >= minSlice) {
                    return Math.max(posStart + minSlice, Math.min(maxY, canvas.height - 1));
                }
                return Math.max(bestY, startY);
            }

            while (position < canvas.height) {
                const tentativeEnd = Math.min(position + pageHeightPx, canvas.height);
                let safeEnd = findSafeCutY(position, tentativeEnd);
                if (safeEnd <= position) safeEnd = Math.min(tentativeEnd, canvas.height);
                const sliceHeight = Math.max(10, safeEnd - position);
                
                // 如果剩余高度太小（小于10像素），跳过创建新页面
                if (sliceHeight < 10 || position >= canvas.height - 5) {
                    break;
                }

                const pageCanvas = document.createElement('canvas');
                pageCanvas.width = canvas.width;
                pageCanvas.height = sliceHeight;
                const ctx = pageCanvas.getContext('2d');
                // 背景填充白色避免透明导致 PDF 显示异常
                ctx.fillStyle = '#ffffff';
                ctx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);
                ctx.drawImage(
                    canvas,
                    0, position, canvas.width, sliceHeight, // 源图裁剪区域
                    0, 0, canvas.width, sliceHeight          // 目标绘制区域
                );

                const imgData = pageCanvas.toDataURL('image/jpeg', 1.0);
                const sliceHeightMM = sliceHeight * imgWidth / canvas.width; // 当前切片在 PDF 中的高度(mm)

                if (pageIndex > 0) pdf.addPage();
                // 绘制页眉
                const headerImage = new Image();
                await new Promise((resolve, reject) => {
                    headerImage.onload = resolve;
                    headerImage.onerror = reject;
                    headerImage.src = '/assets/img/logo_blue.png';  // 使用logo图片
                });

                // 计算图片尺寸，保持宽度在10mm左右
                const imgAspectRatio = headerImage.width / headerImage.height;
                const logoWidthMM = 6; // 设置logo宽度为10mm
                const logoHeightMM = logoWidthMM / imgAspectRatio;

                // 在页眉左侧绘制logo
                pdf.addImage(
                    headerImage,
                    'PNG',
                    margin + 3, // x坐标
                    margin, // y坐标
                    logoWidthMM, // 宽度(mm)
                    logoHeightMM // 高度(mm)
                );
                pdf.addFont('/assets/fonts/simsun.ttf', 'SimSun', 'normal');
                pdf.setFont('SimSun', 'normal');
                pdf.setFontSize(10);
                pdf.text('五星石油-------材料规范', pageWidth / 2, margin + 5, { align: 'center' });
                pdf.setLineWidth(0.2);
                pdf.line(margin, margin + headerHeightMM - 1.5, pageWidth - margin, margin + headerHeightMM - 1.5);
                // 绘制页面主要内容，预留页眉空间
                pdf.addImage(imgData, 'JPEG', margin, margin + headerHeightMM, imgWidth, sliceHeightMM);

                // 添加页脚页码
                pdf.setFont('SimSun', 'normal');
                pdf.setFontSize(10);
                pdf.text(`第 ${pageIndex + 1} 页`, pageWidth / 2, pageHeight - (margin / 2), { align: 'center' });  // 将margin改为margin/2，使页码位置更靠下

                position += sliceHeight;
                pageIndex += 1;
            }

            pdf.save(`五星石油材料规范-${data.specName}-${data.specNumber}.pdf`);
        } catch (e) {
            notifier?.show?.('PDF 导出失败，请重试', 'danger');
        } finally {
            exportContainer.remove();
        }
    }

    // ------------ 导出 Word 和 PDF 结束 ---------------------

    passDocument() {
        alert_confirm('确认审核通过吗？', {
            confirmCallBack: () => {
                fetch(`/pass_techbuy`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(uuid),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content != -1) {
                            disableAllInputs();
                            notifier.show('审核通过', 'success');
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            },
        });
    }

    rejectDocument() {
        alert_confirm('确认拒绝通过吗？', {
            confirmCallBack: () => {
                fetch(`/reject_techbuy`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(uuid),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content != -1) {
                            disableAllInputs();
                            notifier.show('审核拒绝', 'warning');
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            }
        });
    }
}

// 设置所有输入框为只读
function disableAllInputs() {
    // 禁用所有按钮
    document.getElementById('save-button').disabled = true;
    document.getElementById('submit-button').disabled = true;

    // 禁用所有表单输入
    document.getElementById('product-type').disabled = true;
    document.getElementById('material').disabled = true;
    document.getElementById('spec-name').disabled = true;
    document.getElementById('spec-number').disabled = true;
    document.getElementById('build-date').disabled = true;

    // 禁用编辑器
    if (editorInstance) {
        editorInstance.enableReadOnlyMode('editor-locked');

        // 隐藏工具栏
        const toolbar = document.querySelector('#editor-toolbar');
        if (toolbar) {
            toolbar.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function () {
    try {
        // 1. 初始化文档数据
        await initializeDocumentData();

        // 2. 创建TechBuyEditor实例
        const techBuyEditor = new TechBuyEditor();

        // 3. 确保编辑器容器存在且为空
        const editorContainer = document.querySelector('#editor');
        if (!editorContainer) {
            throw new Error('找不到编辑器容器');
        }

        // 清空容器内容
        editorContainer.innerHTML = '';

        // 4. 创建编辑器
        const editor = await DecoupledEditor.create(
            editorContainer,
            createEditorConfig(init_data || defaultTemplate)
        );

        // 粘贴修复：拦截 WPS 的 HTML 数据并规范化
        const clipboardPipeline = editor.plugins.get('ClipboardPipeline');
        clipboardPipeline.on('inputTransformation', (evt, data) => {
            try {
                const html = data?.dataTransfer?.getData('text/html');
                if (!html) return;
                const isWps = /xmlns:\w+=\"urn:schemas-microsoft-com|wps\.|<meta[^>]+WPS|<\w+:[^>]+>/i.test(html);
                if (!isWps) return;
                const normalized = normalizeWpsHtml(html);
                if (normalized && normalized !== html) {
                    data.content = editor.data.htmlProcessor.toView(normalized);
                }
            } catch (_) { /* ignore */ }
        }, { priority: 'high' });

        // 5. 设置工具栏
        const toolbar = document.querySelector('#editor-toolbar');
        if (toolbar && !readonly) {
            toolbar.innerHTML = '';
            toolbar.appendChild(editor.ui.view.toolbar.element);
        }

        // 6. 保存编辑器实例
        editorInstance = editor;

        // 7. 设置编辑器高度
        setupSaveButtonDirtyTracking(editor);
        adjustEditorHeight();

        // 8. 初始化其他功能
        window.addEventListener('resize', adjustEditorHeight);
        initOutlinePanel(editor);
        modal_init();
    } catch (error) {
        const editorContainer = document.querySelector('#editor');
        if (editorContainer) {
            editorContainer.innerHTML = `
                <div class="editor-error" style="
                    padding: 20px;
                    color: #721c24;
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 4px;
                    margin: 10px 0;
                ">
                    <h4>编辑器初始化失败</h4>
                    <p>${error.message || '发生未知错误'}</p>
                    <button onclick="window.location.reload()" class="btn btn-danger btn-sm mt-2">
                        刷新页面重试
                    </button>
                </div>
            `;
        }
        if (typeof notifier !== 'undefined') {
            notifier.show('编辑器初始化失败，请刷新重试', 'danger');
        }
    }
});

