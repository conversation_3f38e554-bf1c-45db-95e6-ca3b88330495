let page_stockinitems = function () {
    let get_height = getHeight() - 133;
    let row_num = Math.floor(get_height / 33);

    //执行日期实例------------------------------------------------
    service.set_date();
    let date1 = document.querySelector('#search-date1').value;
    let date2 = document.querySelector('#search-date2').value;

    //表格搜索----------------------------------------------------
    let init_data = {
        container: '#table-stockin',
        url: `/get_stockin_items`,
        post_data: {
            id: "",
            name: '',
            sort: "日期 DESC, 单号 DESC",
            rec: row_num,
        },
        edit: false,
        header_names: {
            "到货日期": "documents.文本字段5",
            "入库日期": "documents.日期",
            "入库单号": "documents.单号",
            "名称": "split_part(node_name,' ',2)",
            "物料号": "products.物料号",
            "材质": "split_part(node_name,' ',1)",
            "规格": "规格型号",
            "状态": "products.文本字段2",
            "炉批号": "products.文本字段4",
            "长度": "products.整数字段1",
            "执行标准": "products.文本字段3",
            "生产厂家": "products.文本字段5",
            "重量": "库存下限",
            "备注": "documents.备注"
        },

        row_fn: row_fn,
    };

    init_data.post_data.cate = `${date1}${SPLITER}${date2}`;

    tool_table.table_init(init_data);
    tool_table.fetch_table(show_sum);

    //点击搜索按钮
    document.querySelector('#serach-button').addEventListener('click', function () {
        let fields = document.querySelector('#search-fields').value;
        let date1 = document.querySelector('#search-date1').value;
        let date2 = document.querySelector('#search-date2').value;

        init_data.post_data.name = fields;
        init_data.post_data.cate = `${date1}${SPLITER}${date2}`;
        init_data.post_data.page = 1;
        
        tool_table.fetch_table(show_sum);
    });

    function row_fn(tr) {
        let row = tr.split(SPLITER);
        return `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2]}</td><td>${row[3]}</td><td>${row[4]}</td><td>${row[5]}</td>
            <td>${row[6]}</td><td>${row[7]}</td><td>${row[8]}</td><td>${row[9]}</td>
            <td>${row[11]}</td><td>${row[12]}</td><td>${row[10]}</td><td>${row[13]}</td><td>${row[14]}</td></tr>`;
    }

    function show_sum(data) {
        document.querySelector('.sum1').textContent = Number(data[3]).toFixed(0) + " KG";
    }

    document.querySelector('#data-out').addEventListener('click', () => {
        let da1 = document.querySelector('#search-date1').value;
        let da2 = document.querySelector('#search-date2').value;
        let name = document.querySelector('#search-fields').value;
        let data = `${da1}${SPLITER}${da2}${SPLITER}${name}`;
        fetch(`/stockin_excel`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    download_file(`/download/入库明细表.xlsx`);
                    notifier.show('成功导出至 Excel 文件', 'success');
                } else {
                    notifier.show('权限不够，操作失败', 'danger');
                }
            });
    });
}();