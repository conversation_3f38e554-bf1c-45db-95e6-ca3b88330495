let page_home = function () {
    document.querySelector('#title').classList.add('show-chosed');

    var ctx1 = document.getElementById('myChart1').getContext('2d');
    var ctx2 = document.getElementById('myChart2').getContext('2d');

    var currentDate = new Date();
    let da2 = currentDate.Format("yyyy-MM-dd");

    currentDate.setMonth(currentDate.getMonth() - 12);
    let da1 = currentDate.Format("yyyy-MM") + "-01";

    let data1 = {
        statis_cate: "按月",
        date1: da1,
        date2: da2,
    };

    set_chart1(data1);

    set_chart2();

    fetch(`/home_statis`, {
        method: 'post',
        headers: {
            "Content-Type": "application/json",
        },
    })
        .then(response => response.json())
        .then(content => {
            if (content != -1) {
                // 待办单据
                let data1 = {
                    content: content[0],
                    reminder: document.querySelector('#todo-first .reminder table tbody'),
                    title_holer: document.querySelector('#fan-data'),
                    title: `待办 ${content[0].length} 类单据`,
                    alter_func: function () {
                        this.reminder.querySelectorAll('tr').forEach((tr) => {
                            tr.addEventListener('click', () => {
                                let cate = tr.querySelector('td:nth-child(1)').textContent.trim();
                                let q_str;
                                if (cate == "待入库") {
                                    q_str = "/buy_query?s=wait_in";
                                } else if (cate == "待出库") {
                                    q_str = "/sale_query?s=wait_out";
                                } else if (cate == "待发货") {
                                    q_str = "/sale_query?s=wait_trans";
                                } else if (cate == "待收款") {
                                    q_str = "/sale_query?s=wait_money";
                                } else if (cate == "待开票") {
                                    q_str = "/sale_query?s=wait_kp";
                                } else {
                                    q_str = `/other_query?s=${cate}`;
                                }

                                window.location.href = q_str;
                            })
                        });
                    }
                }

                show_reminders(data1);

                //等待审核---------------------------------
                let data2 = {
                    content: content[1],
                    reminder: document.querySelector('#shen-first .reminder table tbody'),
                    title_holer: document.querySelector('#warn-data2'),
                    title: `待审核 ${content[1].length} 类单据`,
                    alter_func: function () {
                        this.reminder.querySelectorAll('tr').forEach((tr) => {
                            tr.addEventListener('click', () => {
                                let cate = tr.querySelector('td:nth-child(1)').textContent.trim();
                                let query = "s=wait_shen" + " " + cate;
                                window.location.href = `${get_address(cate)}?${query}`;
                            })
                        });
                    }
                }

                show_reminders(data2);

                // 未提交审核单据
                get_shen(content[2], "todo-second");

                // 已提交审核单据
                get_shen(content[3], "shen-second");
            }
        });

    function get_shen(rows, table) {
        let trs = "";
        for (let tr of rows) {
            let d = tr.split('　');
            let color = d[0].indexOf("_FS") != -1 ? "red1" : "";
            trs += `<tr class="${color}"><td>${d[0]}</td><td>${d[1]}</td><td width='35%'>${d[2]}</td><td>${d[3]}</td><td>${d[4]}</td></tr>`;
        }

        document.querySelector(`#${table} .reminder table tbody`).innerHTML = trs;

        document.querySelectorAll(`#${table} .reminder table tbody tr`).forEach(row => {
            row.addEventListener('click', function () {
                let dh = row.querySelector('td:nth-child(2)').textContent.trim();
                window.location.href = get_locat(dh);
            })
        });
    }

    function get_locat(dh) {
        let loc;
        if (dh.startsWith("XS")) {
            loc = "/sale/" + dh;
        } else if (dh.startsWith("XT")) {
            loc = "/saleback/" + dh;
        } else if (dh.startsWith("CG")) {
            loc = "/buy_in/" + dh;
        } else if (dh.startsWith("CT")) {
            loc = "/buy_back/" + dh;
        } else if (dh.startsWith("RK")) {
            loc = "/material_in/" + dh;
        } else if (dh.startsWith("CK")) {
            loc = "/material_out/" + dh;
        } else if (dh.startsWith("FH")) {
            loc = "/transport/" + dh;
        } else if (dh.startsWith("TR")) {
            loc = "/stock_change_in/" + dh;
        } else if (dh.startsWith("KP")) {
            loc = "/kp/" + dh;
        } else {
            loc = "/stock_change_out/" + dh;
        }
        return loc;
    }

    function get_address(cate) {
        let address;
        if (cate == "材料采购") {
            address = `/buy_query`;
        } else if (cate == "商品销售") {
            address = `/sale_query`;
        } else if (cate == "销售退货") {
            address = '/sale_query';
        } else if (cate == "采购入库") {
            address = `/change_query_in`;
        } else if (cate == "采购退货") {
            address = `/buy_query`;
        } else if (cate == "销售出库") {
            address = `/change_query_out`;
        } else if (cate == "运输发货") {
            address = `/trans_query`;
        } else if (cate == "调整入库") {
            address = `/stock_query_in`;
        } else if (cate == "调整出库") {
            address = `/stock_query_out`;
        } else if (cate == "销售开票") {
            address = `/kp_query`;
        }
        return address;
    }

    function show_reminders(data) {
        let rows = "";
        for (let i = 0; i < data.content.length; i++) {
            let r = data.content[i].split('　');
            let color = r[0] == "反审单据" ? "red0" : "";
            rows += `<tr class="${color}"><td width="60%">${r[0]}</td><td>${r[1]}</td></tr>`;
        }

        data.title_holer.textContent = data.title;
        data.reminder.innerHTML = rows;
        data.alter_func();
    }

    function set_chart1(data) {
        fetch(`/fetch_statis`, {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    let cont = content[2].map(n => Math.round((n / 10000), 1));
                    new Chart(ctx1,
                        {
                            type: 'bar',
                            data: {
                                labels: content[1],
                                datasets: [{
                                    label: '销售额',
                                    data: cont,
                                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                    borderColor: 'rgba(54, 162, 235, 1)',
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                scales: {
                                    yAxes: [{
                                        ticks: {
                                            beginAtZero: true
                                        }
                                    }],
                                },
                                responsive: true,
                                maintainAspectRatio: false,
                                hover: {
                                    animationDuration: 0  // 防止鼠标移上去，数字闪烁
                                },
                                animation: {           // 这部分是数值显示的功能实现
                                    onComplete: function () {
                                        var chartInstance = this;
                                        let ctx = chartInstance.ctx;
                                        // 以下属于canvas的属性（font、fillStyle、textAlign...）
                                        // ctx.font = Chart.helpers.fontString(Chart.defaults.global.defaultFontSize,
                                        //             Chart.defaults.global.defaultFontStyle,
                                        //             Chart.defaults.global.defaultFontFamily);
                                        ctx.fillStyle = "dark-gray";
                                        ctx.textAlign = 'center';
                                        ctx.textBaseline = 'bottom';

                                        this.data.datasets.forEach(function (dataset, i) {
                                            var meta = chartInstance.controller.getDatasetMeta(i);
                                            meta.data.forEach(function (bar, index) {
                                                var data = dataset.data[index];
                                                ctx.fillText(data, bar._model.x, bar._model.y - 5);
                                            });
                                        });
                                    }
                                }
                            }
                        }
                    );
                }
            });
    }

    function set_chart2() {
        fetch(`/fetch_cost`, {
            method: 'post',
        })
            .then(response => response.json())
            .then(content => {
                if (content != -1) {
                    new Chart(ctx2, {
                        type: 'line',
                        data: {
                            labels: content[0],
                            datasets: [{
                                label: '库存重量',
                                data: content[1],
                                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1,
                                fill: false,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                        }
                    });
                }
            });
    }
}();