let page_productset = function () {
    let global = {
        row_id: 0,
        edit: 0,
        eidt_cate: "",
        product_id: "",
        product_name: "",
        filter_conditions: new Map(),
        filter_sqls: [],
    }

    //配置自动完成和树的显示 ---------------------------------------------------

    let tree_height = document.querySelector('.tree-container').clientHeight;
    let row_num = Math.floor((tree_height - 50) / 30);

    let tree_data = {
        leaf_click: (id, name) => {
            global.product_name = name;
            global.product_id = id;

            document.querySelector('#product-name').textContent = name;
            document.querySelector('#product-id').textContent = id;
            document.querySelector('#search-input').value = "";

            let post_data = {
                id: id,
                name: '',
                filter: '',
                page: 1,
            };

            Object.assign(tool_table.table_data().post_data, post_data);
            tool_table.fetch_table((content) => {
                make_filter();
                add_lu_link();
                show_stat(content);
            });

            // 清除状态
            document.querySelectorAll('.filter_button').forEach(button => {
                button.classList.remove('red');
            });
        }
    }

    tool_tree.tree_init(tree_data);
    let tree_drag = document.querySelector('#user-name').textContent.indexOf("总经理") != -1 ? true : false;
    tool_tree.fetch_tree(stem_click, tree_drag);

    let input = document.querySelector('#auto_input');

    let auto_comp = new AutoInput(input, "", `/tree_auto`, () => {
        tool_tree.tree_search(input.value);
    });

    auto_comp.init();

    document.querySelector("#auto_search").addEventListener('click', () => {
        tool_tree.tree_search(input.value);
    });

    //商品规格表格数据 -------------------------------------------------------------------

    service.build_product_table(row_num, make_filter, add_lu_link, show_stat);

    // 点击树的 stem 显示统计信息
    function show_statistic(cate) {
        fetch("/fetch_statistic", {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: cate,
        })
            .then(response => response.json())
            .then(content => {
                let obj = {
                    "3": "圆钢",
                    "4": "无缝钢管",
                };

                document.querySelector('.info-show').textContent = `${obj[cate]}长度合计：${content.库存长度} 米，重量合计：${content.库存重量} KG`;
            });
    }

    // 显示统计信息，作为 fetch_table 的回调函数
    function show_stat(content) {
        let long = content[3] == 0 && content[4] != 0 ? "< 1" : content[3];
        document.querySelector('.info-show').textContent = `长度合计：${long} 米，重量合计：${content[4]} KG`;
    }

    // 点击树的 stem
    function stem_click() {
        let all_stem = document.querySelectorAll('.item-down');
        all_stem.forEach(stem => {
            stem.addEventListener('click', function () {
                let cate = this.textContent.trim() == "圆钢" ? "3" : "4";
                show_statistic(cate);
            });
        })
    }

    // 给炉号加入连接, 同时给入库单号加入连接
    function add_lu_link() {
        let trs = document.querySelectorAll('.table-product tbody tr');
        service.get_lu(trs);

        for (let tr of trs) {
            // 入库单号连接
            let rk = tr.querySelector('.入库单号');
            if (rk && rk.textContent.trim() != "KT202312-01") {
                let url = rk.textContent.trim().startsWith("RK") ? "/material_in/" : "/stock_change_in/";
                rk.innerHTML = `<a href="${url}${rk.textContent.trim()}" title="点击查阅单据">${rk.textContent.trim()}</a>`;
            }

            // 设置锁定行颜色
            let lb = tr.querySelector('.库存类别');
            let p = document.querySelector('#p-select');
            if (p && p.value != "销售锁定" && lb && lb.textContent.trim() == '锁定') {
                tr.classList.add('red');
            }
        }
    }

    // ------------------------------------ 过滤部分开始--------------------------------------------

    // 使用通用过滤器
    const tableFilter = initTableFilter({
        getThs: () => document.querySelectorAll('.table-container thead th'),
        has_filter: ['规格', '状态', '执行标准', '生产厂家', '炉批号', '库存长度', '区域'],
        url: '/fetch_filter_items',
        state: global,
        position: 'cursor',
        buildFetchPostData: ({ name, baseFilterSql }) => {
            let search = document.querySelector('#search-input').value;
            let cate = document.querySelector('#p-select').value;
            let id = document.querySelector('#product-id').textContent.trim();
            return {
                id: id,
                name: search,
                cate: cate,
                filter_name: name,
                filter: baseFilterSql,
            };
        },
        onRefreshTable: (filterSql) => {
            document.querySelector('.f-choose').innerHTML = '';
            Object.assign(tool_table.table_data().post_data, { filter: filterSql, page: 1 });
            tool_table.fetch_table((content) => {
                make_filter();
                add_lu_link();
                show_stat(content);
            });
        },
    });

    function make_filter() {
        tableFilter.ensureButtons();
        tableFilter.updateButtonColors();
    }

    // ------------------------------- 过滤部分结束 --------------------------------

    // 筛选当前库存
    document.querySelector('#p-select').addEventListener('change', () => {
        let post_data = {
            id: document.querySelector('#product-id').textContent.trim(),
            name: document.querySelector('#search-input').value,
            cate: document.querySelector('#p-select').value,
            page: 1,
        };

        Object.assign(tool_table.table_data().post_data, post_data);
        tool_table.fetch_table((content) => {
            make_filter();
            add_lu_link();
            show_stat(content);
        });
    });

    // ------------------------------- 过滤部分结束 --------------------------------

    //增加按键
    document.querySelector('#add-button').addEventListener('click', function () {
        global.eidt_cate = "add";

        if (global.product_name != "") {
            document.querySelector('.modal-body').innerHTML = service.build_add_form(service.table_fields());
            document.querySelector('.modal-title').textContent = global.product_name;
            document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;"
            document.querySelector('.modal').style.display = "block";
            document.querySelector('.modal-body input').focus();
            leave_alert();
        } else {
            notifier.show('请先选择商品', 'danger');
        }
    });

    //查阅出库按键
    document.querySelector('#find-button').addEventListener('click', function () {
        global.eidt_cate = "add";
        let chosed = document.querySelector('tbody .focus');
        let id = chosed ? chosed.querySelector('td:nth-child(2)').textContent.trim() : "";
        let p_name = chosed ? chosed.querySelector('.名称').textContent.trim() : "";
        let sale_lock = chosed ? chosed.querySelector('.库存类别').textContent.trim() : "";

        if (global.product_name == "") {
            global.product_name = p_name;
        }

        if (global.product_name != "" && id != "") {
            id = sale_lock != "锁定" ? id : id + "#" + sale_lock;

            fetch('/fetch_pout_items', {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: id,
            })
                .then(response => response.json())
                .then(content => {
                    let html = `
                    <div class="table-container table-pout">
                        <table>
                            <thead>
                                <tr><th width="6%">序号</th><th width="10%">单据类别</th><th width="15%">日期</th><th width="15%">单号</th>
                                <th width="8%">切分数量</th><th width="8%">总长度</th><th width="10%">实际重量</th><th width="13%">备注</th></tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>                        
                    </div>`;

                    let tds = "";
                    let n = 1;

                    let addr = {
                        "销售出库": "/material_out/",
                        "调整出库": "/stock_change_out/",
                        "商品销售": "/sale/"
                    }

                    for (let row of content) {
                        tds += `<tr><td>${n++}</td><td>${row.cate}</td><td>${row.date}</td><td><a href="${addr[row.cate]}${row.dh}" target="_blank">${row.dh}</a></td>
                            <td>${row.num}</td><td>${row.all_long}</td><td>${row.weight.toFixed(1)}</td><td>${row.note}</td></tr>`;
                    }

                    for (let i = 0; i < 15 - n + 1; i++) {
                        tds += `<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>`;
                    }

                    let gg = chosed.querySelector('.规格').textContent;
                    let status = chosed.querySelector('.状态').textContent;
                    let now_long = chosed.querySelector('.库存长度').textContent;

                    document.querySelector('.modal-body').innerHTML = html;
                    document.querySelector('.modal-body .table-pout tbody').innerHTML = tds;
                    document.querySelector('.modal-title').textContent = `${id}：${global.product_name}　${gg}　${status}，库存长度：${now_long}`;
                    document.querySelector('.modal-dialog').style.cssText = "max-width: 800px;";
                    document.querySelector('.modal').style.display = "block";
                    document.querySelector('#modal-sumit-button').style.display = "none";
                });
        } else {
            notifier.show('请先选择物料', 'danger');
        }
    });

    //全部属性
    document.querySelector('#all-button').addEventListener('click', function () {
        let chosed = document.querySelector('tbody .focus');
        let id = chosed ? chosed.querySelector('td:nth-child(2)').textContent : "";
        let p_name = chosed ? chosed.querySelector('.名称').textContent.trim() : "";
        let p_id = chosed ? chosed.querySelector('.商品id').textContent.trim() : "";

        if (global.product_name == "") {
            global.product_name = p_name;
        }

        if (global.product_id == "") {
            global.product_id = p_id;
        }
        if (global.product_name != "" && id != "") {
            fetch('/fetch_all_info', {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: id,
            })
                .then(response => response.json())
                .then(content => {
                    let fields = ["物料号", "规格", "状态", "执行标准", "炉批号", "生产厂家", "切分", "库存长度", "库存重量",
                        "入库长度", "外径壁厚", "入库单号", "入库日期", "入库方式", "原因", "库位", "库存类别", "区域", "备注"];

                    let form = "<form>";
                    for (let name of fields) {
                        let control;
                        control = `
                        <div class="form-group">
                            <div class="form-label">
                                <label>${name}</label>
                            </div>
                            <input class="form-control input-sm has-value" type="text" value="${content[name]}" readonly>
                        </div>`;

                        form += control;
                    }

                    form += "</form>";

                    global.row_id = id;
                    document.querySelector('.modal-body').innerHTML = form;
                    document.querySelector('.modal-title').textContent = global.product_name;
                    document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;";
                    document.querySelector('#modal-sumit-button').style.display = "none";
                    document.querySelector('.modal').style.display = "block";
                });
        } else {
            notifier.show('请先选择物料', 'danger');
        }
    });

    //编辑按键
    if (document.querySelector('#edit-button')) {
        document.querySelector('#edit-button').addEventListener('click', function () {
            global.eidt_cate = "edit";

            let chosed = document.querySelector('tbody .focus');
            let id = chosed ? chosed.querySelector('td:nth-child(2)').textContent : "";
            let p_name = chosed ? chosed.querySelector('.名称').textContent.trim() : "";
            let p_id = chosed ? chosed.querySelector('.商品id').textContent.trim() : "";

            if (global.product_name == "") {
                global.product_name = p_name;
            }

            if (global.product_id == "") {
                global.product_id = p_id;
            }

            if (global.product_name != "" && global.product_id != "" && id != "") {
                let fields = [{
                    "show_name": "物料号",
                    "ctr_type": "普通输入",
                    "is_use": false
                },
                {
                    "show_name": "状态",
                    "ctr_type": "普通输入",
                    "is_use": true
                },
                {
                    "show_name": "炉批号",
                    "ctr_type": "普通输入",
                    "is_use": true
                },
                {
                    "show_name": "入库长度",
                    "ctr_type": "普通输入",
                    "is_use": true
                },
                {
                    "show_name": "库位",
                    "ctr_type": "普通输入",
                    "is_use": true
                },
                {
                    "show_name": "库存类别",
                    "ctr_type": "下拉列表",
                    "option_value": "正常销售_自用_不合格_已切完",
                    "is_use": true
                },
                {
                    "show_name": "备注",
                    "ctr_type": "普通输入",
                    "is_use": true
                }];

                let form = "<form>";
                for (let name of fields) {
                    let control;
                    let dis = !name.is_use ? "disabled" : "";

                    if (name.ctr_type == "普通输入") {
                        let value = chosed.querySelector(`.${name.show_name}`).textContent;
                        control = `<div class="form-group">
                            <div class="form-label">
                                <label>${name.show_name}</label>
                            </div>
                            <input class="form-control input-sm has-value ${name.show_name}" type="text" value="${value}" ${dis}>
                        </div>`;
                    } else {
                        let show_value = chosed.querySelector(`.${name.show_name}`).textContent;
                        control = `<div class="form-group">
                            <div class="form-label">                                    
                                <label>${name.show_name}</label>
                            </div>
                            <select class='select-sm has-value'>`;

                        let options = name.option_value.split('_');
                        for (let value of options) {
                            if (value == show_value) {
                                control += `<option value="${value}" selected>${value}</option>`;
                            } else {
                                control += `<option value="${value}">${value}</option>`;
                            }
                        }

                        control += "</select></div>";
                    }

                    form += control;
                }
                form += "</form>";

                global.row_id = id;
                document.querySelector('.modal-body').innerHTML = form;

                // 上传炉号质保单
                let lu_button_html = `<button class="btn btn-info btn-sm zhibao_button">质保书</button><input type="file" id="lu_upload" accept="application/pdf" hidden="hidden"/>`;
                let lu_input = document.querySelector('.modal-body .炉批号');
                lu_input.style.width = "230px";
                lu_input.insertAdjacentHTML('afterend', lu_button_html);

                let lu_button = document.querySelector('.modal-body .zhibao_button');
                lu_button.addEventListener('click', function (e) {
                    e.preventDefault();
                    if (lu_input.value.trim() == "") {
                        notifier.show('炉批号不能为空', 'danger');
                        return false;
                    }
                    lu_upload.click();
                });

                //上传 pdf 文件
                lu_upload.addEventListener('change', () => {
                    let focused = document.querySelector('.table-product .focus');
                    let lh = `${focused.querySelector('.材质').textContent.trim()}_${focused.querySelector('.规格').textContent.trim()}_${focused.querySelector('.炉批号').textContent.trim()}__${focused.querySelector('.生产厂家').textContent.trim()}`;

                    lu_button.disabled = true;
                    const fd = new FormData();
                    fd.append('file', lu_upload.files[0]);
                    fetch(`/pdf_in`, {
                        method: 'POST',
                        body: fd,
                    })
                        .then(res => res.json())
                        .then(content => {
                            if (content == -3) {
                                notifier.show('文件大小不能超过1M', 'danger');
                                lu_button.disabled = "";
                                return false;
                            }

                            fetch(`/pdf_in_save`, {
                                method: 'post',
                                headers: {
                                    "Content-Type": "application/json",
                                },
                                body: JSON.stringify(lh)
                            })
                                .then(response => response.json())
                                .then(content => {
                                    notifier.show('质保书成功保存', 'success');
                                    lu_button.disabled = "";
                                });
                        });
                });

                document.querySelector('.modal-title').textContent = "编辑物料 - " + global.product_name;
                document.querySelector('.modal-dialog').style.cssText = "max-width: 500px;";
                document.querySelector('.modal').style.display = "block";
                document.querySelector('.modal-body input').focus();
                leave_alert();
            } else {
                notifier.show('请先选择物料', 'danger');
            }
        });
    }

    //提交按键
    document.querySelector('#modal-sumit-button').addEventListener('click', function () {
        if (document.querySelector('.modal-title').textContent.indexOf('编辑') != -1) {
            if (global.eidt_cate == "add" || global.eidt_cate == "edit") {
                let all_input = document.querySelectorAll('.has-value');
                if (all_input[0].value != "") {
                    if (!regReal.test(document.querySelector('.modal-body .入库长度').value)) {
                        notifier.show('数字字段输入错误', 'danger');
                        return false;
                    }

                    let product = `${global.row_id}${SPLITER}${global.product_id}${SPLITER}`;

                    for (let input of all_input) {
                        product += `${input.value}${SPLITER}`;
                    }

                    let data = {
                        data: product,
                    };

                    let url = global.eidt_cate == "edit" ? `/update_product` : `/add_product`;

                    fetch(url, {
                        method: 'post',
                        headers: {
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify(data),
                    })
                        .then(response => response.json())
                        .then(content => {
                            if (content == 1) {
                                global.edit = 0;
                                modal_out_data.edit = 0;
                                notifier.show('商品修改成功', 'success');

                                tool_table.fetch_table((content) => {
                                    make_filter();
                                    add_lu_link();
                                    show_stat(content);

                                });

                                if (global.eidt_cate == "add") {
                                    for (let input of all_input) {
                                        input.value = "";
                                    }
                                }
                                close_modal();
                            } else {
                                notifier.show('权限不够，操作失败', 'danger');
                            }
                        });
                } else {
                    notifier.show('空值不能提交', 'danger');

                }
            } else {
                let url = global.eidt_cate == "批量导入" ? `/product_datain` : `/product_updatein`;
                fetch(url, {
                    method: 'post',
                    body: global.product_id,
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content == 1) {
                            notifier.show('批量操作成功', 'success');
                            tool_table.fetch_table((content) => {
                                make_filter();
                                add_lu_link();
                                show_stat(content);
                            });
                            close_modal();
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            }
        } else {    // 选择导出数据
            document.querySelector('.modal').style.display = "none";
            let inputs = document.querySelectorAll('.fields-out input[type=checkbox]');
            let names = "";
            inputs.forEach(input => {
                if (input.checked) {
                    names += input.nextElementSibling.textContent.trim() + "#";
                }
            });

            let id = global.eidt_cate == "全部导出" ? "all" : document.querySelector('#product-id').textContent.trim();
            let p_name = global.eidt_cate == "全部导出" ? "全部库存" : document.querySelector('#product-name').textContent.trim();

            let data = {
                id: id,
                name: p_name,
                fields: names,
                cate: document.querySelector('#p-select').value,
                filter: tableFilter.buildFilterString(),
                search: document.querySelector('#search-input').value,
            };

            // console.log(data);

            fetch(`/product_out`, {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            })
                .then(response => response.json())
                .then(content => {
                    if (content != -1) {
                        download_file(`/download/${content}.xlsx`);
                        notifier.show('成功导出至 Excel 文件', 'success');
                    } else {
                        notifier.show('权限不够，操作失败', 'danger');
                    }
                });
        }
    });

    modal_init();

    //数据导入和导出 ------------------------------------------------------------------------------

    document.querySelector('#data-out').addEventListener('click', function () {
        global.product_name = document.querySelector('#product-name').textContent.trim();
        if (global.product_name != "") {
            global.eidt_cate = "类别导出";
            choose_fields();
        } else {
            notifier.show('请先选择商品', 'danger');
        }
    });

    document.querySelector('#data-all-out').addEventListener('click', function () {
        global.product_name = "全部商品";
        global.eidt_cate = "全部导出";
        choose_fields();
    });

    function choose_fields() {
        let fields = [
            {
                name: "名称",
                checked: "checked"
            },
            {
                name: "材质",
                checked: "checked"
            },
            {
                name: "物料号",
                checked: "checked"
            },
            {
                name: "规格",
                checked: "checked"
            },
            {
                name: "状态",
                checked: "checked"
            },
            {
                name: "执行标准",
                checked: "checked"
            },
            {
                name: "炉批号",
                checked: "checked"
            },
            {
                name: "切分",
                checked: ""
            },
            {
                name: "库存长度",
                checked: "checked"
            },
            {
                name: "理论重量",
                checked: "checked"
            },
            {
                name: "入库长度",
                checked: ""
            },
            {
                name: "外径壁厚",
                checked: ""
            },
            {
                name: "入库单号",
                checked: ""
            },
            {
                name: "入库日期",
                checked: ""
            },
            {
                name: "入库方式",
                checked: ""
            },
            {
                name: "原因",
                checked: ""
            },
            {
                name: "库位",
                checked: ""
            },
            {
                name: "库存类别",
                checked: ""
            },
            {
                name: "区域",
                checked: ""
            },
            {
                name: "备注",
                checked: "checked"
            }
        ];
        let form = "<form class='form-out'>";
        for (let f of fields) {
            let control;
            control = `
                <div class="form-group fields-out">
                    <label class="check-radio">
                        <input type="checkbox" ${f.checked}>
                        <span>${f.name}</span>
                        <span class="checkmark"></span>
                    </label>
                </div>`;

            form += control;
        }

        form += "</form>";

        document.querySelector('.modal-body').innerHTML = form;
        document.querySelector('.modal-title').textContent = global.product_name + " - 请选择导出字段：";
        document.querySelector('.modal-dialog').style.cssText = "max-width: 300px;";
        document.querySelector('.modal').style.display = "block";
    }

    //批量导入
    let fileBtn = document.getElementById('choose_file');

    document.getElementById('data-in').addEventListener('click', function () {
        if (global.product_name == "") {
            notifier.show('请先选择商品分类', 'danger');
            return false;
        }
        fileBtn.click();
    });

    fileBtn.addEventListener('change', () => {
        data_in(fileBtn, "将追加", "追加新数据，同时保留原数据", "批量导入");
    });

    //批量更新
    let fileBtn_update = document.getElementById('choose_file2');

    document.getElementById('data-update').addEventListener('click', function () {
        if (global.product_name == "") {
            notifier.show('请先选择商品分类', 'danger');
            return false;
        }
        fileBtn_update.click();
    });

    fileBtn_update.addEventListener('change', () => {
        data_in(fileBtn_update, "将更新", "更新数据，原数据将被替换，请谨慎操作！", "批量更新");
    });

    function data_in(fileBtn, info1, info2, cate) {
        if (checkFileType(fileBtn)) {
            const fd = new FormData();
            fd.append('file', fileBtn.files[0]);
            fetch(`/product_in`, {
                method: 'POST',
                body: fd,
            })
                .then(res => res.json())
                .then(content => {
                    if (content != -1 && content != -2) {
                        let rows = "<div class='table-container table-product'><table style='font-size: 12px;'><thead>";
                        let n = 1;
                        for (let item of content[0]) {
                            let arr_p = item.split("<`*_*`>");
                            let row;
                            if (n == 1) {
                                row = `<tr>`;
                                for (let i = 0; i < arr_p.length - 1; i++) {
                                    row += `<th>${arr_p[i]}</th}>`;
                                }
                                row += "</tr></thead><tbody>";
                                n = 2;
                            } else {
                                row = `<tr>`;
                                for (let i = 0; i < arr_p.length - 1; i++) {
                                    row += `<td>${arr_p[i]}</td>`;
                                }
                                row += "</tr>";
                            }

                            rows += row;
                        }
                        rows += "</tbody></table></div>";
                        document.querySelector('.modal-body').innerHTML = rows;

                        let message = content[2] > 50 ? " (仅显示前 50 条）" : "";
                        document.querySelector('.modal-title').innerHTML = `${global.product_name} ${info1} ${content[1]} 条数据${message}：`;
                        document.querySelector('#modal-info').innerHTML = `${global.product_name} ${info2}`;

                        global.eidt_cate = cate;

                        document.querySelector('.modal-dialog').style.cssText = "max-width: 1200px;";
                        document.querySelector('.modal').style.cssText = "display: block";
                        fileBtn.value = "";

                    } else if (content == -1) {
                        notifier.show('缺少操作权限', 'danger');
                    } else {
                        notifier.show('excel 表列数不符合', 'danger');
                    }
                });
        } else {
            notifier.show('需要 excel 文件', 'danger');
        }
    }
}();