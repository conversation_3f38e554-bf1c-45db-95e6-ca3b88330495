let page_visit = function () {
    let cus_cate = document.querySelector('#category').textContent;

    let get_height = getHeight() - 198;
    let row_num = Math.floor(get_height / 30);

    let init_data = {
        container: '.table-customer',
        url: `/fetch_visit`,
        post_data: {
            id: "",
            name: '',
            sort: "max(last_visit) DESC",
            rec: row_num,
            cate: ''
        },
        edit: false,

        row_fn: table_row,
        blank_row_fn: blank_row,
    };

    let table = document.querySelector('.table-customer');
    let custom_fields = [{ name: '序号', width: 3 }, { name: '用户名', field: 'max(username)', width: 6 },
    { name: '客户名称', field: 'max(名称)', width: 12 }, { name: '最近访问', field: 'max(last_visit)', width: 10 },
    { name: '今日查询', field: 'SUM(case when visit_date::DATE = CURRENT_DATE then num else 0 end)', width: 6 },
    { name: '近一月查询', field: "SUM(case when visit_date::DATE >= CURRENT_DATE - interval '1 month' then num else 0 end)", width: 6 },
    { name: '近一年查询', field: "SUM(case when visit_date::DATE >= CURRENT_DATE - interval '1 year' then num else 0 end)", width: 6 },
    ];

    let data = service.build_table_header(table, custom_fields, []);
    table.querySelector('thead tr').innerHTML = data.th_row;

    init_data.header_names = data.header_names;

    tool_table.table_init(init_data);
    tool_table.fetch_table();

    function table_row(tr) {
        let row = tr.split(SPLITER);
        return `<tr><td>${row[0]}</td><td>${row[1]}</td><td>${row[2]}</td><td>${row[3]}</td><td>${row[4]}</td>
        <td>${row[5]}</td><td>${row[6]}</td></tr>`;
    }

    function blank_row() {
        return `<tr><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>`;
    }

    //搜索客户供应商
    let search_input = document.querySelector('#search-input');

    let auto_comp = new AutoInput(search_input, document.querySelector('#category'), "customer_auto", () => {
        search_table();
    });

    auto_comp.init();

    document.querySelector('#serach-button').addEventListener('click', function () {
        search_table();
    });

    if (document.querySelector('#cate-select')) {
        document.querySelector('#cate-select').addEventListener('change', function () {
            Object.assign(tool_table.table_data().post_data, { cate: cus_cate + "#" + this.value, name: '', page: 1 });
            tool_table.fetch_table();
        });
    }

    function search_table() {
        let search = document.querySelector('#search-input').value;
        Object.assign(tool_table.table_data().post_data, { name: search, page: 1 });
        tool_table.fetch_table();
    }
}();
