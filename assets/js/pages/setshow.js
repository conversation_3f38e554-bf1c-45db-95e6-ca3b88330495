let page_setshow = function () {

    tool_tree.tree_init();
    tool_tree.fetch_tree(make_tree);

    // 构建树HTML，为每个节点添加checkbox
    function make_tree(data) {
        let nodes = document.querySelectorAll('.leaf');

        nodes.forEach(node => {
            let id = node.id.replace('t_', '');

            // 创建checkbox标签
            let label = document.createElement('label');
            label.className = 'check-radio';
            label.innerHTML = `
                <input type="checkbox" class="form-check" id="check_${id}" checked>
                <span class="checkmark"></span>
            `;

            node.appendChild(label);

            node.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                // 切换checkbox状态
                let checkbox = document.querySelector(`#check_${id}`);
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                    checkbox.parentElement.parentElement.classList.toggle('not-show');
                }
            });
        });

        let all_items = data[0].children.concat(data[1].children);
        all_items.forEach(item => {
            if (!item.show) {
                const not_show = document.querySelector(`#check_${item.num}`);
                not_show.checked = false;
                not_show.parentElement.parentElement.classList.add('not-show');

            }
        });
    }

    // 搜索功能
    let input = document.querySelector('#auto_input');
    let auto_comp = new AutoInput(input, "", `/tree_auto`, () => {
        tool_tree.tree_search(input.value);
    });
    auto_comp.init();

    document.querySelector("#auto_search").addEventListener('click', () => {
        tool_tree.tree_search(input.value);
    });

    // 保存设置
    document.querySelector('#submit-button').addEventListener('click', () => {
        let check_true = [];
        let check_false = [];
        document.querySelectorAll('.form-check').forEach(checkbox => {
            if (checkbox.checked == true) {
                check_true.push(checkbox.id.replace('check_', ''));
            } else {
                check_false.push(checkbox.id.replace('check_', ''));
            }
        });

        let data = `${check_true.join(',')}#${check_false.join(',')}`;      

        fetch('/save_tree_show', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
            .then(response => response.json())
            .then(result => {
                if (result == 1) {
                    notifier.show('保存成功', 'success');
                } else {
                    notifier.show('保存失败', 'danger');
                }
            });
    });
}();

