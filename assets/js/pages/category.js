let category_manage = function () {
    let get_height = getHeight() - 100;
    document.querySelector('.table-price').style.height = get_height + "px";
    let categories;

    fetch(`/fetch_product_cate`, {
        method: 'get',
    })
        .then(response => response.json())
        .then(content => {
            if (content != -1) {
                categories = content;
                loadCategories();
            }
        });

    let currentEditId = null;
    let draggedElement = null;

    function loadCategories() {
        const tbody = document.getElementById('sortable-list');
        tbody.innerHTML = '';

        categories.forEach((category, index) => {
            const row = createCategoryRow(category);
            tbody.appendChild(row);
        });

        bindRowEvents();
    }

    function createCategoryRow(category) {
        const row = document.createElement('tr');
        row.setAttribute('data-id', category.id);
        row.draggable = true;

        row.innerHTML = `
            <td hidden>${category.id}</td>
            <td class="order-cell">${category.order}</td>
            <td class="name-cell">${category.name}</td>
        `;

        return row;
    }

    function bindRowEvents() {
        const rows = document.querySelectorAll('#sortable-list tr');

        rows.forEach(row => {
            row.addEventListener('click', function () {
                rows.forEach(r => r.classList.remove('focus'));
                row.classList.add('focus');
            });

            row.addEventListener('dragstart', function (e) {
                draggedElement = row;
                row.style.opacity = '0.5';
                e.dataTransfer.effectAllowed = 'move';
            });

            row.addEventListener('dragend', function (e) {
                row.style.opacity = '';
                draggedElement = null;
            });

            row.addEventListener('dragover', function (e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });

            row.addEventListener('drop', function (e) {
                e.preventDefault();
                if (draggedElement && draggedElement !== row) {
                    const tbody = document.getElementById('sortable-list');
                    const draggedIndex = Array.from(tbody.children).indexOf(draggedElement);
                    const targetIndex = Array.from(tbody.children).indexOf(row);

                    reorderCategories(draggedIndex, targetIndex);
                }
            });
        });
    }

    function reorderCategories(fromIndex, toIndex) {
        const movedCategory = categories.splice(fromIndex, 1)[0];
        categories.splice(toIndex, 0, movedCategory);

        categories.forEach((category, index) => {
            category.order = index + 1;
        });

        // loadCategories();

        fetch("/order_cate", {
            method: 'post',
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(categories),
        })
            .then(response => response.json())
            .then(content => {
                if (content == 1) {
                    loadCategories();
                    notifier.show('类别排序成功', 'success');
                } else {
                    notifier.show('操作失败', 'danger');
                }
            });
    }

    function showModal(title, category = null) {
        const modal = document.querySelector('.modal');
        const modalTitle = document.querySelector('.modal-title');

        modalTitle.textContent = title;
        currentEditId = category ? category.id : null;

        const formHtml = `
            <form id="category-form">
                <div class="form-group">
                    <label for="category-name" style="width: 80px;">类别名称:</label>
                    <input type="text" class="form-control" id="category-name" placeholder="请输入类别名称" value="${category ? category.name : ''}" required>
                </div>
            </form>
        `;

        document.querySelector('.modal-body').innerHTML = formHtml;
        document.querySelector('.modal-dialog').style.cssText = "max-width: 400px; margin-top: 170px;";
        document.querySelector('#modal-sumit-button').style.display = "inline-block";

        modal.style.display = 'block';
        document.getElementById('category-name').focus();
    }

    function closeModal() {
        const modal = document.querySelector('.modal');
        modal.style.display = 'none';
        currentEditId = null;
    }

    window.editCategoryName = function (categoryId) {
        const category = categories.find(c => c.id === categoryId);
        if (category) {
            showModal('编辑类别', category);
        }
    }

    document.getElementById('add-button').addEventListener('click', function () {
        showModal('新增类别');
    });

    document.getElementById('edit-button').addEventListener('click', function () {
        const focusedRow = document.querySelector('#sortable-list tr.focus');
        if (!focusedRow) {
            notifier.show('请先选择要编辑的类别', 'danger');
            return;
        }

        const categoryId = parseInt(focusedRow.getAttribute('data-id'));
        const category = categories.find(c => c.id === categoryId);

        showModal('编辑类别', category);
    });

    document.getElementById('modal-sumit-button').addEventListener('click', function () {
        const categoryName = document.getElementById('category-name');
        if (!categoryName) return; // 如果不是类别编辑模态框，直接返回

        const name = categoryName.value.trim();

        if (!name) {
            notifier.show('请输入类别名称', 'danger');
            return;
        }

        const existingCategory = categories.find(c =>
            c.name === name && c.id !== currentEditId
        );

        if (existingCategory) {
            notifier.show('类别名称已存在', 'danger');
            return;
        }

        if (currentEditId) {
            const category = categories.find(c => c.id === currentEditId);
            category.name = name;

            fetch("/edit_cate", {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    id: currentEditId,
                    name: name
                }),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        loadCategories();
                        notifier.show('类别编辑成功', 'success');
                    } else {
                        notifier.show('操作失败', 'danger');
                    }
                });
        } else {
            const newCategory = {
                name: name,
                order: categories.length + 1
            };

            fetch("/create_cate", {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(newCategory),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == 1) {
                        categories.push(newCategory);
                        loadCategories();
                        notifier.show('类别新增成功', 'success');
                    } else {
                        notifier.show('操作失败', 'danger');
                    }
                });
        }

        closeModal();
    });

    modal_init();
}();