let page_documentquery = function () {
    const get_height = getHeight() - 168;
    const row_num = Math.floor(get_height / 30);

    const init_data = {
        container: '.table-documents',
        url: `/fetch_anti_shen`,
        post_data: {
            id: "",
            name: '',
            sort: '反审日期 desc',
            rec: row_num,
            cate: '',
        },
        edit: false,
        header_names: {
            "单号": "单号",
            "类别": "类别",
            "日期": "日期",
            "经办人": "经办人",
            "审核": "文本字段10",
            "反审人": "反审人",
            "反审日期": "反审日期",
        },

        row_fn: table_row,
    };

    // 执行日期实例
    service.set_date();
    const { value: date1 } = document.querySelector('#search-date1');
    const { value: date2 } = document.querySelector('#search-date2');
    init_data.post_data.cate = `${date1}${SPLITER}${date2}`;

    tool_table.table_init(init_data);
    tool_table.fetch_table();

    function table_row(tr) {
        let addr = service.dh_cate(tr.单号);
        let shen = tr.提交审核 ? '是' : '否';
        return `
        <tr>
            <td>${tr.序号}</td>
            <td class="单号"><a href="${addr}" target="_blank" title="查阅编辑">${tr.单号}</a></td>
            <td class="类别">${tr.类别}</td>
            <td class="日期">${tr.日期}</td>
            <td class="经办人">${tr.经办人}</td>
            <td class="提交审核">${shen}</td>
            <td class="审核">${tr.审核}</td>
            <td class="反审人">${tr.反审人}</td>
            <td class="反审日期">${tr.反审日期}</td>
            <td class="区域">${tr.区域}</td>
            <td class="备注">${tr.备注}</td>
        </tr>
        `;
    }

    document.querySelector('#serach-button').addEventListener('click', () => {
        search_table();
    });

    function search_table() {
        const { value: search } = document.querySelector('#search-input');
        const { value: date1 } = document.querySelector('#search-date1');
        const { value: date2 } = document.querySelector('#search-date2');
        Object.assign(tool_table.table_data().post_data, { name: search, cate: `${date1}${SPLITER}${date2}`, page: 1 });
        tool_table.fetch_table();
    }
}();