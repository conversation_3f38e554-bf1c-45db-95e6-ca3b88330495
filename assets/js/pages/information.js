let page_information = function () {
    let rows = Math.floor((document.querySelector('body').clientHeight - 395) / 17);
    document.querySelector('#infor-show textarea').setAttribute('rows', rows);

    document.querySelector('#info-submit').addEventListener('click', function () {
        let title = document.querySelector('#infor-title').value.trim();
        let content = document.querySelector('#infor-textarea').value.trim();

        if (title == '' || content == '') {
            notifier.show('标题或内容不能为空', 'danger');
            return false;
        }
        else {
            let show = document.querySelector('#show-check').checked;
            content = content.replace(/\n/g, '<br>');

            // console.log(content);            

            fetch(`/save_information`, {
                method: 'post',
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ 'title': title, 'content': content, 'show': show }),
            })
                .then(response => response.json())
                .then(content => {
                    if (content == -1) {
                        notifier.show('权限不够，操作失败', 'danger');
                    }
                    else {
                        notifier.show('信息保存成功', 'success');
                    }
                });
        }
    });

    fetch(`/fetch_information`)
        .then(response => response.json())
        .then(content => {
            if (content != -1) {
                let cc = content['content'].replace(/<br>/g, '\n');
                document.querySelector('#infor-title').value = content['title'];
                document.querySelector('#infor-textarea').value = cc;
                document.querySelector('#show-check').checked = content['show'];
            }
        });

    document.querySelector('#infor-title').focus();
}();
