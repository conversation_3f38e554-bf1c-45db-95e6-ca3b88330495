let page_techbuyquery = function () {
    let get_height = getHeight() - 133;
    let row_num = Math.floor(get_height / 33);

    let init_data = {
        container: '#table-stockout',
        url: `/get_tech_sale`,
        post_data: {
            id: "",
            name: '',
            sort: "build_date DESC",
            rec: row_num,
            cate: "",
        },
        edit: false,
        header_names: {
            "id": "id",
            "客户": "名称",
            "协议名称": "name",
            "编号": "tech_no",
            "产品类型": "p_type",
            "牌号": "p_material",
            "编辑日期": "build_date",
            "编辑人": "editor",
        },

        row_fn: row_fn,
    };

    tool_table.table_init(init_data);
    tool_table.fetch_table();

    //点击搜索按钮
    document.querySelector('#serach-button').addEventListener('click', function () {
        let fields = document.querySelector('#search-fields').value;
        init_data.post_data.name = fields;
        init_data.post_data.page = 1;
        tool_table.fetch_table();
    });

    function row_fn(tr) {
        const fei = tr.作废 ? " class='fei'" : "";
        const name_pdf = tr.pdf_url ? `<td class="名称" title="下载协议pdf"><a href="${tr.pdf_url}">${tr.名称}</a></td>` :
            `<td class="名称">${tr.名称}</td>`;
        return `<tr${fei}><td>${tr.序号}</td><td class="客户" title="查阅编辑"><a href="/tech_sale/${tr.id}__编辑" target="_blank">${tr.客户}</a></td>
            ${name_pdf}
            <td class="编号">${tr.编号}</td><td class="产品类型">${tr.产品类型}</td>
            <td class="牌号">${tr.牌号}</td><td class="编辑人">${tr.编辑人}</td><td class="编辑日期">${tr.编辑日期}</td><td hidden>${tr.id}</td></tr>`;
    }

    //作废按键
    document.querySelector('#button-fei').addEventListener('click', function () {
        let chosed = document.querySelector('tbody .focus');

        if (!chosed) {
            notifier.show('请先选择协议', 'danger');
            return;
        }

        alert_confirm('确认作废此协议吗？', {
            confirmCallBack: () => {
                const id = chosed.querySelector('td:last-child').textContent.trim();

                const data = { id: id };

                fetch(`/fei_techsale`, {
                    method: 'post',
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(data),
                })
                    .then(response => response.json())
                    .then(content => {
                        if (content != -1) {
                            chosed.style.color = "lightgray";
                            chosed.style.textDecoration = "line-through";
                            notifier.show('协议已作废', 'success');
                        } else {
                            notifier.show('权限不够，操作失败', 'danger');
                        }
                    });
            },
        });
    });
}();