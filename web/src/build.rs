//! 该模块需在 rust 其它源文件编译之前进行编译, 将静态文件加入定义的 StaticFile 结构中, 然后将模板编译成可执行函数.
//! 编译后的全部文件将通过 `include!` 宏命令的方式引入到 `main.rs` 中供使用.

use ructe::{Ructe, RucteError};
use std::path::Path;
use std::process::Command;

fn main() -> Result<(), RucteError> {
    // 前置编译模块前端资源
    println!("cargo:warning=Running modules_assets_build.sh...");
    let status = Command::new("../scripts/modules_assets_build.sh")
        .status()
        .expect("Failed to run modules_assets_build.sh");
    if !status.success() {
        panic!("modules_assets_build.sh failed with exit code {:?}", status.code());
    }

    let mut ructe = Ructe::from_env()?;         //创建一个 Ructe 实例. 同时, 创建文件: templates.rs 以及一个包含子模块的目录: templates, 当 main() 执行完毕, 文件内容才全部建立
    let mut statics = ructe.statics()?;
    // 工作区根 static 目录（当前 build.rs 位于 web/src，需回到工作区根）
    statics.add_files("../static")?;

    // 编译根 templates（工作区根目录）
    if Path::new("../templates").exists() {
        ructe.compile_templates("../templates")?;
    }

    // 编译模块模板（相对工作区根路径）
    for p in [
        "../modules/common/templates",
        "../modules/sales/templates",
        "../modules/buy/templates",
        "../modules/users/templates",
    ] {
        if Path::new(p).exists() {
            ructe.compile_templates(p)?;
        }
    }

    Ok(())
}