use actix_files as fs;
use actix_identity::{CookieIdentityPolicy, IdentityService};
use actix_web::web::Data;
use deadpool_postgres::Runtime;
use erp_core::ModuleRegistry;
use tracing_subscriber::EnvFilter;

#[derive(serde::Deserialize)]
struct Config {
    pg: deadpool_postgres::Config,
}

impl Config {
    fn from_env() -> Result<Self, config::ConfigError> {
        let cfg = config::Config::builder()
            .add_source(config::Environment::default().separator("__"))
            .build()?;
        cfg.try_deserialize()
    }
}

use actix_web::{HttpRequest, HttpResponse, web::Path};
// use actix_web::http::header::ContentType;
use actix_files::NamedFile;
use std::path::PathBuf;

/// 全局 /static/{name} 处理器：优先返回 ructe 内嵌静态文件，找不到再回退到工作区 static 目录
async fn static_file_handler(req: HttpRequest, name: Path<String>) -> HttpResponse {
    let name = name.into_inner();
    // 先尝试 ructe 嵌入静态表（二分查找）
    if let Some(data) = modules_common::templates::statics::StaticFile::get(&name) {
        return HttpResponse::Ok()
            .body(data.content);
    }
    // 如果二分查找失败，退回到线性查找（有些生成场景 STATICS 可能未严格按 name 排序）
    if let Some(data_ref) = modules_common::templates::statics::STATICS.iter().find(|s| s.name == name) {
        let data = *data_ref;
        return HttpResponse::Ok()
            .body(data.content);
    }
    // 回退到磁盘 static 目录（兼容未哈希或外部资源）
    let path: PathBuf = ["static", &name].iter().collect();
    match NamedFile::open(path) {
        Ok(f) => f.into_response(&req),
        Err(_) => HttpResponse::NotFound().finish(),
    }
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv::dotenv().ok();
    tracing_subscriber::fmt()
        .with_env_filter(EnvFilter::from_default_env())
        .init();

    // 构建数据库连接池
    let cfg = Config::from_env().expect("PG config not found in env");
    let pool = cfg.pg.create_pool(Some(Runtime::Tokio1), tokio_postgres::NoTls).expect("Create pool");

    let mut registry = ModuleRegistry::new();

    // 注册模块
    registry.register(modules_common::BaseModule::default());
    registry.register(modules_sales::SalesModule::default());
    registry.register(modules_purchase::PurchaseModule::default());
    registry.register(modules_users::UsersModule::default());


    let registry_data = Data::new(registry);

    // 直接启动 HttpServer 并注入中间件与共享数据
    println!("服务已启动：http://localhost:8080");

    actix_web::HttpServer::new(move || {
        let r = registry_data.clone();
        actix_web::App::new()
            .app_data(Data::new(pool.clone()))
            .app_data(r.clone())
            .wrap(IdentityService::new(
                CookieIdentityPolicy::new(&[6; 32])
                    .name("auth-newsales")
                    .max_age(actix_web::cookie::time::Duration::days(30))
                    .secure(false),
            ))
            .configure(move |cfg| {
                r.configure_routes(cfg);
                // 挂载全局 /static 路由到内置 handler（优先 ructe 编译的静态文件）
                cfg.service(actix_web::web::resource("/static/{name:.*}").to(static_file_handler));
            })
            // 添加磁盘 assets/upload 映射（这些仍是磁盘目录）
            .service(fs::Files::new("/assets", "assets"))
            .service(fs::Files::new("/upload", "upload"))
    })
    .bind(("0.0.0.0", 8080))?
    .run()
    .await
}
