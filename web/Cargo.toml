[package]
name = "web"
version = "0.1.0"
edition = "2021"

build = "src/build.rs"

[build-dependencies]
ructe = { git = "https://github.com/samlink/ructe.git" }

[dependencies]
erp-core = { path = "../core" }
modules-common = { path = "../modules/common" }
modules-sales = { path = "../modules/sales" }
modules-purchase = { path = "../modules/buy" }
modules-users = { path = "../modules/users" }

actix-web = "4"
actix-identity = "0.4"
actix-files = "0.6"

tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
config = "0.13.4"
dotenv = "0.15.0"
deadpool-postgres = { version = "0.12.1", features = ["serde"] }
tokio-postgres = { version = "0.7.10", features = ["with-chrono-0_4"] }
serde = { version = "1", features = ["derive"] }
