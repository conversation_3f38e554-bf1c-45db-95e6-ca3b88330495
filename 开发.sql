-- products 表

-- 将供应商换成 供应商id
ALTER TABLE products ADD COLUMN 供应商id integer;

-- ALTER TABLE products
-- ADD CONSTRAINT fk_products_suppliers
-- FOREIGN KEY (供应商id) REFERENCES customers(id);

-- CREATE INDEX products_供应商id_index ON products (供应商id);

-- 规范产地字段
update products set "文本字段5"='冶钢-圆钢' where "文本字段5"='大冶特殊钢' and 商品id like '3%';
update products set "文本字段5"='冶钢-钢管' where "文本字段5"='大冶特殊钢' and 商品id like '4%';

update products set "文本字段5"='冶钢-圆钢' where "文本字段5"='大冶特钢' and 商品id like '3%';
update products set "文本字段5"='冶钢-钢管' where "文本字段5"='大冶特钢' and 商品id like '4%';

update products set "文本字段5"='冶钢-圆钢' where "文本字段5"='大冶特殊' and 商品id like '3%';
update products set "文本字段5"='冶钢-钢管' where "文本字段5"='大冶特殊' and 商品id like '4%';

update products set "文本字段5"='冶钢-圆钢' where "文本字段5"='冶钢' and 商品id like '3%';
update products set "文本字段5"='冶钢-钢管' where "文本字段5"='冶钢' and 商品id like '4%';

update products set "文本字段5"='冶钢-圆钢' where "文本字段5"='湖北新冶钢' and 商品id like '3%';
update products set "文本字段5"='冶钢-钢管' where "文本字段5"='湖北新冶钢' and 商品id like '4%';

update products set "文本字段5"='常宝' where "文本字段5"='江苏常宝';
update products set "文本字段5"='劝诚' where "文本字段5"='劝诚特钢';
update products set "文本字段5"='抚顺特钢' where "文本字段5"='抚钢';
update products set "文本字段5"='浙江华东钢管' where "文本字段5"='浙江华东';
update products set "文本字段5"='衡钢' where "文本字段5"='衡阳华菱';
update products set "文本字段5"='江阴兴澄特种钢' where "文本字段5"='江阴兴澄';
update products set "文本字段5"='江阴兴澄特种钢' where "文本字段5"='兴澄特钢+浩运';
update products set "文本字段5"='本钢' where "文本字段5"='本钢钢铁';
update products set "文本字段5"='本钢' where "文本字段5"='本钢/中兴热处理';
update products set "文本字段5"='东北特殊钢' where "文本字段5"='东北特钢';
update products set "文本字段5"='重庆钢铁' where "文本字段5"='重庆重材';
update products set "文本字段5"='山东海鑫达' where "文本字段5"='海鑫达';
update products set "文本字段5"='天津钢管制造' where "文本字段5"='大无缝';
update products set "文本字段5"='宝山钢铁' where "文本字段5"='宝钢特种';
update products set "文本字段5"='上大' where "文本字段5"='中航上大';

-- 更新供应商id
UPDATE products
SET 供应商id = customers.id
FROM customers
WHERE TRIM(products.文本字段5) = TRIM(customers.文本字段1);

-- 供应商id 无法自动生成的，设置为 52（需先查询空值 供应商id）
update products set 供应商id = 52 where 供应商id is null;

-- 供应商表
update customers set 文本字段1 = '上大' where 文本字段1 = ' 上大';

-- product_info 表

-- 新增供应商字段
alter table product_info add column supplier_id integer;

ALTER TABLE product_info
ADD CONSTRAINT fk_product_info_suppliers
FOREIGN KEY (supplier_id) REFERENCES customers(id);

CREATE INDEX product_info_supplier_id_index ON product_info (supplier_id);

-- 重新生成产品信息表
INSERT INTO product_info (name, material, size, status, tech_no, supplier_id, code)
SELECT                
    split_part(node_name,' ',2) as name,
    split_part(node_name,' ',1) as material, 
    "规格型号" as size,
    "文本字段2" as status,
    "文本字段3" as tech_no,
    "供应商id" as supplier_id,
    split_part(node_name,' ',2) || '_' || split_part(node_name,' ',1) || '_' || 
        "规格型号" || '_' || "文本字段2" || '_' || "文本字段3" || '_' || "供应商id" AS code
FROM products p
JOIN tree ON tree.num = p.商品id
WHERE split_part(node_name,' ',2) = '圆钢' OR split_part(node_name,' ',2) = '无缝钢管'
GROUP BY node_name, "规格型号", "文本字段2", "文本字段3", "供应商id"
ORDER BY split_part(node_name,' ',2) DESC, split_part(node_name,' ',1), "规格型号";

-- 以上为保留内容，布置服务器会用到 -------------------------------

alter table documents add column 反审人 text default '';
alter table documents add column 反审日期 text default '';