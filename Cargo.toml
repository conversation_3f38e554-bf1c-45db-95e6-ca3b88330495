[package]
name = "sales"
version = "0.1.0"
authors = ["sam <<EMAIL>>"]
edition = "2021"

build = "modules/common/src/build.rs"

[workspace]
members = [
  ".",
  "core",
  "web",
  "modules/common",
  "modules/buy",
  "modules/sales",
  "modules/users",
]
resolver = "2"

[build-dependencies]
ructe = { git = "https://github.com/samlink/ructe.git", features = ["mime03"] }
# ructe = { version = "0.18.2", features = ["mime03"] }

[dependencies]
time = "0.1"
uuid = { version = "0.8", features = ["serde", "v4"] }
mime = "0.3"

actix-web = "4.9"
actix-files = "0.6"
actix-multipart = "0.6.1"
actix-identity = "0.4"

futures = "0.3"

serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

tokio = { version = "1", features = ["fs", "io-util"] }

tokio-postgres = { version = "0.7.10", features = ["with-chrono-0_4"] }
deadpool-postgres = { version = "0.12.1", features = ["serde"] }
config = "0.13.4"
dotenv = "0.15.0"
image = "0.24.7"

rust-crypto = "0.2.36"
rand = "0.7.3"
reqwest = "0.9"
regex = "1"
async-recursion = "1.0.5"
rust_xlsxwriter = "0.84.0"
calamine = "0.26.0"
rust-pinyin = "0.1.3"
chrono = { version = "0.4", features = ["serde"] }
