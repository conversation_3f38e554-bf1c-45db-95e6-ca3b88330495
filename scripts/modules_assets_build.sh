#!/usr/bin/env bash
set -euo pipefail

# 模块化前端构建脚本（示例）：
# - 遍历 modules/*/assets 与 modules/*/scss
# - 输出到 static/<module>/js 与 static/<module>/css（注意：/static 为生成目录，勿手改）

ROOT_DIR=$(cd "$(dirname "$0")/.." && pwd)
STATIC_DIR="$ROOT_DIR/static"

JS_MIN=${JS_MIN:-"terser"}
SASS_CMD=${SASS_CMD:-"sass --style compressed --no-source-map"}

# Ensure sass can resolve shared partials by setting SASS_PATH
# Include root scss, and common module scss (base/parts) so that @import "parts/notify" works
export SASS_PATH="$ROOT_DIR/scss:$ROOT_DIR/scss/base:$ROOT_DIR/modules/common/scss:$ROOT_DIR/modules/common/scss/base:$ROOT_DIR/modules/common/scss/parts${SASS_PATH:+:$SASS_PATH}"

mkdir -p "$STATIC_DIR"

# 清空 static 目录，避免旧文件残留
echo "Clearing static directory: $STATIC_DIR"
rm -rf "$STATIC_DIR"/* "$STATIC_DIR"/.* 2>/dev/null || true

# 添加日志：输出根目录和静态目录
echo "ROOT_DIR: $ROOT_DIR"
echo "STATIC_DIR: $STATIC_DIR"

# 构建根级 JS（聚合 tools_service.js 以及 pages/*.js）
if [ -f "$ROOT_DIR/assets/js/min.sh" ]; then
  echo "Building root JS via assets/js/min.sh"
  bash "$ROOT_DIR/assets/js/min.sh"
fi

for MOD in "$ROOT_DIR"/modules/*; do
  [ -d "$MOD" ] || continue
  MOD_NAME=$(basename "$MOD")
  echo "Processing module: $MOD_NAME"

  # JS（优先支持 modules/*/assets/js/pages，其次支持 modules/*/js）
  if [ -d "$MOD/assets/js/pages" ]; then
    OUT_DIR="$STATIC_DIR"
    mkdir -p "$OUT_DIR"
    echo "JS OUT_DIR: $OUT_DIR"
    for f in "$MOD/assets/js/pages"/*.js; do
      [ -f "$f" ] || continue
      base=$(basename "$f")
      out="$OUT_DIR/$base"
      echo "Building JS: $f -> $out"
      if [ -f "$out" ]; then
        echo "Warning: File $out already exists, will be overwritten"
      fi
      $JS_MIN "$f" -o "$out" --compress --mangle
    done
  elif [ -d "$MOD/js" ]; then
    OUT_DIR="$STATIC_DIR"
    mkdir -p "$OUT_DIR"
    echo "JS OUT_DIR: $OUT_DIR"
    for f in "$MOD/js"/*.js; do
      [ -f "$f" ] || continue
      base=$(basename "$f")
      out="$OUT_DIR/$base"
      echo "Building JS: $f -> $out"
      if [ -f "$out" ]; then
        echo "Warning: File $out already exists, will be overwritten"
      fi
      $JS_MIN "$f" -o "$out" --compress --mangle
    done
  fi

  # SCSS（支持 modules/*/scss/pages 以及 modules/*/scss 根目录）
  if [ -d "$MOD/scss/pages" ]; then
    OUT_DIR="$STATIC_DIR"
    mkdir -p "$OUT_DIR"
    echo "SCSS OUT_DIR: $OUT_DIR"
    for f in "$MOD/scss/pages"/*.scss "$MOD/scss/pages"/_*.scss; do
      [ -f "$f" ] || continue
      base=$(basename "$f")
      # 仅编译非下划线开头的入口文件
      if [[ "$base" != _* && "$base" == *.scss ]]; then
        out="$OUT_DIR/${base%.scss}.css"
        echo "Building SCSS: $f -> $out"
        if [ -f "$out" ]; then
          echo "Warning: File $out already exists, will be overwritten"
        fi
        $SASS_CMD "$f":"$out"
      fi
    done
  elif [ -d "$MOD/scss" ]; then
    OUT_DIR="$STATIC_DIR"
    mkdir -p "$OUT_DIR"
    echo "SCSS OUT_DIR: $OUT_DIR"
    for f in "$MOD/scss"/*.scss; do
      [ -f "$f" ] || continue
      base=$(basename "$f")
      if [[ "$base" != _* && "$base" == *.scss ]]; then
        out="$OUT_DIR/${base%.scss}.css"
        echo "Building SCSS: $f -> $out"
        if [ -f "$out" ]; then
          echo "Warning: File $out already exists, will be overwritten"
        fi
        $SASS_CMD "$f":"$out"
      fi
    done
  fi

done

echo "Modules assets build finished."
