use crate::service::*;
use actix_identity::Identity;
use actix_web::{post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde::{Deserialize, Serialize};
use serde_json::json;

///获取入库单据
#[post("/fetch_in_docs")]
pub async fn fetch_in_docs(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let search_sql = if name != "" {
            format!(
                r#" and (LOWER(ds.单号) LIKE '%{name}%' OR LOWER(ds.文本字段6) LIKE '%{name}%' OR LOWER(客商名称.供应商) LIKE '%{name}%')
                "#
            )
        } else {
            "".to_owned()
        };

        // cate 用于传递起止日期（与 transitems 相同的约定：date1<`*_*`>date2）
        let mut date_limit = String::new();
        if search_sql == "" && !post_data.cate.trim().is_empty() {
            let parts: Vec<&str> = post_data.cate.split(SPLITER).collect();
            if parts.len() >= 2 {
                let d1 = parts[0].trim();
                let d2 = parts[1].trim();
                if !d1.is_empty() && !d2.is_empty() {
                    date_limit = format!(" and ds.日期 between '{}' and '{}' ", d1, d2);
                } else if !d1.is_empty() {
                    date_limit = format!(" and ds.日期 >= '{}' ", d1);
                } else if !d2.is_empty() {
                    date_limit = format!(" and ds.日期 <= '{}' ", d2);
                }
            }
        }

        let sql = format!(
            r#"
                select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号, ds.单号 入库单号, ds.文本字段6 采购单号, 
                    客商名称.供应商, 名称, 材质, 规格, ds.日期 入库日期, ds.文本字段5 到货日期, 实数字段1 来料重量, 实数字段2 实际重量, 
                    实数字段3 理论重量, 文本字段2 图片, 经办人, 布尔字段3 提交审核, 文本字段10 审核, 文本字段7 区域, 备注, 作废
                FROM documents ds
                join (
                    select 单号, c.文本字段1 供应商 from documents d join customers c on d.客商id=c.id
                    where d.类别='材料采购' and d.作废=false
                ) as 客商名称
                on ds.文本字段6 = 客商名称.单号
                join (
                    select 单号id, split_part(max(node_name),' ',2) as 名称,
                        split_part(max(node_name),' ',1) as 材质, max(规格型号) 规格 
                    from products p
                    join tree on tree.num = p.商品id
                    where 单号id like 'RK%'   
                    group by 单号id
                ) as 规格型号
                on ds.单号 = 规格型号.单号id
                WHERE ds.类别='采购入库' AND ds.作废=false {search_sql} {date_limit}
                ORDER BY {sort} OFFSET {skip} LIMIT {rec}
        "#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut docs = Vec::new();
        for row in rows {
            let doc = json!({
                    "序号": row.get::<&str, i64>("序号").to_string(),
                    "入库单号": row.get::<&str, String>("入库单号"),
                    "采购单号": row.get::<&str, String>("采购单号"),
                    "名称": row.get::<&str, String>("名称"),
                    "材质": row.get::<&str, String>("材质"),
                    "规格": row.get::<&str, String>("规格"),
                    "供应商": row.get::<&str, String>("供应商"),
                    "入库日期": row.get::<&str, String>("入库日期"),
                    "到货日期": row.get::<&str, String>("到货日期"),
                    "来料重量": row.get::<&str, f64>("来料重量"),
                    "实际重量": row.get::<&str, f64>("实际重量"),
                    "理论重量": row.get::<&str, f64>("理论重量"),
                    "图片": row.get::<&str, String>("图片"),
                    "经办人": row.get::<&str, String>("经办人"),
                    "提交审核": row.get::<&str, bool>("提交审核"),
                    "审核": row.get::<&str, String>("审核"),
                    "区域": row.get::<&str, String>("区域"),
                    "备注": row.get::<&str, String>("备注"),
            });

            docs.push(doc);
        }

        let count_sql = format!(
            r#"SELECT count(ds.单号) as 记录数, sum(实数字段1) as 来料重量合计, sum(实数字段2) as 实际重量合计, sum(实数字段3) as 理论重量合计
                FROM documents ds
                join (
                    select 单号, c.文本字段1 供应商 from documents d join customers c on d.客商id=c.id
                    where d.类别='材料采购' and d.作废=false
                ) as 客商名称
                on ds.文本字段6 = 客商名称.单号
                join (
                    select 单号id, max(规格型号) 规格 from products
                    where 单号id like 'RK%'   
                    group by 单号id
                ) as 规格型号
                on ds.单号 = 规格型号.单号id
                WHERE ds.类别='采购入库' AND ds.作废=false {search_sql} {date_limit}
            "#
        );

        let row = &conn.query_one(count_sql.as_str(), &[]).await.unwrap();
        let count: i64 = row.get("记录数");
        let sum1: f64 = row.get("来料重量合计");
        let sum2: f64 = row.get("实际重量合计");
        let sum3: f64 = row.get("理论重量合计");
        let pages = (count as f64 / rec as f64).ceil() as i32;

        HttpResponse::Ok().json((docs, count, pages, sum1, sum2, sum3))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取单据显示字段
#[post("/fetch_show_fields")]
pub async fn fetch_show_fields(
    db: web::Data<Pool>,
    name: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());

    if user_name != "" {
        let fields = get_fields(db.clone(), &name).await;
        HttpResponse::Ok().json(fields)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取全部单据
#[post("/fetch_all_documents")]
pub async fn fetch_all_documents(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;

    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let mut limits = get_limits(&user).await;

        let doc_cate;
        let doc_sql;

        // cate 结构："<查询分类> <limit>" 或 "<查询分类> <limit><`*_*`>date1<`*_*`>date2"
        let cate_parts: Vec<&str> = post_data.cate.split(SPLITER).collect();
        let cate: Vec<&str> = cate_parts[0].split(' ').collect();

        if cate[0] == "采购查询" {
            doc_cate = "采购单据";
            doc_sql = "documents.类别 = '材料采购' or documents.类别 = '采购退货'";
            if user.duty == "销售" {
                limits = "".to_owned();
            } else if user.duty == "库管" {
                limits = format!("documents.文本字段7 = '{}' AND", user.area);
            }
        } else if cate[0] == "销售查询" {
            doc_cate = "销售单据";
            doc_sql = "documents.类别 = '商品销售' or documents.类别 = '销售退货'";
        } else if cate[0] == "入库查询" {
            doc_cate = "入库单据";
            doc_sql = "documents.类别 = '采购入库'";
        } else if cate[0] == "出库查询" {
            doc_cate = "出库单据";
            doc_sql = "documents.类别 = '销售出库'";
        } else if cate[0] == "调入查询" {
            doc_cate = "库存调入";
            doc_sql = "documents.类别 = '调整入库'";
        } else if cate[0] == "调出查询" {
            doc_cate = "库存调出";
            doc_sql = "documents.类别 = '调整出库'";
        } else if cate[0] == "开票查询" {
            doc_cate = "销售开票";
            doc_sql = "documents.类别 = '销售开票'";
        } else {
            doc_cate = "发货单据";
            doc_sql = "documents.类别 = '运输发货'";
        }

        let mut query_limit = "".to_owned();
        if cate.len() > 1 {
            query_limit = if cate[1] == "wait_out" {
                r#"documents.类别='商品销售' and documents.文本字段10 != '' and documents.布尔字段2 = false and 单号 not in
                (select 文本字段6 from documents where documents.类别='销售出库' and 布尔字段3 = true and 文本字段10 = '' and 作废=false) and"#.to_string()
            } else if cate[1] == "wait_shen" {
                format!("documents.布尔字段3 = true and documents.文本字段10 = '' and documents.类别 = '{}' and 作废=false and", &cate[2])
            } else if cate[1] == "wait_trans" {
                "documents.类别 = '商品销售' and documents.布尔字段1 = false and documents.文本字段10 != ''
                and 单号 in (select documents.文本字段6 from documents where documents.文本字段6 <>''
                and documents.类别='销售出库' and documents.文本字段10 != '' and 布尔字段1 = false and 作废=false) 
                and 单号 not in (select 文本字段6 from documents where documents.类别='运输发货' and 
                布尔字段3 = true and 文本字段10 = '' and 作废=false) and 作废=false 
                or 单号 in 
                (select 单号 from documents join
                    (select 文本字段6 from documents where documents.类别='运输发货' and
                        布尔字段3 = false and 文本字段10 = '' and 作废=false) as foo
                    on foo.文本字段6 = 单号
                where 类别 = '商品销售' and 布尔字段1 = false and 作废=false) and".to_owned()
            } else if cate[1] == "wait_money" {
                "documents.类别 = '商品销售' and documents.是否欠款 = true and documents.文本字段10 != '' and 名称 != '实验室' and".to_owned()
            } else if cate[1] == "wait_kp" {
                "documents.类别='商品销售' AND documents.是否欠款 = true AND documents.布尔字段1 = true AND
                单号 not in (select 文本字段6 from documents where documents.类别='销售开票' and 布尔字段3 = true and 作废=false) AND 
                名称 != '天津彩虹石油机械有限公司' AND 名称 != '实验室' and".to_owned()
            } else if cate[1] == "wait_in" {
                "documents.类别 = '材料采购' and documents.布尔字段2 = false and documents.文本字段10 != '' 
                and 单号 not in (select 文本字段6 from documents where documents.类别='采购入库' and 
                布尔字段3 = true and 文本字段10 = '' and 作废=false) and".to_owned()
            } else if cate[1] == "wait_buy_back" {
                "documents.类别 = '采购退货' and documents.布尔字段2 = false and documents.文本字段10 != '' and 作废=false and".to_owned()
            } else {
                "".to_owned()
            };
        }

        if user.duty == "库管" && cate[1] == "wait_out" {
            limits = format!("documents.文本字段7 = '{}' AND", user.area);
        }

        let mut date_limit = String::new();
        if name == "" && cate_parts.len() > 2 {
            let d1 = cate_parts[1].trim();
            let d2 = cate_parts[2].trim();
            if !d1.is_empty() && !d2.is_empty() {
                date_limit = format!(" and documents.日期 between '{}' and '{}' ", d1, d2);
            } else if !d1.is_empty() {
                date_limit = format!(" and documents.日期 >= '{}' ", d1);
            } else if !d2.is_empty() {
                date_limit = format!(" and documents.日期 <= '{}' ", d2);
            }
        }

        // println!("{},{}",cate[1], query_limit);

        let fields = get_fields(db.clone(), doc_cate).await;

        let mut sql_fields = "SELECT 单号,documents.类别, ".to_owned();
        let mut sql_where = "".to_owned();

        for f in &fields {
            sql_fields += &format!("documents.{},", f.field_name);
            if f.data_type == "文本" {
                sql_where += &format!("LOWER(documents.{}) LIKE '%{}%' OR ", f.field_name, name)
            }
        }

        sql_where += &format!(
            "单号 LIKE '%{name}%' OR 名称 LIKE '%{name}%' OR documents.类别 LIKE '%{name}%' OR 经办人 like '%{name}%'"
        );

        // sql_where = sql_where.trim_end_matches(" OR ").to_owned();

        let sql = format!(
            r#"{sql_fields} 作废, ROW_NUMBER () OVER (ORDER BY {sort}) as 序号,customers.名称, documents.整数字段1 po_check FROM documents
            JOIN customers ON documents.客商id=customers.id
            WHERE {limits} {query_limit} ({doc_sql}) AND ({sql_where}) {date_limit} ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut doc_rows: Vec<String> = Vec::new();
        for row in rows {
            let records = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("单号"),
                row.get::<&str, String>("类别"),
                row.get::<&str, String>("名称"),
                row.get::<&str, bool>("作废").to_string(),
                simple_string_from_base(row, &fields),
                row.get::<&str, i32>("po_check").to_string(),
            ];

            doc_rows.push(sp_query(records));
        }

        let count_sql = format!(
            r#"SELECT count(单号) as 记录数 FROM documents 
            JOIN customers ON documents.客商id=customers.id 
            WHERE {limits} {query_limit} ({doc_sql}) AND ({sql_where}) {date_limit}"#
        );

        let (pages, count) = pages(&conn, count_sql, rec).await;
        HttpResponse::Ok().json((doc_rows, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取其他单据
///
#[post("/fetch_a_documents")]
pub async fn fetch_a_documents(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;

    if user.name != "" {
        let mut limits = get_limits(&user).await;
        let mut doc_sql = "";

        if post_data.cate == "未提交审核" {
            doc_sql =
                "documents.布尔字段3 = false and 已记账 = false and documents.类别 != '采购退货'";
        } else if post_data.cate == "采购退货未完成" {
            doc_sql =
                "documents.类别='采购退货' and documents.布尔字段2 = false and 已记账 = false";
        } else if post_data.cate == "反审单据" {
            doc_sql = "documents.文本字段10 = '' and documents.布尔字段3 = false and 已记账 = true";
        } else if post_data.cate == "销售退货待入库" {
            doc_sql = "documents.类别='销售退货' and documents.文本字段10 != '' and documents.布尔字段2 = false and 已记账 = false";

            if user.duty == "库管" {
                limits = format!("documents.文本字段7 = '{}' AND", user.area,); // 文本字段7 为 区域
            }
        }

        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let mut sql_where = "".to_owned();

        sql_where += &format!(
            "单号 LIKE '%{name}%' OR 名称 LIKE '%{name}%' OR documents.类别 LIKE '%{name}%' OR 经办人 like '%{name}%'"
        );

        // sql_where = sql_where.trim_end_matches(" OR ").to_owned();

        let sql = format!(
            r#"SELECT 单号, documents.类别, documents.日期, ROW_NUMBER () OVER (ORDER BY {sort}) as 序号,
            经办人, documents.备注 FROM documents 
            JOIN customers ON documents.客商id=customers.id
            WHERE {limits} ({doc_sql}) AND ({sql_where}) {NOT_DEL_SQL} ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#,
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut doc_rows: Vec<String> = Vec::new();
        for row in rows {
            let records = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("单号"),
                row.get::<&str, String>("类别"),
                row.get::<&str, String>("日期"),
                row.get::<&str, String>("经办人"),
                row.get::<&str, String>("备注"),
            ];

            doc_rows.push(sp_query(records));
        }

        let count_sql = format!(
            r#"SELECT count(单号) as 记录数 FROM documents 
            JOIN customers ON documents.客商id=customers.id 
            WHERE {limits} {doc_sql} AND ({sql_where}) {NOT_DEL_SQL}"#
        );

        let (pages, count) = pages(&conn, count_sql, rec).await;
        HttpResponse::Ok().json((doc_rows, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[derive(Deserialize, Serialize)]
pub struct Rem {
    id: String,
    has: bool,
    rights: String,
}

#[derive(Deserialize, Serialize)]
pub struct Del {
    id: String,
    rights: String,
    base: String,
}

/// ### 删除单据, 通用   
/// 前端调用:
/// ``` js
/// let del = {
///     id: dh,
///     rights: "删除",
///     base: base  // base 为表名, 如 sale_items
/// }
///
/// fetch(`/documents_del`, {
///     method: 'post',
///     headers: {
///         "Content-Type": "application/json",
///     },
///     body: JSON.stringify(del),
/// })
/// .then(response => response.json())
/// ```
#[post("/documents_del")]
pub async fn documents_del(db: web::Data<Pool>, del: web::Json<Del>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "删除单据".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(r#"DELETE FROM {} WHERE 单号id='{}'"#, del.base, del.id);
        let _ = &conn.execute(sql.as_str(), &[]).await.unwrap();

        let sql = format!(r#"DELETE FROM documents WHERE 单号='{}'"#, del.id);

        let _ = &conn.execute(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

/// ### 作废单据, 并作后续处理  
/// 前端调用:
/// ``` js
/// let del = {
///     id: dh,
///     rights: "作废",
///     base: base
/// }
///
/// fetch(`/documents_fei`, {
///     method: 'post',
///     headers: {
///        "Content-Type": "application/json",
///     },
///     body: JSON.stringify(del),
/// })
/// .then(response => response.json())
/// ```
#[post("/documents_fei")]
pub async fn documents_fei(db: web::Data<Pool>, fei: web::Json<Del>, id: Identity) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    let conn = db.get().await.unwrap();

    let sql = format!(
        r#"select 经办人, 文本字段10 已审核 from documents WHERE 单号='{}'"#,
        fei.id
    );

    let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
    let user = get_user(&db, id, "删除单据".to_owned()).await;

    if user.name == "" && row.get::<&str, String>("经办人") != user_name {
        return HttpResponse::Ok().json(-1);
    } else if user.name == "" {
        if row.get::<&str, String>("已审核") != "" {
            return HttpResponse::Ok().json(-1);
        }
    }

    let sql = format!(
        r#"UPDATE documents set 作废 = true WHERE 单号='{}'"#,
        fei.id
    );
    let _ = &conn.execute(sql.as_str(), &[]).await.unwrap();

    // 作废后操作
    after_fei(db, &fei.id).await;

    HttpResponse::Ok().json(1)
}

/// ### 单据作废后, 对受影响的单据进行状态恢复  
/// 不同单据, 不同处理  
/// 调用方法:  
/// ```
/// after_fei(db, &dh).await;
/// ```
async fn after_fei(db: web::Data<Pool>, dh: &str) {
    let conn = db.get().await.unwrap();
    let mut sql = "".to_owned();
    // 入库单: 对应采购单重置入库完成
    if dh.starts_with("RK") {
        sql = format!(
            r#"update documents set 布尔字段2 = false where 单号 =
            (select 文本字段6 from documents where 单号 = '{dh}')
            "#,
        );
    } else if dh.starts_with("CK") {
        sql = format!(
            r#"update documents set 布尔字段2 = false where 单号 =
            (select 文本字段6 from documents where 单号 = '{dh}')"#,
        );
    } else if dh.starts_with("FH") {
        // 发货单: 重置销售单发货完成
        sql = format!(
            r#"update documents set 布尔字段1 = false where 单号 = 
            (select 文本字段6 from documents where 单号 = '{dh}')"#,
        );
    } else if dh.starts_with("KP") {
        // 开票单: 重置销售单欠款项
        sql = format!(
            r#"update documents set 是否欠款 = true where 单号 = 
            (select 文本字段6 from documents where 单号 = '{dh}')"#,
        );
    }

    let _ = &conn.execute(sql.as_str(), &[]).await.unwrap();
}

/// ### 作废单据前处理  
/// 前端调用:  
/// ``` js
/// fetch(`/before_fei`, {
///     method: 'post',
///     body: dh,
/// })
/// .then(response => response.json())
/// .then(content => {
/// ```
#[post("/before_fei")]
pub async fn before_fei(db: web::Data<Pool>, dh: String) -> HttpResponse {
    let conn = db.get().await.unwrap();
    let mut sql = "".to_owned();
    let mut message = "";

    if dh.starts_with("RK") || dh.starts_with("TR") {
        sql = format!(
            r#"select 1 from products p join sale_items si on p.物料号 = si.物料号 
                where p.单号id = '{dh}' and not_fei(si.单号id) and not_fei(p.单号id)"#,
        );
        message = "本单入库料号已销售, 不能作废";
    } else if dh.starts_with("CG") {
        sql = format!(r#"select 1 from documents d where 文本字段6 = '{dh}' and not_fei(单号)"#,);
        message = "本单已经做入库, 不能作废";
    } else if dh.starts_with("XS") {
        sql = format!(r#"select 1 from documents d where 文本字段6 = '{dh}' and not_fei(单号)"#,);
        message = "本单已经做出库, 不能作废";
    } else if dh.starts_with("CK") {
        sql = format!(
            r#"select 1 from fh_items where 出库id like '{dh}%' and not_fei(单号id) limit 1"#,
        );
        message = "本单已经做发货, 不能作废";
    }

    let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

    if rows.len() > 0 {
        return HttpResponse::Ok().json(message);
    }

    HttpResponse::Ok().json(1)
}

//发货单导出到 excel，仅用于发货查询
#[post("/trans_excel")]
pub async fn trans_excel(
    db: web::Data<Pool>,
    post_data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "导出数据".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let data: Vec<&str> = post_data.split(SPLITER).collect();
        let name = data[2].trim().to_lowercase();

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#"(LOWER(单号) LIKE '%{name}%' OR LOWER(文本字段6) LIKE '%{name}%' OR LOWER(文本字段3) LIKE '%{name}%' OR
                LOWER(日期) LIKE '%{name}%' OR LOWER(文本字段5) LIKE '%{name}%' OR LOWER(备注) LIKE '%{name}%') AND"#
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#" AND 日期::date>='{}'::date"#, data[0])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select 日期 发货日期, 单号, 文本字段6 as 销售单号, 文本字段3 as 合同号, 应结金额::text 单据金额, 实数字段1::text 实际重量, 
                文本字段5 客户, 文本字段8 as 收货人, 文本字段9 as 收货电话, 文本字段11 as 提货车牌, 文本字段12 as 司机电话, 
                经办人, 备注, ROW_NUMBER () OVER (ORDER BY 日期 DESC)::text as 序号 from documents
            where {query_field} 文本字段10 != '' and 类别='运输发货' {query_date}
            ORDER BY 日期 DESC, 单号 DESC"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let f_str = r#"[
            {"name": "序号", "width": 6},
            {"name": "单号", "width": 15},
            {"name": "销售单号", "width": 15},
            {"name": "合同号", "width": 20},
            {"name": "实际重量", "width": 12},
            {"name": "单据金额", "width": 12},
            {"name": "发货日期", "width": 15},
            {"name": "客户", "width": 30},
            {"name": "收货人", "width": 12},
            {"name": "收货电话", "width": 15},  
            {"name": "提货车牌", "width": 15},
            {"name": "司机电话", "width": 15},
            {"name": "经办人", "width": 10},
            {"name": "备注", "width": 15}
        ]"#;

        let fields: Vec<Fields> = serde_json::from_str(f_str).unwrap();

        out_excel("发货单表", fields, rows.as_ref());

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
