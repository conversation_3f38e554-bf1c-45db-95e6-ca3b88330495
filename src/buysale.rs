use crate::service::*;
use actix_identity::Identity;
use actix_web::{get, post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

///获取单据显示字段
#[post("/fetch_inout_fields")]
pub async fn fetch_inout_fields(
    db: web::Data<Pool>,
    name: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());

    if user_name != "" {
        let fields = get_inout_fields(db.clone(), &name).await;
        HttpResponse::Ok().json(fields)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//商品规格自动完成
#[get("/buyin_auto")]
pub async fn buyin_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let fields = get_inout_fields(db.clone(), "商品规格").await;
        let f_map = map_fields(&db, "商品规格").await;
        let mut s: Vec<&str> = search.s.split(" ").collect();
        if s.len() == 1 {
            s.push("");
            s.push("");
        } else if s.len() == 2 {
            s.push("");
        }

        let mut sql_fields = "".to_owned();
        for f in &fields {
            sql_fields += &format!("products.{} || '{}' ||", f.field_name, SPLITER);
        }

        let sql_where = format!(
            "LOWER(node_name) LIKE '%{}%' and {} like '%{}%'",
            s[1].to_lowercase(),
            f_map["规格"],
            s[2]
        );

        let sql = if search.cate == "销售单据" {
            format!(
                r#"SELECT num as id, split_part(node_name,' ',2) || '{SPLITER}' || split_part(node_name,' ',1) 
                || '{SPLITER}' || 规格型号 || '{SPLITER}' || p.文本字段2 || '{SPLITER}' || p.文本字段3 || '{SPLITER}' || 
                库存长度 || '{SPLITER}' || 理论重量 || '{SPLITER}' || p.物料号 AS label 
                FROM products p
                JOIN tree ON p.商品id = tree.num
                LEFT JOIN length_weight() foo ON p.物料号 = foo.物料号
                WHERE p.物料号 like '%{}%' and (库存状态='' and 库存长度 > 10 OR p.物料号 = '锯口费') limit 10"#,
                s[0].to_uppercase(),
            )
        } else {
            format!(
                r#"SELECT num as id, split_part(node_name,' ',2) || '{SPLITER}' || split_part(node_name,' ',1) 
                || '{SPLITER}' || {} || '{SPLITER}' || products.{} AS label 
                FROM products
                JOIN tree ON products.商品id = tree.num
                LEFT JOIN length_weight() as foo
                ON products.物料号 = foo.物料号
                WHERE (pinyin LIKE '%{}%' OR LOWER(node_name) LIKE '%{}%') AND ({sql_where}) limit 10
            "#,
                f_map["规格"],
                f_map["状态"],
                s[0].to_uppercase(),
                s[0].to_uppercase(),
            )
        };

        // println!("{}", sql);

        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 采购时获取商品列表
#[post("/fetch_product_buyin")]
pub async fn fetch_product_buyin(
    db: web::Data<Pool>,
    post_data: web::Json<TablePagerExt>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let id_sql = if post_data.id != "" {
            format!(" and num = '{}'", post_data.id)
        } else {
            "".to_owned()
        };
        let sql = format!(
            r#"select num id, split_part(node_name,' ',2) 名称, split_part(node_name,' ',1) 材质,
                    规格型号 规格, p.文本字段2 状态, p.文本字段3 执行标准,
                    ROW_NUMBER () OVER (ORDER BY {sort}) as 序号
                FROM products p
                JOIN tree ON p.商品id = tree.num
                where 规格型号 like '%{name}%' {id_sql}
                group by num, node_name, 规格型号, 文本字段2, 文本字段3
                ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut products = Vec::new();
        if rows.len() > 0 {
            for row in rows {
                let json = json!({
                "id": row.get::<&str, &str>("id"),
                "序号": row.get::<&str, i64>("序号"),
                "名称": row.get::<&str, &str>("名称"),
                "材质": row.get::<&str, &str>("材质"),
                "规格": row.get::<&str, &str>("规格"),
                "状态": row.get::<&str, &str>("状态"),
                "执行标准": row.get::<&str, &str>("执行标准")});

                products.push(json);
            }
        } else {
            let name: Vec<&str> = post_data.cate.split(' ').collect();
            let json = json!({
                "id" : name[2],
                "序号": 1,
                "名称": name[1],
                "材质": name[0],
                "规格": "",
                "状态": "",
                "执行标准": ""
            });
            products.push(json);
        }

        let sql2 = format!(
            r#"select count(*) 记录数 from
                (SELECT node_name FROM products
                JOIN tree ON 商品id = tree.num
                where 规格型号 like '{name}%' {id_sql}
                group by num, node_name, 规格型号, 文本字段2, 文本字段3) foo
                "#
        );

        let (pages, count) = pages(&conn, sql2, rec).await;

        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//获取指定 id 的产品
#[post("/fetch_one_product")]
pub async fn fetch_one_product(
    db: web::Data<Pool>,
    product_id: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "商品规格").await;

        let sql = format!("SELECT num || '{SPLITER}' || split_part(node_name,' ',2) || '{SPLITER}' || split_part(node_name,' ',1) 
                            || '{SPLITER}' || products.{} || '{SPLITER}' || products.{} || '{SPLITER}' || products.{} || '{SPLITER}' || 
                            products.{} || '{SPLITER}' || products.{} || '{SPLITER}' || products.物料号 as p
                            from products
                            JOIN tree ON products.商品id = tree.num
                            JOIN documents on 单号id = 单号
                            WHERE products.物料号 = '{product_id}' and documents.文本字段10 <> '' {NOT_DEL_SQL}",
                          f_map["规格"], f_map["状态"], f_map["执行标准"], f_map["售价"], f_map["库存长度"]
        );

        let conn = db.get().await.unwrap();
        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut data = "".to_owned();

        for row in rows {
            data = row.get("p");
        }

        HttpResponse::Ok().json(data)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//状态、执行标准、生产厂家、库位的自动输入, 类别通过 cate 传入
#[get("/get_status_auto")]
pub async fn get_status_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "商品规格").await;
        let sql = format!(
            "select distinct products.{} label, '1' as id from products
                        join documents on 单号id = 单号
                        where lower(products.{}) like '%{}%' and documents.文本字段10 <> '' {}
                        order by products.{} limit 10",
            f_map[&search.cate],
            f_map[&search.cate],
            search.s.to_lowercase(),
            NOT_DEL_SQL,
            f_map[&search.cate]
        );
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/get_factory_auto")]
pub async fn get_factory_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let sql = format!("select 文本字段1 label, '1' as id from customers
                                where 类别='供应商' and lower(助记码) like '%{}%' OR 文本字段1 like '%{}%'",
                          search.s.to_lowercase(), search.s.to_lowercase());
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/get_truck_auto")]
pub async fn get_truck_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let sql = format!(
            r#"select distinct 文本字段11 label, '1' as id from documents
                where 类别='运输发货' and 文本字段5 like '%{}%' and 
                lower(文本字段11) like '%{}%' {}"#,
            search.cate,
            search.s.to_lowercase(),
            NOT_DEL_SQL
        );
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/get_truck2_auto")]
pub async fn get_truck2_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let sql = format!(
            r#"select distinct 文本字段12 label, '1' as id from documents
                        where 类别='运输发货' and 文本字段5 like '%{}%' and lower(文本字段12) like '%{}% {}'"#,
            search.cate,
            search.s.to_lowercase(),
            NOT_DEL_SQL
        );
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///保存销售单据
#[post("/save_document_sale")]
pub async fn save_document_sale(
    db: web::Data<Pool>,
    data: web::Json<Document>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();
        let doc_data: Vec<&str> = data.document.split(SPLITER).collect();
        let f_map = map_fields(&db, "销售单据").await;
        let (dh, doc_sql) = get_doc_sql(db.clone(), user, doc_data.clone(), "销售单据").await;

        // println!("{}", doc_sql);

        let transaction = conn.transaction().await.unwrap();
        transaction.execute(doc_sql.as_str(), &[]).await.unwrap();

        if dh != "新单据" {
            transaction
                .execute("DELETE FROM sale_items WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        }

        // 更新明细
        let mut n = 1;
        for item in &data.items {
            let value: Vec<&str> = item.split(SPLITER).collect();
            let id = format!("{}-{}", dh, n);
            let items_sql = format!(
                r#"INSERT INTO sale_items (id, 单号id, 类型, 单价, 长度, 数量, 理重, 重量,
                        金额, 物料号, 备注, 顺序)
                       VALUES('{}', '{}', '{}', {}, {}, {}, {}, {}, {}, '{}', '{}', {})"#,
                id,
                dh,
                value[0],
                value[1],
                value[2],
                value[3],
                value[4],
                value[5],
                value[6],
                value[7],
                value[8],
                n
            );

            // println!("{}", items_sql);

            transaction.execute(items_sql.as_str(), &[]).await.unwrap();
            n += 1;
        }

        let _result = transaction.commit().await;

        if dh != "新单据" {
            // 处理发货完成
            let conn2 = db.get().await.unwrap();
            let sql = format!(
                r#"select {} 发货完成 from documents where 单号='{}' {}"#,
                f_map["发货完成"], dh, NOT_DEL_SQL
            );
            let row = &conn2.query_one(sql.as_str(), &[]).await.unwrap();
            let comp: bool = row.get("发货完成");
            let f_map2 = map_fields(&db, "出库单据").await;
            let comp_sql = format!(
                r#"update documents set {} = {} where {}='{}'"#,
                f_map2["发货完成"], comp, f_map2["销售单号"], dh
            );
            let _ = &conn2.query(comp_sql.as_str(), &[]).await.unwrap();
        }

        // 处理彩虹石油
        let conn2 = db.get().await.unwrap();
        let owe = if doc_data[2] == "25" { false } else { true };
        let sql = format!(
            r#"update documents set 是否欠款 = {} where 单号 = '{}'"#,
            owe, dh
        );

        let _ = &conn2.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(dh)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/save_document")]
pub async fn save_document(
    db: web::Data<Pool>,
    data: web::Json<Document>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();
        let doc_data: Vec<&str> = data.document.split(SPLITER).collect();

        let fields_cate = if data.rights.contains("采购") {
            "采购单据"
        } else {
            "库存调整"
        };

        let (dh, doc_sql) = get_doc_sql(db.clone(), user, doc_data, fields_cate).await;
        let transaction = conn.transaction().await.unwrap();
        transaction.execute(doc_sql.as_str(), &[]).await.unwrap();

        if dh != "新单据" {
            transaction
                .execute("DELETE FROM buy_items WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        }

        let mut n = 1;
        for item in &data.items {
            let value: Vec<&str> = item.split(SPLITER).collect();
            let id = format!("{}-{}", dh, n);
            let items_sql = format!(
                r#"INSERT INTO buy_items (id, 单号id, 商品id, 规格, 状态, 执行标准, 单价, 长度,
                        重量, 金额, 备注, 顺序)
                        VALUES('{}', '{}', '{}', '{}', '{}', '{}', {}, {}, {}, {}, '{}', {})"#,
                id,
                dh,
                value[0],
                value[1],
                value[2],
                value[3],
                value[4],
                value[5],
                value[6],
                value[7],
                value[8],
                n
            );
            // println!("{}", items_sql);

            transaction.execute(items_sql.as_str(), &[]).await.unwrap();
            n += 1;
        }

        match transaction.commit().await {
            Ok(_) => {
                HttpResponse::Ok().json(dh)
            }
            Err(e) => {
                eprintln!("事务提交错误: {:?}", e);
                HttpResponse::Ok().json(-2)
            }
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[derive(Deserialize, Serialize)]
pub struct History {
    pub cate: String,
    pub customer_id: i32,
    pub product_id: i32,
}

///获取单据字段
#[post("/fetch_document")]
pub async fn fetch_document(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let fields = get_inout_fields(db.clone(), &data.cate).await;
        let f_map = map_fields(&db, &data.cate).await;

        let mut sql_fields = "SELECT ".to_owned();

        for f in &fields {
            sql_fields += &format!("documents.{},", f.field_name);
        }

        let sql = format!(
            r#"{} 作废, documents.{} as 提交审核, 客商id, 名称, documents.{} as 审核, 经办人
            FROM documents
            JOIN customers ON documents.客商id=customers.id WHERE 单号='{}'"#,
            sql_fields, f_map["提交审核"], f_map["审核"], data.dh
        );

        // println!("{}", sql);

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();

        let f_str = simple_string_from_base(row, &fields);
        let fds = f_str.split(SPLITER).collect::<Vec<&str>>();

        if data.cate.contains("销售") {
            let document = json!({
                "客户PO": fds[0],
                "合同编号": fds[1],
                "订单日期": fds[2],
                "单据金额": fds[3],
                "交货日期": fds[4],
                "出库完成": fds[5],
                "备注": fds[6],
                "名称": row.get::<&str, String>("名称"),
                "提交审核": row.get::<&str, bool>("提交审核"),
                "客户id": row.get::<&str, i32>("客商id"),
                "审核": row.get::<&str, String>("审核"),
                "经办人": row.get::<&str, String>("经办人"),
                "作废": row.get::<&str, bool>("作废"),
            });

            HttpResponse::Ok().json(document)
        } else if data.cate.contains("采购") {
            let document = json!({
                "合同编号": fds[0],
                "日期": fds[1],
                "金额": fds[2],
                "到货日期": fds[3],
                "入库完成": fds[4],
                "备注": fds[5],
                "名称": row.get::<&str, String>("名称"),
                "提交审核": row.get::<&str, bool>("提交审核"),
                "客户id": row.get::<&str, i32>("客商id"),
                "审核": row.get::<&str, String>("审核"),
                "经办人": row.get::<&str, String>("经办人"),
                "作废": row.get::<&str, bool>("作废"),
            });

            HttpResponse::Ok().json(document)
        } else {
            let document = json!({
                "原因": fds[0],
                "日期": fds[1],
                "备注": fds[2],
                "名称": row.get::<&str, String>("名称"),
                "提交审核": row.get::<&str, bool>("提交审核"),
                "客户id": row.get::<&str, i32>("客商id"),
                "审核": row.get::<&str, String>("审核"),
                "经办人": row.get::<&str, String>("经办人"),
                "作废": row.get::<&str, bool>("作废"),
            });

            HttpResponse::Ok().json(document)
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取单据明细
#[post("/fetch_document_items")]
pub async fn fetch_document_items(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select 商品id, split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质,
                规格, 状态, 执行标准, 单价, 长度, round((重量)::numeric,1)::real 重量, 
                round((单价*重量)::numeric,2)::real as 金额, 备注
                FROM buy_items
                JOIN tree ON 商品id=tree.num
                WHERE 单号id='{}' ORDER BY 顺序"#,
            data.dh
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("执行标准"),
                row.get::<&str, f32>("单价").to_string(),
                row.get::<&str, i32>("长度").to_string(),
                format!("{:.1}", row.get::<&str, f32>("重量")),
                format!("{:.2}", row.get::<&str, f32>("金额")),
                row.get::<&str, String>("备注"),
                row.get::<&str, String>("商品id"),
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取销售单明细
#[post("/fetch_document_items_sales")]
pub async fn fetch_document_items_sales(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质,
                    p.规格型号 规格, 文本字段2 状态, 文本字段3 执行标准, di.类型, 单价, 长度, 数量, 理重,
                    重量, di.物料号, 金额, di.备注
               FROM sale_items di
               JOIN products p ON p.物料号 = di.物料号
               JOIN tree ON p.商品id = tree.num
               WHERE di.单号id = '{}'
               ORDER BY 顺序"#,
            data.dh
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("执行标准"),
                row.get::<&str, String>("类型"),
                row.get::<&str, f32>("单价").to_string(),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                format!("{:.1}", row.get::<&str, f32>("理重")),
                format!("{:.1}", row.get::<&str, f32>("重量")),
                format!("{:.2}", row.get::<&str, f32>("金额")),
                row.get::<&str, String>("物料号"),
                row.get::<&str, String>("备注"),
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//获取相关单据
#[post("/fetch_other_documents")]
pub async fn fetch_other_documents(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select 单号,日期,类别 from documents 
                where (文本字段6 = '{}' or 文本字段4 = '{}') 
                and 文本字段10 <>'' {} order by 日期 desc"#,
            data, data, NOT_DEL_SQL
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let dh: String = row.get("单号");
            let date: String = row.get("日期");
            let cate: String = row.get("类别");
            let item = format!("{}　{}　{}", cate, dh, date);

            document_items.push(item);
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//销售退货时，获取相关销售单据
#[post("/get_sale_dh")]
pub async fn get_sale_dh(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select 单号,日期 from documents where 文本字段6 = '{}' and 类别 = '商品销售' {}"#,
            data, NOT_DEL_SQL
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let dh: String = row.get("单号");
            let date: String = row.get("日期");
            let item = format!("销售单据　{}　{}", dh, date);

            document_items.push(item);
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///提交审核
#[post("/make_sumit_shen")]
pub async fn make_sumit_shen(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, &data.cate).await;
        let sql = format!(
            r#"update documents set {}=true, 提交时间=localtimestamp WHERE 单号='{}'"#,
            f_map["提交审核"], data.dh
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///审核
#[post("/make_formal")]
pub async fn make_formal(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "单据审核".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, &data.cate).await;
        let sql = format!(
            r#"update documents set {}='{}' WHERE 单号='{}'"#,
            f_map["审核"], user.name, data.dh
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///反审单据
#[post("/anti_formal")]
pub async fn anti_formal(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "反审单据".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "销售单据").await; //所有单据均为 文本字段10, 提交审核为 布尔字段3
        let sql = format!(
            r#"update documents set {}='', {}=false, 已记账=true, 布尔字段1=false, 布尔字段2=false, 
                反审人='{}', 反审日期=CURRENT_DATE::text WHERE 单号='{}'"#,
            f_map["审核"], f_map["提交审核"], user.name, data
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 保存销售单的应结金额
#[post("/save_sale_money")]
pub async fn save_sale_money(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let da = data.split(SPLITER).collect::<Vec<&str>>();
        // // 为销售单添加 单据金额
        // let sql = format!(
        //     r#"update documents set 应结金额={} WHERE 单号='{}'"#,
        //     da[2], da[0]
        // );

        // let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        // 为发货单添加 单据金额和实际重量
        let sql = format!(
            r#"update documents set 应结金额={}, 实数字段1={} WHERE 单号='{}'"#,
            da[2], da[3], da[1]
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/check_ku")]
pub async fn check_ku(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let mut result = Vec::new();
        let dh_data: Vec<&str> = data.split("##").collect(); // L658：1690##XS2406-015
        let da: Vec<&str> = dh_data[0].split(SPLITER).collect();
        for d in da {
            let now_num: Vec<&str> = d.split("：").collect();
            let wu_num = now_num[0];

            let sql = format!(
                r#"select 库存长度 from products
                   LEFT JOIN length_weight('{}') as foo
                   ON products.物料号 = foo.物料号
                   where products.物料号 = '{}'"#,
                dh_data[1], wu_num
            );

            let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
            let mut v: i32 = 0;
            for row in rows {
                v = row.get(0);
            }

            if now_num[1].parse::<i32>().unwrap() > v + 10 {
                // 10 为切分损耗补偿
                result.push(wu_num);
            }
        }

        if result.len() == 0 {
            HttpResponse::Ok().json(1)
        } else {
            HttpResponse::Ok().json(result)
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/check_ku2")]
pub async fn check_ku2(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let mut result = Vec::new();
        let da: Vec<&str> = data.split(SPLITER).collect();
        for d in da {
            let field: Vec<&str> = d.split("##").collect();

            let sql = format!(
                r#"select (整数字段3-COALESCE(长度合计,0)-COALESCE(切分次数,0)*2)::real as 库存 from products
                                LEFT JOIN cut_length() as foo
                                ON products.物料号 = foo.物料号
                                where products.物料号 = '{}'"#,
                field[0]
            );

            let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
            let mut v: f32 = 0f32;
            for row in rows {
                v = row.get("库存");
            }

            if field[1].parse::<f32>().unwrap() > v {
                result.push(field[0]);
            }
        }

        if result.len() == 0 {
            HttpResponse::Ok().json(1)
        } else {
            HttpResponse::Ok().json(result)
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/check_contract")]
pub async fn check_contract(db: web::Data<Pool>, data: web::Json<String>) -> HttpResponse {
    let conn = db.get().await.unwrap();
    let da: Vec<&str> = data.split(SPLITER).collect();

    let sql = format!(
        r#"
        select 1 from documents 
        WHERE 类别 = '商品销售' 
        AND 单号 <> '{}'
        AND 文本字段6 = '{}'
        AND 作废 = false 
        "#,
        da[0], da[1]
    );

    let row = &conn.query(sql.as_str(), &[]).await.unwrap();

    if row.len() == 0 {
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json("exsits")
    }
}

#[post("/po_check")]
pub async fn po_check(db: web::Data<Pool>, data: web::Json<Value>) -> HttpResponse {
    let conn = db.get().await.unwrap();
    let sql = "update documents set 整数字段1 = $1 WHERE 单号 = $2";
    let is_checked = data["is_checked"].as_i64().unwrap_or(0) as i32;
    let dh = data["dh"].as_str().unwrap_or("");
    let _row = &conn.execute(sql, &[&is_checked, &dh]).await.unwrap();
    HttpResponse::Ok().json(1)
}

#[derive(Deserialize)]
struct DhQuery {
    dh: String,
}

#[get("/get_po_check")]
pub async fn get_po_check(db: web::Data<Pool>, query: web::Query<DhQuery>) -> HttpResponse {        
    let conn = db.get().await.unwrap();
    let dh = &query.dh;
    let sql = "select 整数字段1 from documents WHERE 单号 = $1";
    let v: i32 = conn.query_one(sql, &[&dh]).await.unwrap().get(0);
    HttpResponse::Ok().json(v)
}
