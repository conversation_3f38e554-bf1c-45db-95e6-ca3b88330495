use crate::service::*;
use actix_identity::Identity;
use actix_web::{post, web, HttpResponse};
use deadpool_postgres::Pool;

// 销售开票欠款
#[post("/make_xs_kp")]
pub async fn make_xs_kp(db: web::Data<Pool>, dh: web::Json<String>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let dh: Vec<&str> = dh.split(SPLITER).collect();
        let owe: bool = dh[1].parse::<bool>().unwrap();
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"update documents set 是否欠款={} WHERE 单号='{}'"#,
            owe, dh[0]
        );

        let _ = conn.execute(sql.as_str(), &[]).await;
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 开票单获得销售单据
#[post("/fetch_sale_docs")]
pub async fn fetch_sale_docs(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        let f_map2 = map_fields(&db, "客户").await;

        let sql = &format!(
            r#"SELECT 单号 as id, 单号 || '　' || customers.{} || '{SPLITER}' || 名称 || '　' || documents.{} ||
            '　' || documents.{} || '　' || documents.{} || '　' || customers.id  as label FROM documents
            join customers on 客商id = customers.id
            WHERE documents.类别='商品销售' AND documents.{} = true AND documents.{} = true AND
            单号 not in (select 文本字段6 from documents where documents.类别='销售开票' {NOT_DEL_SQL}) 
            AND 名称 != '天津彩虹石油机械有限公司' AND 名称 != '实验室' {NOT_DEL_SQL}
            order by 单号 desc"#,
            f_map2["简称"],
            f_map["合同编号"],
            f_map["客户PO"],
            f_map["单据金额"],
            f_map["是否欠款"],
            f_map["发货完成"],
        );

        // println!("{}",sql);
        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 开票单已保存未提交
#[post("/fetch_sale_saved_docs")]
pub async fn fetch_sale_saved_docs(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        let f_map2 = map_fields(&db, "客户").await;

        let sql = &format!(
            r#"SELECT 开票单号 as id, 单号 || '　' || customers.{} || '　' || 开票单.经办人  || '{SPLITER}' || 名称 || '　' || documents.{} ||
            '　' || documents.{} || '　' || documents.{} || '　' || customers.id as label FROM documents
            join customers on 客商id = customers.id
            join 
            (select 单号 开票单号, 文本字段6, 经办人 from documents where documents.类别='销售开票' and 
            布尔字段3 = false and 文本字段10 = '' {NOT_DEL_SQL}) as 开票单
            on 开票单.文本字段6 = documents.单号
            WHERE documents.类别='商品销售' AND documents.{} = true AND documents.{} = true AND
            单号 not in (select 文本字段6 from documents where documents.类别='销售开票' and 布尔字段3 = true {NOT_DEL_SQL}) 
            AND 名称 != '天津彩虹石油机械有限公司' AND 名称 != '实验室' {NOT_DEL_SQL}
            order by 单号 desc"#,
            f_map2["简称"],
            f_map["合同编号"],
            f_map["客户PO"],
            f_map["单据金额"],
            f_map["是否欠款"],
            f_map["发货完成"],
        );

        // println!("{}",sql);
        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///保存开票单据
#[post("/save_document_kp")]
pub async fn save_document_kp(
    db: web::Data<Pool>,
    data: web::Json<Document>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();
        let conn2 = db.get().await.unwrap();
        let doc_data: Vec<&str> = data.document.split(SPLITER).collect();
        let mut doc_sql;

        let fields_cate = &data.rights;

        let f_map = map_fields(&db, fields_cate).await;

        let fields = get_inout_fields(db.clone(), fields_cate).await;
        let dh_data = doc_data[1].to_owned();
        let mut dh = doc_data[1].to_owned();

        if dh_data == "新单据" {
            dh = get_dh(db.clone(), doc_data[0]).await;

            let mut init = "INSERT INTO documents (单号, 客商id,".to_owned();
            for f in &fields {
                init += &format!("{},", &*f.field_name);
            }

            init += &format!(
                "类别,{},{}) VALUES('{}', {},",
                f_map["经办人"], f_map["区域"], dh, doc_data[2]
            );

            doc_sql = build_sql_for_insert(&doc_data, init, &fields, 3);
            doc_sql += &format!("'{}','{}', '{}')", doc_data[0], user.name, user.area);
        } else {
            let init = "UPDATE documents SET ".to_owned();
            doc_sql = build_sql_for_update(doc_data.clone(), init, fields, 3);
            doc_sql += &format!(
                "客商id={}, 类别='{}', {}='{}', {}='{}' WHERE 单号='{}'",
                doc_data[2], doc_data[0], f_map["经办人"], user.name, f_map["区域"], user.area, dh
            );
        }

        // println!("{}", doc_sql);

        let transaction = conn.transaction().await.unwrap();
        transaction.execute(doc_sql.as_str(), &[]).await.unwrap();

        // 单据明细
        if dh_data != "新单据" {
            transaction
                .execute("DELETE FROM kp_items WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        }

        for item in &data.items {
            let value: Vec<&str> = item.split(SPLITER).collect();
            let items_sql = format!(
                r#"INSERT INTO kp_items (单号id, 名称, 规格, 数量, 单价, 税率, 顺序)
                     VALUES('{}', '{}', '{}', {}, {}, '{}', '{}')"#,
                dh, value[1], value[2], value[3], value[4], value[5], value[0],
            );

            // println!("{}", items_sql);

            transaction.execute(items_sql.as_str(), &[]).await.unwrap();
        }

        let _result = transaction.commit().await;

        // 保存时修改销售单是否欠款
        let sql = format!(
            r#"update documents set 是否欠款 = (select 是否欠款 from documents where 单号='{dh}')
                where 单号 = (select 文本字段6 from documents where 单号='{dh}')"#,
        );

        let _ = conn2.execute(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(dh)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取开票单据明细
#[post("/fetch_kp_items")]
pub async fn fetch_kp_items(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select 名称, 规格, 数量, 单价, 税率
                FROM kp_items
                WHERE 单号id='{}' ORDER BY 顺序"#,
            data.dh
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let num = row.get::<&str, f32>("数量");
            let price = row.get::<&str, f32>("单价");
            let tax = row.get::<&str, String>("税率");
            let m = price * num;
            let num_tax = tax.replace("%", "").parse::<f32>().unwrap();
            let tt = format!("{:.2}", m - m / (1.0 + num_tax / 100.0));

            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("规格"),
                row.get::<&str, f32>("数量").to_string(),
                row.get::<&str, f32>("单价").to_string(),
                format!("{:.2}", m), //金额
                tax,
                tt,
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取发货明细，供开票使用
#[post("/fetch_fh_items")]
pub async fn fetch_fh_items(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                select di.顺序 as 序号, split_part(node_name,' ',2) 名称, split_part(node_name,' ', 1)
                    || '/' || 规格型号 as 规格, case when si.类型='按重量' then pi.重量 else pi.数量 end 数量, 单价
                FROM fh_items di
                join pout_items pi on di.出库id = pi.id 
                join sale_items si on si.id = pi.销售id
                join products on si.物料号 = products.物料号
                JOIN tree on products.商品id = tree.num
                WHERE di.单号id in (select 单号 from documents where (文本字段6='{data}' or 文本字段4='{data}')
                     and 类别='运输发货' and 文本字段10 != '' {NOT_DEL_SQL})                 
                UNION ALL
                select 999 序号, 物料号 名称, '--' 规格, 数量, 单价 
                from sale_items 
                where 单号id = '{data}' and 物料号='锯口费' 
                ORDER BY 序号"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let num = row.get::<&str, f32>("数量");
            let price = row.get::<&str, f32>("单价");
            let tax = "13%".to_owned();
            let m = price * num;
            let tt = format!("{:.2}", m - m / 1.13);

            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("规格"),
                row.get::<&str, f32>("数量").to_string(),
                row.get::<&str, f32>("单价").to_string(),
                format!("{:.2}", m), //金额
                tax,
                tt,
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}