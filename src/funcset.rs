use crate::service::*;
use actix_identity::Identity;
use actix_web::{get, post, web, HttpResponse};
use deadpool_postgres::Pool;
use rust_xlsxwriter::{Format, FormatAlign, Workbook};
// use serde::Deserialize;
use serde_json::{json, Value};

// 获取商品类别
#[get("/fetch_product_cate")]
pub async fn fetch_product_cate(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "商品类别".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                select id, name, ord from product_cate
                order by ord;
            "#,
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut docs = Vec::new();

        for row in rows {
            let doc = json!({
                "id": row.get::<&str, i32>("id"),
                "name": row.get::<&str, String>("name"),
                "order": row.get::<&str, i32>("ord"),
            });

            docs.push(doc);
        }

        HttpResponse::Ok().json(docs)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 新增类别
#[post("/create_cate")]
pub async fn create_cate(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "商品类别".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                insert into product_cate (name, ord) values ('{}', {})
            "#,
            data["name"].to_string().trim_matches('"'),
            data["order"].to_string().trim_matches('"'),
        );

        let _row = conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 新增类别
#[post("/edit_cate")]
pub async fn edit_cate(db: web::Data<Pool>, data: web::Json<Value>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "商品类别".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                update product_cate set name = '{}' where id = {}
            "#,
            data["name"].to_string().trim_matches('"'),
            data["id"].to_string().trim_matches('"'),
        );

        let _row = conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 类别排序
#[post("/order_cate")]
pub async fn order_cate(
    db: web::Data<Pool>,
    data: web::Json<Vec<Value>>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "商品类别".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        // 批量更新每个类别的排序
        for item in data.iter() {
            let sql = format!(
                r#"
                    update product_cate set ord = {} where id = {}
                "#,
                item["order"].to_string().trim_matches('"'),
                item["id"].to_string().trim_matches('"'),
            );

            let _row = conn.query(sql.as_str(), &[]).await.unwrap();
        }

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取产品信息
#[post("/get_product_info")]
pub async fn get_product_info(
    db: web::Data<Pool>,
    post_data: web::Json<TablePagerExt>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "产品信息".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let search_sql = if name != "" {
            format!(
                "(lower(material) like '%{}%' or lower(size) like '%{}%' or 
                    lower(status) like '%{}%' or lower(tech_no) like '%{}%' or lower(c.文本字段1) like '%{}%'
                    )",
                name, name, name, name, name
            )
        } else {
            "true".to_owned()
        };

        // 构建 filter 字符串
        let mut filter_sql = "".to_owned();
        if post_data.filter != "" {
            filter_sql = post_data
                .filter
                .replace("牌号", "material")
                .replace("规格", "size")
                .replace("状态", "status")
                .replace("技术编号", "tech_no")
                .replace("供应商", "c.文本字段1")
                .replace("(空白)", "");
        }

        let sql = format!(
            r#"
                select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号,
                    product_info.id,
                    product_info.name,
                    product_info.material,
                    product_info.size,
                    product_info.status,
                    product_info.tech_no,
                    c.文本字段1 AS supplier,
                    COALESCE(stock.库存支数, 0)::TEXT AS 库存支数,
                    COALESCE(stock.库存长度, 0)::TEXT AS 库存长度,
                    COALESCE(stock.库存重量, 0)::TEXT AS 库存重量,
                    COALESCE(product_info.safety_stock, 0)::TEXT AS 安全库存预警,
                    COALESCE(product_info.max_stock, 0)::TEXT AS 最高库存预警,
                    product_info.note
                FROM
                    product_info
                JOIN customers c ON product_info.supplier_id = c.id
                LEFT JOIN (
                    SELECT
                        node_name,
                        规格型号,
                        products.文本字段2 AS 状态,
                        products.文本字段3 AS 标准,
                        products.文本字段5 AS 供应商,
                        SUM(CASE WHEN COALESCE(foo.库存长度, 0) > 0 THEN 1 ELSE 0 END) AS 库存支数,
                        SUM(COALESCE(foo.库存长度, 0)) AS 库存长度,
                        SUM(COALESCE(foo.理论重量, 0)) AS 库存重量
                    FROM
                        products
                    JOIN documents ON 单号id = 单号
                    JOIN tree ON tree.num = products.商品id
                    LEFT JOIN length_weight() AS foo ON products.物料号 = foo.物料号
                    WHERE
                        库存状态 = ''
                        AND (node_name LIKE '%圆钢%' OR node_name LIKE '%无缝钢管%')
                        AND COALESCE(foo.库存长度, 0) > 10
                        AND documents.文本字段10 != ''
                        AND 作废 = false
                    GROUP BY
                        node_name,
                        规格型号,
                        products.文本字段2,
                        products.文本字段3,
                        products.文本字段5
                ) AS stock
                ON product_info.material || ' ' || product_info.name = stock.node_name
                AND product_info.size = stock.规格型号
                AND product_info.status = stock.状态
                AND product_info.tech_no = stock.标准
                AND c.文本字段1 = stock.供应商
            where {search_sql} {filter_sql}
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = json!({
                    "序号": row.get::<&str, i64>("序号").to_string(),
                    "id": row.get::<&str, i32>("id"),
                    "名称": row.get::<&str, String>("name"),
                    "技术编号": row.get::<&str, String>("tech_no"),
                    "牌号": row.get::<&str, String>("material"),
                    "规格": row.get::<&str, String>("size"),
                    "状态": row.get::<&str, String>("status"),
                    "供应商": row.get::<&str, String>("supplier"),
                    "库存支数": row.get::<&str, String>("库存支数"),
                    "库存长度": row.get::<&str, String>("库存长度"),
                    "库存重量": row.get::<&str, String>("库存重量"),
                    "安全库存预警": row.get::<&str, String>("安全库存预警"),
                    "最高库存预警": row.get::<&str, String>("最高库存预警"),
                    "备注": row.get::<&str, String>("note"),
            });

            products.push(fields);
        }

        let count_sql = format!(
            r#"
            select count(*) as 记录数 from product_info
            JOIN customers c ON product_info.supplier_id = c.id
            where {search_sql} {filter_sql}"#
        );

        // println!("{}", count_sql);

        let (pages, count) = pages(&conn, count_sql, post_data.rec).await;
        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 增加产品信息
#[post("/add_product_info")]
pub async fn add_product_info(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "产品信息".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let name = data["name"].to_string();
        let name = name.trim_matches('"').to_string();
        let material = data["material"].to_string();
        let material = material.trim_matches('"').to_string();
        let size = data["size"].to_string();
        let size = size.trim_matches('"').to_string();
        let status = data["status"].to_string();
        let status = status.trim_matches('"').to_string();
        let tech_no = data["tech_no"].to_string();
        let tech_no = tech_no.trim_matches('"').to_string();
        let supplier_id = data["supplier"].to_string();
        let supplier_id = supplier_id.trim_matches('"').to_string();
        let supplier_id_val: i32 = supplier_id.parse().unwrap_or(0);
        let safety_stock = data["safety_stock"].to_string();
        let safety_stock = safety_stock.trim_matches('"').to_string();
        let max_stock = data["max_stock"].to_string();
        let max_stock = max_stock.trim_matches('"').to_string();
        let note = data["note"].to_string();
        let note = note.trim_matches('"').to_string();

        // 生成code字段，格式：name_material_size_status_tech_no
        let start = if name == "圆钢" { "YG" } else { "WG" };
        let code = format!(
            "{}_{}_{}_{}_{}_{}",
            start, material, size, status, tech_no, supplier_id
        );

        let safety_stock_val: i32 = safety_stock.parse().unwrap_or(0);
        let max_stock_val: i32 = max_stock.parse().unwrap_or(0);

        let sql = r#"
            INSERT INTO product_info (name, material, size, status, tech_no, supplier_id, code, safety_stock, max_stock, note)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        "#;

        let _result = conn
            .query(
                sql,
                &[
                    &name,
                    &material,
                    &size,
                    &status,
                    &tech_no,
                    &supplier_id_val,
                    &code,
                    &safety_stock_val,
                    &max_stock_val,
                    &note,
                ],
            )
            .await
            .unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 编辑产品信息
#[post("/edit_product_info")]
pub async fn edit_product_info(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "产品信息".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let name = data["name"].to_string();
        let name = name.trim_matches('"').to_string();
        let material = data["material"].to_string();
        let material = material.trim_matches('"').to_string();
        let size = data["size"].to_string();
        let size = size.trim_matches('"').to_string();
        let status = data["status"].to_string();
        let status = status.trim_matches('"').to_string();
        let tech_no = data["tech_no"].to_string();
        let tech_no = tech_no.trim_matches('"').to_string();
        let supplier_id = data["supplier"].to_string();
        let supplier_id = supplier_id.trim_matches('"').to_string();
        let supplier_id_val: i32 = supplier_id.parse().unwrap_or(0);
        let safety_stock = data["safety_stock"].to_string();
        let safety_stock = safety_stock.trim_matches('"').to_string();
        let max_stock = data["max_stock"].to_string();
        let max_stock = max_stock.trim_matches('"').to_string();
        let note = data["note"].to_string();
        let note = note.trim_matches('"').to_string();
        let id = data["id"].to_string();
        let id = id.trim_matches('"').to_string();

        // 生成code字段，格式：name_material_size_status_tech_no
        let start = if name == "圆钢" { "YG" } else { "WG" };
        let code = format!(
            "{}_{}_{}_{}_{}_{}",
            start, material, size, status, tech_no, supplier_id
        );

        let safety_stock_val: i32 = safety_stock.parse().unwrap_or(0);
        let max_stock_val: i32 = max_stock.parse().unwrap_or(0);
        let id_val: i32 = id.parse().unwrap_or(0);

        let sql = r#"
            UPDATE product_info SET name=$1, material=$2, size=$3, 
                status=$4, tech_no=$5, supplier_id=$6, code=$7, safety_stock=$8, max_stock=$9, note=$10
            WHERE id=$11
        "#;

        let _result = conn
            .query(
                sql,
                &[
                    &name,
                    &material,
                    &size,
                    &status,
                    &tech_no,
                    &supplier_id_val,
                    &code,
                    &safety_stock_val,
                    &max_stock_val,
                    &note,
                    &id_val,
                ],
            )
            .await
            .unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取牌号自动完成
#[get("/get_material_auto")]
pub async fn get_material_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let sql = format!(
            "SELECT DISTINCT material as label, '1' as id FROM product_info 
             WHERE LOWER(material) LIKE '%{}%' 
             ORDER BY material LIMIT 10",
            search.s.to_lowercase()
        );
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取牌号自动完成
#[get("/get_status_info_auto")]
pub async fn get_status_info_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let sql = format!(
            "SELECT DISTINCT status as label, '1' as id FROM product_info 
             WHERE LOWER(status) LIKE '%{}%' 
             ORDER BY status LIMIT 10",
            search.s.to_lowercase()
        );
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取技术编号自动完成
#[get("/get_tech_no_auto")]
pub async fn get_tech_no_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let sql = format!(
            r#"
            SELECT DISTINCT label, '1' as id FROM
                ((SELECT DISTINCT tech_no as label FROM product_info 
                WHERE LOWER(tech_no) LIKE '%{}%' 
                LIMIT 10)
                UNION ALL
                (SELECT tech_no as label FROM tech_buy 
                WHERE LOWER(tech_no) LIKE '%{}%' and fei=false
                LIMIT 10)
                ) as all_tech
            ORDER BY label LIMIT 10
            "#,
            search.s.to_lowercase(),
            search.s.to_lowercase()
        );
        autocomplete(db, &sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 导出产品信息到Excel
#[post("/export_product_info")]
pub async fn export_product_info(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "产品信息".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        // 生成文件名
        let file_name = format!("产品信息_{}.xlsx", chrono::Local::now().format("%Y-%m-%d"));
        let file_path = format!("./download/{}", file_name);

        let mut wb = Workbook::new();
        let sheet = wb.add_worksheet().set_name("产品信息").unwrap();

        // 设置标题格式
        let format = Format::new().set_align(FormatAlign::Center).set_bold();

        let format2 = Format::new().set_align(FormatAlign::Center);

        // 设置表头
        let headers = [
            "序号",
            "名称",
            "牌号",
            "规格",
            "状态",
            "供应商",
            "技术编号",
            "库存支数",
            "库存长度",
            "库存重量",
            "安全库存预警",
            "最高库存预警",
            "备注",
        ];

        for (i, header) in headers.iter().enumerate() {
            sheet
                .write_with_format(0, i as u16, *header, &format)
                .unwrap();
            sheet.set_column_width(i as u16, 15.0).unwrap();
        }

        // 构建查询条件
        let search = data["search"].as_str().unwrap_or("").to_lowercase();
        let search_sql = if !search.is_empty() {
            format!(
                "WHERE (lower(material) like '%{}%' or lower(size) like '%{}%' or 
                    lower(status) like '%{}%' or lower(tech_no) like '%{}%')",
                search, search, search, search
            )
        } else {
            "".to_owned()
        };

        // 构建 filter 条件
        let mut filter_sql = "".to_owned();
        if let Some(filter) = data.get("filter") {
            if !filter.as_str().unwrap_or("").is_empty() {
                filter_sql = filter
                    .as_str()
                    .unwrap()
                    .replace("牌号", "material")
                    .replace("规格", "size")
                    .replace("状态", "status")
                    .replace("技术编号", "tech_no")
                    .replace("(空白)", "");
                if !search_sql.is_empty() {
                    filter_sql = format!("{}", filter_sql);
                } else {
                    filter_sql = format!("WHERE true {}", filter_sql);
                }
            }
        }

        let sql = format!(
            r#"
                select ROW_NUMBER () OVER (ORDER BY name) as 序号,
                    product_info.name AS 名称,
                    product_info.material AS 牌号,
                    product_info.size AS 规格,
                    product_info.status AS 状态,
                    product_info.tech_no AS 技术编号,
                    c.文本字段1 AS 供应商,
                    COALESCE(stock.库存支数, 0)::TEXT AS 库存支数,
                    COALESCE(stock.库存长度, 0)::TEXT AS 库存长度,
                    COALESCE(stock.库存重量, 0)::TEXT AS 库存重量,
                    COALESCE(product_info.safety_stock, 0)::TEXT AS 安全库存预警,
                    COALESCE(product_info.max_stock, 0)::TEXT AS 最高库存预警,
                    product_info.note AS 备注
                FROM
                    product_info
                JOIN customers c ON product_info.supplier_id = c.id
                LEFT JOIN (
                    SELECT
                        node_name,
                        规格型号,
                        products.文本字段2 AS 状态,
                        products.文本字段3 AS 标准,
                        products.文本字段5 AS 供应商,
                        SUM(CASE WHEN COALESCE(foo.库存长度, 0) > 0 THEN 1 ELSE 0 END) AS 库存支数,
                        SUM(COALESCE(foo.库存长度, 0)) AS 库存长度,
                        SUM(COALESCE(foo.理论重量, 0)) AS 库存重量
                    FROM
                        products
                    JOIN documents ON 单号id = 单号
                    JOIN tree ON tree.num = products.商品id
                    LEFT JOIN length_weight() AS foo ON products.物料号 = foo.物料号
                    WHERE
                        库存状态 = ''
                        AND (node_name LIKE '%圆钢%' OR node_name LIKE '%无缝钢管%')
                        AND COALESCE(foo.库存长度, 0) > 10
                        AND documents.文本字段10 != ''
                        AND 作废 = false
                    GROUP BY
                        node_name,
                        规格型号,
                        products.文本字段2,
                        products.文本字段3,
                        products.文本字段5
                ) AS stock
                ON product_info.material || ' ' || product_info.name = stock.node_name
                AND product_info.size = stock.规格型号
                AND product_info.status = stock.状态
                AND product_info.tech_no = stock.标准
                AND c.文本字段1 = stock.供应商
                {search_sql} {filter_sql}
                ORDER BY name"#,
        );

        // println!("SQL: {}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        // 写入数据
        for (row_num, row) in rows.iter().enumerate() {
            let row_num = (row_num + 1) as u32;
            sheet
                .write_with_format(
                    row_num,
                    0,
                    row.get::<&str, i64>("序号").to_string(),
                    &format2,
                )
                .unwrap();
            sheet
                .write(row_num, 1, row.get::<&str, &str>("名称"))
                .unwrap();
            sheet
                .write(row_num, 2, row.get::<&str, &str>("牌号"))
                .unwrap();
            sheet
                .write(row_num, 3, row.get::<&str, &str>("规格"))
                .unwrap();
            sheet
                .write(row_num, 4, row.get::<&str, &str>("状态"))
                .unwrap();
            sheet
                .write(row_num, 5, row.get::<&str, &str>("技术编号"))
                .unwrap();
            sheet
                .write(row_num, 6, row.get::<&str, &str>("供应商"))
                .unwrap();
            sheet
                .write_with_format(row_num, 7, row.get::<&str, &str>("库存支数"), &format2)
                .unwrap();
            sheet
                .write_with_format(row_num, 8, row.get::<&str, &str>("库存长度"), &format2)
                .unwrap();
            sheet
                .write_with_format(row_num, 9, row.get::<&str, &str>("库存重量"), &format2)
                .unwrap();
            sheet
                .write_with_format(row_num, 10, row.get::<&str, &str>("安全库存预警"), &format2)
                .unwrap();
            sheet
                .write_with_format(row_num, 11, row.get::<&str, &str>("最高库存预警"), &format2)
                .unwrap();
            sheet
                .write(row_num, 12, row.get::<&str, &str>("备注"))
                .unwrap();
        }

        wb.save(&file_path).unwrap();

        HttpResponse::Ok().json(file_name)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取产品信息筛选项
#[post("/get_product_info_filter_items")]
pub async fn get_product_info_filter_items(
    db: web::Data<Pool>,
    post_data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "产品信息".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let name = post_data["name"]
            .as_str()
            .unwrap_or("")
            .trim()
            .to_lowercase();
        let filter_name = post_data["filter_name"].as_str().unwrap_or("");
        let filter = post_data["filter"].as_str().unwrap_or("");

        let search_sql = if name != "" {
            format!(
                "AND (lower(material) like '%{}%' or lower(size) like '%{}%' or 
                    lower(status) like '%{}%' or lower(tech_no) like '%{}%')",
                name, name, name, name
            )
        } else {
            "".to_owned()
        };

        // 构建 filter 字符串
        let mut filter_sql = "".to_owned();
        if filter != "" {
            filter_sql = filter
                .replace("牌号", "material")
                .replace("规格", "size")
                .replace("状态", "status")
                .replace("技术编号", "tech_no")
                .replace("供应商", "supplier_id")
                .replace("(空白)", "");
        }

        // 映射中文字段名到数据库字段名并构建SQL
        let sql = match filter_name {
            "牌号" => format!(
                r#"
                SELECT DISTINCT material FROM product_info
                WHERE true {} {}
                ORDER BY material"#,
                search_sql, filter_sql
            ),
            "规格" => format!(
                r#"
                SELECT DISTINCT size FROM product_info
                WHERE true {} {}
                ORDER BY size"#,
                search_sql, filter_sql
            ),
            "状态" => format!(
                r#"
                SELECT DISTINCT status FROM product_info
                WHERE true {} {}
                ORDER BY status"#,
                search_sql, filter_sql
            ),
            "技术编号" => format!(
                r#"
                SELECT DISTINCT tech_no FROM product_info
                WHERE true {} {}
                ORDER BY tech_no"#,
                search_sql, filter_sql
            ),
            "供应商" => format!(
                r#"
                SELECT DISTINCT customers.文本字段1 FROM product_info
                join customers on product_info.supplier_id = customers.id
                WHERE true {} {}
                ORDER BY customers.文本字段1"#,
                search_sql, filter_sql
            ),
            _ => format!(
                r#"
                SELECT DISTINCT material FROM product_info
                WHERE true {} {}
                ORDER BY material"#,
                search_sql, filter_sql
            ),
        };

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut items: Vec<String> = Vec::new();

        for row in rows {
            items.push(row.get(0));
        }

        HttpResponse::Ok().json(items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取反审单据
#[post("/fetch_anti_shen")]
pub async fn fetch_anti_shen(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "反审单据".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let cate = post_data.cate.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let data: Vec<&str> = cate.split(SPLITER).collect();

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(日期) LIKE '%{name}%' 
                    OR LOWER(documents.备注) LIKE '%{name}%' OR LOWER(经办人) LIKE '%{name}%')"#,
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"反审日期>='{}' AND 反审日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号, 单号, 日期, 类别, 经办人, 布尔字段3 提交审核,
                文本字段10 审核, 反审人, 反审日期, 文本字段7 区域, 备注
            from documents
            where 反审人 <> '' and {query_date}{query_field} {NOT_DEL_SQL}
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#,
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = json!({
                "序号": row.get::<&str, i64>("序号").to_string(),
                "单号": row.get::<&str, String>("单号"),
                "日期": row.get::<&str, String>("日期"),
                "类别": row.get::<&str, String>("类别"),
                "经办人": row.get::<&str, String>("经办人"),
                "提交审核": row.get::<&str, bool>("提交审核"),
                "审核": row.get::<&str, String>("审核"),
                "反审人": row.get::<&str, String>("反审人"),
                "反审日期": row.get::<&str, String>("反审日期"),
                "区域": row.get::<&str, String>("区域"),
                "备注": row.get::<&str, String>("备注"),
            });

            products.push(fields);
        }

        let count_sql = format!(
            r#"select count(单号) as 记录数
                from documents
                where 反审人 <> '' and {query_date}{query_field} {NOT_DEL_SQL}"#
        );

        let row = &conn.query_one(count_sql.as_str(), &[]).await.unwrap();
        let count: i64 = row.get("记录数");
        let pages = (count as f64 / rec as f64).ceil() as i32;

        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}