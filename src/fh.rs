use crate::service::*;
use actix_identity::Identity;
use actix_web::{post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde_json::json;

#[post("/get_sale_out")]
pub async fn get_sale_out(
    db: web::Data<Pool>,
    xs_dh: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let dh: Vec<&str> = xs_dh.split("#").collect();
        let sql = &format!(
            r#"
            SELECT p.单号id || '　' || sum(p.数量) || '　' || sum(p.数量) - coalesce(max(已发), 0) 
                || '{SPLITER}' || sum(p.数量) || '　' || coalesce(max(已发), 0) item
            FROM pout_items p
            join documents d on d.单号 = p.单号id
            left join (
                select pi.单号id, sum(pi.数量) 已发 from fh_items fi 
                join pout_items pi on fi.出库id = pi.id 
                join sale_items si on pi.销售id = si.id 
                where si.单号id = '{}' and not_fei(fi.单号id)
                group by pi.单号id
            ) foo
            on p.单号id = foo.单号id
            WHERE 文本字段6 = '{}' and 类别='销售出库' and not_fei(p.单号id)
            group by p.单号id
            "#, dh[0], dh[0]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let item = row.get("item");
            document_items.push(item);
        }
        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//获取运输发货单明细
#[post("/fetch_trans_items")]
pub async fn fetch_trans_items(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"(select split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质,
                    规格型号 规格, 文本字段2 状态, 文本字段4 炉号, 长度, pi.数量, pi.理重, pi.重量, 单价, 
                    case when si.类型='按重量' then 单价*pi.重量 else 单价*pi.数量 end 金额, si.类型, 
                    出库id, fi.备注
                FROM fh_items fi
                join pout_items pi on pi.id = fi.出库id
                join sale_items si on si.id = pi.销售id
                join products p on p.物料号 = si.物料号
                JOIN tree ON 商品id = tree.num
                WHERE fi.单号id = '{}'
                ORDER BY fi.顺序)
                union all
                (select '锯口费', '--', '--', '--', '--', 0, 数量, 0, 0, 单价, 金额, 类型, '锯口费', '' 
                from sale_items
                where exists (select 1 from fh_items where 单号id = '{}' and 出库id = '锯口费')
                    and 单号id = (select distinct 文本字段6 from fh_items fi 
                        join documents d on d.单号 = fi.单号id 
                        WHERE fi.单号id = '{}')
                    and 物料号= '锯口费')
                "#,
            data.dh, data.dh, data.dh
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("炉号"),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                format!("{:.1}", row.get::<&str, f32>("理重")),
                format!("{:.1}", row.get::<&str, f32>("重量")),
                row.get::<&str, f32>("单价").to_string(),
                format!("{:.2}", row.get::<&str, f64>("金额")),
                row.get::<&str, String>("备注"),
                "checked".to_string(),
                row.get::<&str, String>("出库id"),
                row.get::<&str, String>("类型"),
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/get_items_trans")]
pub async fn get_items_trans(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "商品规格").await;
        let da: Vec<&str> = data.split("　").collect();
        let sale = da[0];  // 销售单号
        let ck = da[1];    // 出库单号

        let sql = &format!(
            r#"SELECT split_part(node_name,' ',2) || '　' || split_part(node_name,' ',1) || '　' ||
                {} || '　' || {} || '　' || {} || '　' || di.长度 || '　' || pi.数量 || '　' ||
                pi.理重 || '　' || pi.重量 || '　' || 单价 || '　' ||
                case when di.类型 = '按重量' then di.单价 * pi.重量 else di.单价 * pi.数量 end
                || '　' || pi.备注 || '　' || pi.id || '　' || di.类型 || '　' || pi.单号id item
            FROM pout_items pi
            join sale_items di on di.id = pi.销售id
            JOIN products ON products.物料号 = di.物料号
            JOIN tree ON 商品id = num
            WHERE pi.单号id = '{ck}'
                and pi.id not in (select 出库id from fh_items fi where 出库id like '{ck}%' and not_fei(单号id))
            	and not_fei(pi.单号id) and not_fei(di.单号id)
            order by pi.顺序"#,
            f_map["规格"], f_map["状态"], f_map["炉批号"]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let item = row.get("item");
            document_items.push(item);
        }

        // 补上锯口费
        let sql = format!(
            r#"SELECT '锯口费' || '　' || '--' || '　' || '' || '　' || '' || '　' || '' || '　' ||
                    长度 || '　' || 数量 || '　' || 理重 || '　' || 重量 || '　' || 单价 || '　' ||
                    di.金额 || '　' || di.备注 || '　' || di.物料号 || '　' || di.类型 || '　' || '{ck}' item
                from sale_items di
                join products on products.物料号 = di.物料号
                where not exists (
                    select 1 from fh_items 
                    where 单号id in (select 单号 from documents where 文本字段6 = '{sale}' and 类别='运输发货' {NOT_DEL_SQL})
                        and 出库id = '锯口费')
                    and di.单号id = (select 文本字段6 销售单号 from documents where 单号= '{ck}' {NOT_DEL_SQL}) 
                    and di.物料号 = '锯口费'"#,
        );

        // println!("{}", sql);

        let rows = conn.query(sql.as_str(), &[]).await.unwrap();
        for row in rows {
            let item = row.get("item");
            document_items.push(item);
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///保存运输发货单据
#[post("/save_transport")]
pub async fn save_transport(
    db: web::Data<Pool>,
    data: web::Json<crate::rk::Document>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();
        // let conn2 = db.get().await.unwrap();
        let doc_data: Vec<&str> = data.document.split(SPLITER).collect();
        let mut doc_sql;

        let fields_cate = "发货单据";

        let f_map = map_fields(&db, fields_cate).await;

        let fields = get_inout_fields(db.clone(), fields_cate).await;
        let dh_data = doc_data[1].to_owned();
        let mut dh = doc_data[1].to_owned();

        if dh_data == "新单据" {
            dh = get_dh(db.clone(), doc_data[0]).await;

            let mut init = "INSERT INTO documents (单号, 客商id,".to_owned();
            for f in &fields {
                init += &format!("{},", &*f.field_name);
            }

            init += &format!(
                "类别,{},{}) VALUES('{}', {},",
                f_map["经办人"],
                f_map["区域"],
                dh,
                0 // 0 是本公司的 id
            );

            doc_sql = build_sql_for_insert(&doc_data, init, &fields, 2);
            doc_sql += &format!("'{}','{}', '{}')", doc_data[0], user.name, user.area);
        } else {
            let init = "UPDATE documents SET ".to_owned();
            doc_sql = build_sql_for_update(doc_data.clone(), init, fields, 2);
            doc_sql += &format!(
                "类别='{}', {}='{}', {}='{}' WHERE 单号='{}'",
                doc_data[0], f_map["经办人"], user.name, f_map["区域"], user.area, dh
            );
        }

        // println!("{}", doc_sql);

        let transaction = conn.transaction().await.unwrap();
        transaction.execute(doc_sql.as_str(), &[]).await.unwrap();

        if dh_data != "新单据" {
            transaction
                .execute("DELETE FROM fh_items WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        }

        // let mut ckdh = "";
        for item in &data.items {
            let value: Vec<&str> = item.split(SPLITER).collect();
            let id = format!("{}-{}", dh, value[0]);
            let items_sql = format!(
                r#"INSERT INTO fh_items (id, 单号id, 备注, 出库id, 顺序)
                   VALUES('{}', '{}', '{}', '{}', {})"#,
                id,
                dh,
                value[2],
                value[3],
                value[0],
            );

            transaction.execute(items_sql.as_str(), &[]).await.unwrap();
        }

        let _result = transaction.commit().await;

        HttpResponse::Ok().json(dh)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 发货单获得销售单据
#[post("/materialout_docs")]
pub async fn materialout_docs(
    db: web::Data<Pool>,
    search: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        let f_map2 = map_fields(&db, "客户").await;
        let f_map3 = map_fields(&db, "出库单据").await;

        let limit = get_limits(&user).await;
        let sql = &format!(
            r#"SELECT 单号 as id, 单号 || '　' || customers.{} AS label 
            FROM documents
            join customers on 客商id = customers.id            
            WHERE {} documents.类别='{}' AND documents.{} <> '' AND documents.{} = false and 单号 in 
            (select {} from documents where {} <>'' and 类别='销售出库' and  {} <> '' and {} = false {}) and
            单号 not in (select 文本字段6 from documents where documents.类别='运输发货' and 文本字段10 = '' {}) {}
            order by 单号 desc
            "#,
            f_map2["简称"],
            limit,
            search,
            f_map["审核"],
            f_map["发货完成"],
            f_map3["销售单号"],
            f_map3["销售单号"],
            f_map3["审核"],
            f_map3["发货完成"],
            NOT_DEL_SQL,
            NOT_DEL_SQL,
            NOT_DEL_SQL
        );

        // println!("{}", sql);
        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 发货单已保存未提交单据
#[post("/materialout_saved_docs")]
pub async fn materialout_saved_docs(
    db: web::Data<Pool>,
    search: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        let f_map2 = map_fields(&db, "客户").await;
        let f_map3 = map_fields(&db, "出库单据").await;

        let limit = get_limits(&user).await;
        let sql = &format!(
            r#"SELECT 运输单号 as id, 单号 || '　' || customers.{} || '　' || 运输单.经办人 AS label FROM documents
            join customers on 客商id = customers.id
            join 
            (select 单号 运输单号, 文本字段6, 经办人 from documents where documents.类别='运输发货' and 
            布尔字段3 = false and 文本字段10 = '' {NOT_DEL_SQL}) as 运输单
            on 运输单.文本字段6 = documents.单号
            WHERE {limit} documents.类别='{search}' AND documents.{} <> '' AND documents.{} = false and 单号 in 
            (select {} from documents where {} <>'' and 类别='销售出库' and  {} <> '' {NOT_DEL_SQL}) {NOT_DEL_SQL}
            order by 单号 desc
            "#,
            f_map2["简称"],
            f_map["审核"],
            f_map["发货完成"],
            f_map3["销售单号"],
            f_map3["销售单号"],
            f_map3["审核"]
        );

        // println!("{}", sql);
        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取发货单据字段
#[post("/fetch_document_fh")]
pub async fn fetch_document_fh(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let fields = get_inout_fields(db.clone(), &data.cate).await;
        let f_map = map_fields(&db, &data.cate).await;

        let mut sql_fields = "SELECT ".to_owned();

        for f in &fields {
            sql_fields += &format!("documents.{},", f.field_name);
        }

        let sql = format!(
            r#"{} 作废, documents.{} as 提交审核, 客商id, 名称, documents.{} as 审核, 经办人, documents.{} 图片
            FROM documents
            JOIN customers ON documents.客商id=customers.id WHERE 单号='{}'"#,
            sql_fields, f_map["提交审核"], f_map["审核"], f_map["图片"], data.dh
        );

        // println!("{}", sql);

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
        let f_str = simple_string_from_base(row, &fields);
        let fds = f_str.split(SPLITER).collect::<Vec<&str>>();

        let document = json!({
            "销售单号": fds[0],
            "合同号": fds[1],
            "发货方式": fds[2],
            "客户": fds[3],
            "收货人": fds[4],
            "收货电话": fds[5],
            "收货地址": fds[6],
            "提货车牌": fds[7],
            "司机电话": fds[8],
            "备注": fds[9],
            "发货日期": fds[10],
            "名称": row.get::<&str, String>("名称"),
            "图片": row.get::<&str, String>("图片"),
            "提交审核": row.get::<&str, bool>("提交审核"),
            "客户id": row.get::<&str, i32>("客商id"),
            "审核": row.get::<&str, String>("审核"),
            "经办人": row.get::<&str, String>("经办人"),
            "作废": row.get::<&str, bool>("作废"),
        });

        HttpResponse::Ok().json(document)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/get_trans_info")]
pub async fn get_trans_info(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let dh: Vec<&str> = data.split("#").collect();
        let f_map = map_fields(&db, "销售单据").await;
        let f_map2 = map_fields(&db, "客户").await;
        let sql = &format!(
            r#"SELECT documents.{} as 合同编号, 名称, customers.{} 联系人, customers.{} 电话,
            customers.{} 公司地址, documents.{} 审核 from documents
            JOIN customers ON 客商id = customers.id
            WHERE 单号 = '{}' {NOT_DEL_SQL}"#,
            f_map["合同编号"],
            f_map2["收货人"],
            f_map2["收货电话"],
            f_map2["收货地址"],
            f_map["审核"],
            dh[0]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut item = "".to_owned();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("合同编号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("联系人"),
                row.get::<&str, String>("电话"),
                row.get::<&str, String>("公司地址"),
                row.get::<&str, String>("审核"),
            ];

            item = sp_query(fields);
        }
        HttpResponse::Ok().json(item)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 确认销售单的发货完成
#[post("/make_fh_complete")]
pub async fn make_fh_complete(
    db: web::Data<Pool>,
    xs_dh: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"update documents set 布尔字段1 = true where 单号 ='{xs_dh}' and 布尔字段2 = true and
                (select sum(数量) from sale_items where 单号id ='{xs_dh}' and 物料号 <> '锯口费') =
                (select coalesce(sum(pi.数量),0) from fh_items fi 
                 join pout_items pi on fi.出库id = pi.id 
                 where 销售id like '{xs_dh}%' and not_shen_fei(fi.单号id) and not_shen_fei(pi.单号id))
                "#,
        );

        // println!("{}", sql);

        let _ = conn.query(sql.as_str(), &[]).await;

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
