use crate::service::*;
use actix_identity::Identity;
use actix_multipart::Multipart;
use actix_web::{get, post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde::Deserialize;
use serde_json::{json, Value};
use std::fs;
use uuid::Uuid;

// 牌号自动输入
#[get("/get_material")]
pub async fn get_material(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let s = search.s.to_lowercase();

        let sql = &format!(
            r#"
                SELECT id, label FROM (
                    SELECT 
                        num AS id, 
                        split_part(node_name, ' ', 1) AS label,
                        ROW_NUMBER() OVER (PARTITION BY split_part(node_name, ' ', 1) ORDER BY num) AS rn
                    FROM tree
                    WHERE LOWER(node_name) LIKE '%{s}%' AND not_use=false
                ) t
                WHERE rn = 1
                limit 10
            "#
        );

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取编号最大数字
#[post("/get_number")]
pub async fn get_number(db: web::Data<Pool>, data: web::Json<Value>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                SELECT COALESCE(MAX(
                CAST(
                    REGEXP_REPLACE(
                    SUBSTRING(tech_no FROM '{}(\d+)'), 
                    '[^0-9]', '', 'g'
                    ) AS INTEGER
                )
                ), 0) AS max_number
                FROM tech_buy
                WHERE tech_no like 'FS-T-M-{}%' and fei=false;
            "#,
            data["productType"].to_string().trim_matches('"'),
            data["productType"].to_string().trim_matches('"'),
        );

        let row = conn.query_one(sql.as_str(), &[]).await.unwrap();
        let num: i32 = row.get::<&str, i32>("max_number");

        HttpResponse::Ok().json(num + 1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 保存采购技术协议
#[post("/save_techbuy")]
pub async fn save_techbuy(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        save_techbuy_data(&db, &data, &user.name).await;
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 提交审核
#[post("/shen_techbuy")]
pub async fn shen_techbuy(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        save_techbuy_data(&db, &data, &user.name).await;

        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"update tech_buy set sumit_review = true where id::text = '{}'"#,
            data["id"].to_string().trim_matches('"')
        );

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 本地函数：保存采购技术协议数据
async fn save_techbuy_data(db: &web::Data<Pool>, data: &web::Json<Value>, user_name: &str) {
    let conn = db.get().await.unwrap();

    let sql = format!(
        r#"insert into tech_buy (id, name, ver, tech_no, p_type, p_material, build_date, content, editor, note) 
            values ('{}', '{}', '{}', '{}', '{}', '{}', '{}', $${}$$, '{}', '{}')
            ON CONFLICT (id) DO UPDATE 
            SET name = EXCLUDED.name,
                ver = EXCLUDED.ver,
                tech_no = EXCLUDED.tech_no,
                p_type = EXCLUDED.p_type,
                p_material = EXCLUDED.p_material,
                build_date = EXCLUDED.build_date,
                content = EXCLUDED.content,
                editor = EXCLUDED.editor,
                note = EXCLUDED.note;
            "#,
        data["id"].to_string().trim_matches('"'),
        data["specName"].to_string().trim_matches('"'),
        data["ver"].to_string().trim_matches('"'),
        data["specNumber"].to_string().trim_matches('"'),
        data["productType"].to_string().trim_matches('"'),
        data["material"].to_string().trim_matches('"'),
        data["buildDate"].to_string().trim_matches('"'),
        data["content"].to_string().trim_matches('"'),
        user_name,
        data["specNote"].to_string().trim_matches('"')
    );

    let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();
}

// 获取采购技术协议
#[post("/get_tech_buy")]
pub async fn get_tech_buy(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let sql = format!(
            r#"
            select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号, id::text id, name, tech_no, p_type, p_material, editor,
                build_date::text, sumit_review, review_name, COALESCE(review_date::text, '') as review_date, note, fei,
                COALESCE(s.docs[1], '') AS supplier1, COALESCE(s.docs[2], '') AS supplier2, COALESCE(s.docs[3], '') AS supplier3, 
                COALESCE(s.pdfs[1], '') AS pdf1, COALESCE(s.pdfs[2], '') AS pdf2, COALESCE(s.pdfs[3], '') AS pdf3
            from tech_buy t1
            LEFT JOIN (
                SELECT 
                    tech_id,
                    array_agg(doc_no) AS docs,
                    array_agg(pdf) AS pdfs
                FROM 
                tech_suplier
                GROUP BY 
                    tech_id
            ) s ON t1.id = s.tech_id
            where lower(name) like '%{name}%'
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = json!({
                    "序号": row.get::<&str, i64>("序号").to_string(),
                    "id": row.get::<&str, String>("id"),
                    "名称": row.get::<&str, String>("name"),
                    "编号": row.get::<&str, String>("tech_no"),
                    "产品类型": row.get::<&str, String>("p_type"),
                    "牌号": row.get::<&str, String>("p_material"),
                    "供应商1": row.get::<&str, String>("supplier1"),
                    "供应商2": row.get::<&str, String>("supplier2"),
                    "供应商3": row.get::<&str, String>("supplier3"),
                    "PDF1": row.get::<&str, String>("pdf1"),
                    "PDF2": row.get::<&str, String>("pdf2"),
                    "PDF3": row.get::<&str, String>("pdf3"),
                    "编辑人": row.get::<&str, String>("editor"),
                    "编辑日期": row.get::<&str, String>("build_date"),
                    "提交审核": row.get::<&str, bool>("sumit_review"),
                    "审核人": row.get::<&str, String>("review_name"),
                    "生效日期": row.get::<&str, String>("review_date"),
                    "说明": row.get::<&str, String>("note"),
                    "作废": row.get::<&str, bool>("fei"),
            });

            products.push(fields);
        }

        let count_sql = format!(
            r#"
            select count(*) as 记录数 from tech_buy
            where name like '%{name}%'"#
        );

        // println!("{}", count_sql);

        let (pages, count) = pages(&conn, count_sql, post_data.rec).await;
        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[derive(Deserialize)]
pub struct TechId {
    pub id: String,
}

// 获取单个采购技术协议数据
#[get("/get_tech_buy_data")]
pub async fn get_tech_buy_data(
    db: web::Data<Pool>,
    data: web::Query<TechId>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select id::text, name, ver, tech_no, p_type, p_material, build_date::text, content, sumit_review, review_name, 
                COALESCE(review_date::text, '') as review_date, note, fei from tech_buy where id::text = '{}'"#,
            data.id
        );

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();

        let doc = json!({
                "id": row.get::<&str, String>("id"),
                "name": row.get::<&str, String>("name"),
                "ver": row.get::<&str, String>("ver"),
                "tech_no": row.get::<&str, String>("tech_no"),
                "p_type": row.get::<&str, String>("p_type"),
                "p_material": row.get::<&str, String>("p_material"),
                "content": row.get::<&str, String>("content"),
                "build_date": row.get::<&str, String>("build_date"),
                "sumit_review": row.get::<&str, bool>("sumit_review"),
                "review_name": row.get::<&str, String>("review_name"),
                "review_date": row.get::<&str, String>("review_date"),
                "note": row.get::<&str, String>("note"),
                "fei": row.get::<&str, bool>("fei"),
        });

        HttpResponse::Ok().json(doc)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 审核通过
#[post("/pass_techbuy")]
pub async fn pass_techbuy(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术审核".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"update tech_buy set review_name = '{}', review_date = CURRENT_DATE where id::text = '{}'"#,
            user.name, data
        );

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 审核拒绝
#[post("/reject_techbuy")]
pub async fn reject_techbuy(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术审核".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"update tech_buy set sumit_review = false, review_name = '', review_date = null where id::text = '{}'"#,
            data
        );

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//供应商自动输入
#[get("/get_suplier_auto")]
pub async fn get_suplier_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let s = search.s.to_lowercase();
        let cate_s = if search.cate != "" {
            format!("类别 like '%{}%' AND ", search.cate)
        } else {
            "".to_string()
        };
        let sql = &format!(
            r#"SELECT id::text, 文本字段1 AS label FROM customers 
            WHERE {cate_s} (助记码 LIKE '%{s}%' OR LOWER(名称) LIKE '%{s}%') AND 停用=false LIMIT 10"#
        );

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[derive(Deserialize)]
pub struct SavePdfQuery {
    pub name: Option<String>,
}

#[post("/pdf_save")]
pub async fn pdf_save(
    db: web::Data<Pool>,
    query: web::Query<SavePdfQuery>,
    payload: Multipart,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let pdf = save_pdf_with_name(payload, query.name.clone()).await;
        HttpResponse::Ok().json(pdf)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

async fn save_pdf_with_name(payload: Multipart, suggested: Option<String>) -> String {
    let path = "./upload/pdf/suplier.pdf".to_owned();
    let result = save_pic(payload, path.clone()).await.unwrap();
    if result == "-3" {
        return "-3".to_owned();
    }
    // 构造最终文件名
    let mut base = suggested.unwrap_or_else(|| Uuid::new_v4().to_string());
    // 简单清洗非法字符与空白
    base = base
        .replace('/', "_")
        .replace('\\', "_")
        .replace(':', "_")
        .replace('*', "_")
        .replace('?', "_")
        .replace('"', "_")
        .replace('<', "_")
        .replace('>', "_")
        .replace('|', "_")
        .trim()
        .trim_matches('.')
        .to_string();
    if base.is_empty() {
        base = Uuid::new_v4().to_string();
    }

    let pdf = format!("/upload/pdf/{}.pdf", base);
    fs::rename("./upload/pdf/suplier.pdf", format!(".{}", pdf)).unwrap();
    pdf
}

#[post("/pdf_save_sale")]
pub async fn pdf_save_sale(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"update tech_sale set pdf_url='{}' where id::text = '{}'"#,
            data["pdf"].to_string().trim_matches('"'),
            data["id"].to_string().trim_matches('"')
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 删除关联供应商
#[post("/delete_suppliers")]
pub async fn delete_suppliers(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"delete from tech_suplier where tech_id::text = '{}'"#,
            data
        );

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 添加关联供应商
#[post("/add_supplier")]
pub async fn add_supplier(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"insert into tech_suplier (tech_id, supplier_id, doc_no, pdf) values ('{}', {}, '{}', '{}')"#,
            data["doc_id"].to_string().trim_matches('"'),
            data["suplier_id"].to_string().trim_matches('"'),
            data["doc_no"].to_string().trim_matches('"'),
            data["pdf"].to_string().trim_matches('"')
        );

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取供应商
#[post("/fetch_suppliers")]
pub async fn fetch_suppliers(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select supplier_id, doc_no, pdf, COALESCE(文本字段1, 名称) as name 
                from tech_suplier t
                join customers c on t.supplier_id = c.id
                where tech_id::text = '{}'"#,
            data
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let suppliers = rows
            .iter()
            .map(|row| {
                json!({
                    "supplier_id": row.get::<&str, i32>("supplier_id"),
                    "doc_no": row.get::<&str, String>("doc_no"),
                    "pdf": row.get::<&str, String>("pdf"),
                    "name": row.get::<&str, String>("name"),
                })
            })
            .collect::<Vec<_>>();

        HttpResponse::Ok().json(suppliers)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 协议修订
#[post("/revise_techbuy")]
pub async fn revise_techbuy(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
            INSERT INTO tech_buy (name, ver, tech_no, p_type, p_material, build_date, content, editor, note)
                SELECT name, chr(ascii(ver) + 1), regexp_replace(tech_no, '.$', chr(ascii(ver) + 1)),  
                    p_type, p_material, CURRENT_DATE, content, editor, note
                FROM tech_buy 
                WHERE id::text = '{}'
                RETURNING id::text;
            "#,
            data["id"].to_string().trim_matches('"'),
        );

        let row = conn.query_one(sql.as_str(), &[]).await.unwrap();
        let id: String = row.get::<&str, String>("id");

        // 关联供应商
        let sql = format!(
            r#"
                INSERT INTO tech_suplier (tech_id, supplier_id, doc_no, pdf)
                SELECT '{}', supplier_id, doc_no, pdf
                FROM tech_suplier 
                WHERE tech_id::text = '{}';
            "#,
            id,
            data["id"].to_string().trim_matches('"'),
        );

        let _row = conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(id)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 添加修订履历
#[post("/submit_revision")]
pub async fn submit_revision(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                insert into tech_revision (tech_id, section, content, note) 
                values ('{}', '{}', '{}', '{}')
                ON CONFLICT (tech_id) DO UPDATE 
                SET section = EXCLUDED.section,
                content = EXCLUDED.content,
                note = EXCLUDED.note
            "#,
            data["tech_id"].to_string().trim_matches('"'),
            data["section"].to_string().trim_matches('"'),
            data["content"].to_string().trim_matches('"'),
            data["note"].to_string().trim_matches('"')
        );

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取单一修订履历
#[get("/get_revision")]
pub async fn get_revision(
    db: web::Data<Pool>,
    data: web::Query<TechId>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                select section, COALESCE(content, '') content, COALESCE(note, '') note
                from tech_revision 
                where tech_id::text = '{}'"#,
            data.id
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        if rows.is_empty() {
            return HttpResponse::Ok().json(0);
        }

        let row = &rows[0];

        let doc = json!({
                "section": row.get::<&str, String>("section"),
                "content": row.get::<&str, String>("content"),
                "note": row.get::<&str, String>("note"),
        });

        HttpResponse::Ok().json(doc)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取全部修订履历
#[post("/get_revise_data")]
pub async fn get_revise_data(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                select ver, COALESCE(section, '') section, COALESCE(tr.content, '') content, 
                    COALESCE(review_date::text, '') review_date, COALESCE(tr.note, '') note
                from tech_buy
                left join tech_revision tr on tr.tech_id = tech_buy.id
                where name = '{}' and regexp_replace(tech_no, '.$', '') = '{}' and build_date <= '{}'
                order by ver;
            "#,
            data["name"].to_string().trim_matches('"'),
            data["tech_no"].to_string().trim_matches('"'),
            data["date"].to_string().trim_matches('"'),
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut docs = Vec::new();
        for row in rows {
            let doc = json!({
                "ver": row.get::<&str, String>("ver"),
                "section": row.get::<&str, String>("section"),
                "content": row.get::<&str, String>("content"),
                "date": row.get::<&str, String>("review_date"),
                "note": row.get::<&str, String>("note"),
            });
            docs.push(doc);
        }

        HttpResponse::Ok().json(docs)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 作废采购协议
#[post("/fei_techbuy")]
pub async fn fei_techbuy(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
            update tech_buy set fei = true WHERE id::text = '{}'
            "#,
            data["id"].to_string().trim_matches('"'),
        );

        let _row = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// ------------ 客户协议 --------------

// 保存客户技术规范
#[post("/save_techsale")]
pub async fn save_techsale(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"insert into tech_sale (id, customer_id, name, tech_no, p_type, p_material, build_date, content, editor) 
            values ('{}', {}, '{}', '{}', '{}', '{}', '{}', $${}$$, '{}')
            ON CONFLICT (id) DO UPDATE 
            SET name = EXCLUDED.name,
                customer_id = EXCLUDED.customer_id,
                tech_no = EXCLUDED.tech_no,
                p_type = EXCLUDED.p_type,
                p_material = EXCLUDED.p_material,
                build_date = EXCLUDED.build_date,
                content = EXCLUDED.content,
                editor = EXCLUDED.editor;
                "#,
            data["id"].to_string().trim_matches('"'),
            data["customer"].to_string().trim_matches('"'),
            data["specName"].to_string().trim_matches('"'),
            data["specNumber"].to_string().trim_matches('"'),
            data["productType"].to_string().trim_matches('"'),
            data["material"].to_string().trim_matches('"'),
            data["buildDate"].to_string().trim_matches('"'),
            data["content"].to_string().trim_matches('"'),
            user.name
        );

        // println!("{}", sql);

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取采购技术协议
#[post("/get_tech_sale")]
pub async fn get_tech_sale(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let sql = format!(
            r#"
            select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号, t.id::text id, 名称, 
                name, tech_no, p_type, p_material, build_date::text, editor, fei, 
                COALESCE(pdf_url, '') as pdf_url
            from tech_sale t
            join customers c on c.id = t.customer_id
            where 名称 like '%{name}%'
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = json!({
                    "序号": row.get::<&str, i64>("序号").to_string(),
                    "id": row.get::<&str, String>("id"),
                    "客户": row.get::<&str, String>("名称"),
                    "名称": row.get::<&str, String>("name"),
                    "编号": row.get::<&str, String>("tech_no"),
                    "产品类型": row.get::<&str, String>("p_type"),
                    "牌号": row.get::<&str, String>("p_material"),
                    "编辑日期": row.get::<&str, String>("build_date"),
                    "编辑人": row.get::<&str, String>("editor"),
                    "作废": row.get::<&str, bool>("fei"),
                    "pdf_url": row.get::<&str, String>("pdf_url"),
            });

            products.push(fields);
        }

        let count_sql = format!(
            r#"
            select count(*) as 记录数 from tech_sale
            where name like '%{name}%'"#
        );

        // println!("{}", count_sql);

        let (pages, count) = pages(&conn, count_sql, post_data.rec).await;
        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取单个客户协议数据
#[get("/get_tech_sale_data")]
pub async fn get_tech_sale_data(
    db: web::Data<Pool>,
    data: web::Query<TechId>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
                select t.id::text, customer_id, 名称, name, tech_no, p_type, p_material, build_date::text, content, 
                    COALESCE(pdf_url, '') as pdf_url, fei
                from tech_sale t
                join customers c on c.id = t.customer_id
                where t.id::text = '{}'"#,
            data.id
        );

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();

        let doc = json!({
                "id": row.get::<&str, String>("id"),
                "name": row.get::<&str, String>("name"),
                "customer": row.get::<&str, String>("名称"),
                "customer_id": row.get::<&str, i32>("customer_id"),
                "tech_no": row.get::<&str, String>("tech_no"),
                "p_type": row.get::<&str, String>("p_type"),
                "p_material": row.get::<&str, String>("p_material"),
                "content": row.get::<&str, String>("content"),
                "build_date": row.get::<&str, String>("build_date"),
                "pdf_url": row.get::<&str, String>("pdf_url"),
                "fei": row.get::<&str, bool>("fei"),
        });

        HttpResponse::Ok().json(doc)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 作废采购协议
#[post("/fei_techsale")]
pub async fn fei_techsale(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"
            update tech_sale set fei = true WHERE id::text = '{}'
            "#,
            data["id"].to_string().trim_matches('"'),
        );

        let _row = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
