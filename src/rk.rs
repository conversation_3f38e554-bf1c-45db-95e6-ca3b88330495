use crate::service::*;
use actix_identity::Identity;
use actix_multipart::Multipart;
use actix_web::{get, post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::fs;

//自动完成
#[get("/material_auto")]
pub async fn material_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "采购单据").await;
        let f_map2 = map_fields(&db, "供应商").await;
        let s = search.s.to_uppercase();
        let cate_s = if search.cate != "" {
            format!("documents.类别='{}' AND ", search.cate)
        } else {
            "".to_string()
        };
        let sql = &format!(
            r#"SELECT 单号 as id, 单号 || '　' || customers.{} AS label FROM documents
            JOIN customers on 客商id = customers.id
            WHERE {} 单号 like '%{}%' AND documents.{}=false AND documents.文本字段10 <> '' {} LIMIT 10"#,
            f_map2["简称"], cate_s, s, f_map["入库完成"], NOT_DEL_SQL
        );

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 入库单获得采购单据
#[post("/materialin_docs")]
pub async fn materialin_docs(
    db: web::Data<Pool>,
    search: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "采购入库".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "采购单据").await;
        let f_map2 = map_fields(&db, "供应商").await;

        let sql = &format!(
            r#"SELECT 单号 as id, 单号 || '　' || customers.{} AS label FROM documents
            join customers on 客商id = customers.id
            WHERE documents.类别='{search}' AND documents.{}=false AND documents.{} <> '' 
                and 单号 not in (select 文本字段6 from documents where documents.类别='采购入库'
                and 文本字段10 = '' {NOT_DEL_SQL}) {NOT_DEL_SQL}
            order by 单号 desc"#,
            f_map2["简称"], f_map["入库完成"], f_map["审核"]
        );

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 入库单保存未提交
#[post("/materialin_saved_docs")]
pub async fn materialin_saved_docs(
    db: web::Data<Pool>,
    search: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "采购入库".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "采购单据").await;
        let f_map2 = map_fields(&db, "供应商").await;

        let sql = &format!(
            r#"SELECT 入库单号 as id, 单号 || '　' || customers.{} || '　' || 入库单.经办人 AS label
            FROM documents
            join customers on 客商id = customers.id
            join 
                (select 单号 入库单号, 文本字段6, 经办人 from documents where documents.类别='采购入库' and
                布尔字段3 = false and 文本字段10 = '' {NOT_DEL_SQL}) as 入库单
            on 入库单.文本字段6 = documents.单号
            WHERE documents.类别='{}' AND documents.{}=false AND documents.{} <> '' {NOT_DEL_SQL}
            order by 单号 desc"#,
            f_map2["简称"], search, f_map["入库完成"], f_map["审核"]
        );

        // println!("{}", sql);

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/material_auto_kt")]
pub async fn material_auto_kt(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "商品规格").await;
        let sql = &format!(
            r#"SELECT num as id, products.物料号 || '{SPLITER}' || split_part(node_name,' ',2) || '{SPLITER}' ||
                split_part(node_name,' ',1) || '{SPLITER}' || products.{} || '{SPLITER}' || products.{}
                || '{SPLITER}' || 库存长度 AS label
                FROM products
                JOIN tree ON products.商品id = tree.num
                JOIN documents ON 单号id = 单号
                LEFT JOIN length_weight() foo
                ON products.物料号 = foo.物料号
                WHERE LOWER(products.物料号) LIKE '%{}%' AND 库存状态 <> '已切完' AND 库存长度 > 10 AND
                documents.文本字段10 !='' {NOT_DEL_SQL} LIMIT 10"#,
            f_map["规格"],
            f_map["状态"],
            search.s.to_lowercase(),
        );

        // println!("{}", sql);

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/get_items")]
pub async fn get_items(db: web::Data<Pool>, dh: web::Json<String>, id: Identity) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let dh_ar = dh.split("#").collect::<Vec<&str>>();
        let sql = &format!(
            r#"SELECT num || '{SPLITER}' || split_part(node_name,' ',2) || '　' ||
                split_part(node_name,' ',1) || '　' || 规格 || '　' || 状态 ||  '　' ||
                customers.文本字段1 || '　' || 执行标准 || '{SPLITER}' || bi.id || 
                '{SPLITER}' || 
                EXISTS (SELECT 1 FROM products WHERE 单号id = '{}' and not_fei(单号id)) item
            from buy_items bi
            JOIN tree ON 商品id = tree.num
            JOIN documents on 单号id = 单号
            JOIN customers on 客商id = customers.id
            WHERE 单号id = '{}' and not_shen_fei(bi.单号id) 
            order by 顺序"#,
            dh_ar[1], dh_ar[0]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let item = row.get("item");
            document_items.push(item);
        }
        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//获取最近的单号
#[get("/fetch_max_num")]
async fn fetch_max_num(db: web::Data<Pool>, id: Identity) -> String {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "商品规格").await;
        let sql = &format!(
            r#"select max(cast(SUBSTRING({}, 2, 6) as integer))::text as num from products where {} like 'M%';"#,
            f_map["物料号"], f_map["物料号"]
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut num = "".to_owned();
        for row in rows {
            num = row.get("num");
        }
        num
    } else {
        "".to_owned()
    }
}

//上传炉号质保书
#[get("/set_lu")]
async fn set_lu(db: web::Data<Pool>, lu: String, id: Identity) -> String {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let sql = &format!(r#"insert into lu (炉号, 质保书) values ('{lu}', '{lu}')"#);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut num = "".to_owned();
        for row in rows {
            num = row.get("num");
        }
        num
    } else {
        "".to_owned()
    }
}

// buyin.rs 中相同，需整合
#[derive(Deserialize, Serialize)]
pub struct Document {
    pub rights: String,
    pub document: String,
    pub remember: String,
    pub items: Vec<String>,
}

///保存入库单据
#[post("/save_material")]
pub async fn save_material(
    db: web::Data<Pool>,
    data: web::Json<Document>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();
        let doc_data: Vec<&str> = data.document.split(SPLITER).collect();
        let mut doc_sql;

        let fields_cate = if data.rights == "采购入库" {
            "入库单据"
        } else {
            "库存调入"
        };

        let f_map = map_fields(&db, fields_cate).await;

        let fields = get_inout_fields(db.clone(), fields_cate).await;
        let dh_data = doc_data[1].to_owned();
        let mut dh = doc_data[1].to_owned();

        if dh_data == "新单据" {
            dh = get_dh(db.clone(), doc_data[0]).await;

            let mut init = "INSERT INTO documents (单号, 客商id,".to_owned();
            for f in &fields {
                init += &format!("{},", &*f.field_name);
            }

            init += &format!(
                "类别,{},{}) VALUES('{}', {},",
                f_map["经办人"],
                f_map["区域"],
                dh,
                0 // 0 是本公司的 id
            );

            doc_sql = build_sql_for_insert(&doc_data, init, &fields, 2);
            doc_sql += &format!("'{}','{}', '{}')", doc_data[0], user.name, user.area);
        } else {
            let init = "UPDATE documents SET ".to_owned();
            doc_sql = build_sql_for_update(doc_data.clone(), init, fields, 2);
            doc_sql += &format!(
                "类别='{}', {}='{}', {}='{}' WHERE 单号='{}'",
                doc_data[0], f_map["经办人"], user.name, f_map["区域"], user.area, dh
            );
        }

        // println!("{}", doc_sql);

        let transaction = conn.transaction().await.unwrap();
        transaction.execute(doc_sql.as_str(), &[]).await.unwrap();

        if dh_data != "新单据" {
            transaction
                .execute("DELETE FROM products WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        }

        let f_map = map_fields(&db, "商品规格").await;

        // let mut rkid = "";
        for item in &data.items {
            let value: Vec<&str> = item.split(SPLITER).collect();

            let lib = if value[10] == "销售" { "" } else { value[10] }; //库存类别

            let items_sql = if fields_cate == "入库单据" {
                format!(
                    r#"INSERT INTO products (单号id, 商品id, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, 入库id)
                     VALUES('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', {}, {}, {}, '{}', '{}', '{}', '{}', '{}', {}, '{}')"#,
                    f_map["规格"],
                    f_map["状态"],
                    f_map["炉批号"],
                    f_map["执行标准"],
                    f_map["生产厂家"],
                    f_map["物料号"],
                    f_map["入库长度"],
                    f_map["库存长度"],
                    f_map["理论重量"],
                    f_map["外径壁厚"],
                    f_map["库存类别"],
                    f_map["备注"],
                    f_map["质检书"],
                    f_map["区域"],
                    f_map["顺序"],
                    dh,
                    value[12],
                    value[1],
                    value[2],
                    value[3],
                    value[4],
                    value[5],
                    value[6],
                    value[7],
                    value[7],
                    value[8],
                    value[9],
                    lib,
                    value[11],
                    value[14],
                    user.area,
                    value[0],
                    value[13]
                )
            } else {
                format!(
                    r#"INSERT INTO products (单号id, 商品id, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {})
                     VALUES('{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', {}, {}, {}, '{}', {}, '{}', '{}')"#,
                    f_map["规格"],
                    f_map["状态"],
                    f_map["执行标准"],
                    f_map["炉批号"],
                    f_map["生产厂家"],
                    f_map["库位"],
                    f_map["物料号"],
                    f_map["入库长度"],
                    f_map["库存长度"],
                    f_map["理论重量"],
                    f_map["备注"],
                    f_map["顺序"],
                    f_map["区域"],
                    f_map["原物料号"],
                    dh,
                    value[11],
                    value[1],
                    value[2],
                    value[3],
                    value[4],
                    value[5],
                    value[6],
                    value[7],
                    value[8],
                    value[8],
                    value[9],
                    value[10],
                    value[0],
                    user.area,
                    value[12] // value[12] 是原物料号
                )
            };

            // println!("{}", items_sql);

            // if value.len() > 13 && value[13] != "" {
            //     if rkid != value[13] {
            //         rkid = value[13];
            //         let dh_sql = format!(
            //             r#"update buy_items set 入库完成 = true where id = '{}'"#,
            //             rkid
            //         );
            //         transaction.execute(dh_sql.as_str(), &[]).await.unwrap();
            //     }
            // }

            let re = transaction
                .execute(items_sql.as_str(), &[])
                .await
                .unwrap_or(0);

            if re == 0 {
                return HttpResponse::Ok().json(-2); //物料号重复时无法保存
            }
        }

        let _result = transaction.commit().await;

        HttpResponse::Ok().json(dh)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取入库单据字段
#[post("/fetch_document_rkd")]
pub async fn fetch_document_rkd(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let fields = get_inout_fields(db.clone(), &data.cate).await;
        let f_map = map_fields(&db, &data.cate).await;

        let mut sql_fields = "SELECT ".to_owned();

        for f in &fields {
            sql_fields += &format!("documents.{},", f.field_name);
        }

        let sql = format!(
            r#"{} 作废, documents.{} as 图片, documents.{} as 提交审核, 客商id, 名称, documents.{} as 审核, 经办人
            FROM documents
            JOIN customers ON documents.客商id=customers.id WHERE 单号='{}'"#,
            sql_fields, f_map["图片"], f_map["提交审核"], f_map["审核"], data.dh
        );

        // println!("{}", sql);

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();

        let f_str = simple_string_from_base(row, &fields);
        let fds = f_str.split(SPLITER).collect::<Vec<&str>>();

        let document = json!({
            "采购单号": fds[0],
            "入库日期": fds[1],
            "到货日期": fds[2],
            "来料重量": fds[3],
            "实际重量": fds[4],
            "理论重量": fds[5],
            "备注": fds[6],
            "名称": row.get::<&str, String>("名称"),
            "图片": row.get::<&str, String>("图片"),
            "提交审核": row.get::<&str, bool>("提交审核"),
            "客户id": row.get::<&str, i32>("客商id"),
            "审核": row.get::<&str, String>("审核"),
            "经办人": row.get::<&str, String>("经办人"),
            "作废": row.get::<&str, bool>("作废"),
        });

        HttpResponse::Ok().json(document)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取入库单据明细
#[post("/fetch_document_items_tr")]
pub async fn fetch_document_items_tr(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "商品规格").await;
        let sql = format!(
            r#"select {} 原物料号, split_part(node_name,' ', 2) as 名称, split_part(node_name,' ', 1) as 材质,
                {} as 规格, {} as 状态, {} as 炉号, {} as 执行标准, {} as 生产厂家, {} as 库位, {} as 物料号, {} as 入库长度,
                {} as 理论重量, {} as 备注, 商品id FROM products
                JOIN tree ON 商品id=tree.num
                WHERE 单号id='{}' ORDER BY {}"#,
            f_map["原物料号"],
            f_map["规格"],
            f_map["状态"],
            f_map["炉批号"],
            f_map["执行标准"],
            f_map["生产厂家"],
            f_map["库位"],
            f_map["物料号"],
            f_map["入库长度"],
            f_map["理论重量"],
            f_map["备注"],
            data.dh,
            f_map["顺序"]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("原物料号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("执行标准"),
                row.get::<&str, String>("炉号"),
                row.get::<&str, String>("生产厂家"),
                row.get::<&str, String>("库位"),
                row.get::<&str, String>("物料号"),
                row.get::<&str, i32>("入库长度").to_string(),
                format!("{:.1}", row.get::<&str, f64>("理论重量")),
                row.get::<&str, String>("备注"),
                row.get::<&str, String>("商品id"),
            ];

            document_items.push(sp_query(fields))
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取采购入库单据明细
#[post("/fetch_document_items_rk")]
pub async fn fetch_document_items_rk(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "商品规格").await;
        let sql = format!(
            r#"select split_part(node_name,' ', 2) as 名称, split_part(node_name,' ', 1) as 材质,
                {} as 规格, {} as 状态, {} as 炉号, {} as 执行标准, {} as 生产厂家, {} as 物料号, {} as 入库长度,
                {} as 理论重量, {} as 外径壁厚, {} as 库存类别, {} as 备注, 商品id, 入库id, {} as 质检书 FROM products
                JOIN tree ON 商品id=tree.num
                WHERE 单号id='{}' ORDER BY {}"#,
            f_map["规格"],
            f_map["状态"],
            f_map["炉批号"],
            f_map["执行标准"],
            f_map["生产厂家"],
            f_map["物料号"],
            f_map["入库长度"],
            f_map["理论重量"],
            f_map["外径壁厚"],
            f_map["库存类别"],
            f_map["备注"],
            f_map["质检书"],
            data.dh,
            f_map["顺序"]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("炉号"),
                row.get::<&str, String>("执行标准"),
                row.get::<&str, String>("生产厂家"),
                row.get::<&str, String>("物料号"),
                row.get::<&str, i32>("入库长度").to_string(),
                format!("{:.1}", row.get::<&str, f64>("理论重量")),
                row.get::<&str, String>("外径壁厚"),
                row.get::<&str, String>("库存类别"),
                row.get::<&str, String>("备注"),
                row.get::<&str, String>("商品id"),
                row.get::<&str, String>("入库id"),
                row.get::<&str, String>("质检书"),
            ];

            document_items.push(sp_query(fields))
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/make_formal_in")]
pub async fn make_formal_in(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    // let user_name = id.identity().unwrap_or("".to_owned());
    let user = get_user(&db, id, "单据审核".to_owned()).await;
    if user.name != "" {
        let table_name = if data.cate == "采购入库" {
            "入库单据"
        } else {
            "库存调入"
        };

        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, table_name).await;
        let sql = format!(
            r#"update documents set {}='{}' WHERE 单号='{}'"#,
            f_map["审核"], user.name, data.dh
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 实际重量填入销售单
#[post("/make_xs_wight")]
pub async fn make_xs_wight(
    db: web::Data<Pool>,
    dh: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"select 销售id, sum(重量) as 重量 from pout_items where 单号id = '{dh}' group by 销售id;"#
        );

        let rows = conn.query(sql.as_str(), &[]).await.unwrap();
        for row in rows {
            let id: &str = row.get("销售id");
            let weight: f32 = row.get("重量");

            let sql = format!(r#"update sale_items set 重量 = {weight} where id = '{id}'"#);

            let _ = conn.query(sql.as_str(), &[]).await;
        }
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 确认入库完成
#[post("/make_rk_complete")]
pub async fn make_rk_complete(
    db: web::Data<Pool>,
    dh: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"
            with buy_items_count as (
                select count(*) as count 
                from buy_items 
                where 单号id = '{dh}'
            ),
            products_count as (
                select count(distinct 入库id) as count 
                from products 
                where 入库id like '{dh}%' and not_shen_fei(单号id)
            )
            update documents set 布尔字段2 = true 
            where 单号='{dh}' and 
                (select count from buy_items_count) = (select count from products_count)
            "#,
        );

        let _ = conn.query(sql.as_str(), &[]).await;

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/fetch_check_stock")]
pub async fn fetch_check_stock(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();

        let f_map = map_fields(&db, &data.cate).await;
        let sql = format!(
            r#"select {} as 审核 from documents WHERE 单号='{}'"#,
            f_map["审核"], data.dh
        );
        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut check = "".to_owned();
        for row in rows {
            let chk: &str = row.get("审核");
            check = format!("{}", chk);
        }
        HttpResponse::Ok().json(check)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//上传图片
#[post("/pic_in")]
pub async fn pic_in(db: web::Data<Pool>, payload: Multipart, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let path = "./upload/pics/coin.jpg".to_owned();
        let path2 = "./upload/pics/".to_owned();
        save_pic(payload, path.clone()).await.unwrap();
        let path3 = smaller(path.clone(), path2);
        HttpResponse::Ok().json(path3)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 保存图片
async fn save_pics(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
    cate: &str,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let da: Vec<&str> = data.split(SPLITER).collect();
        if da[1] == "/upload/pics/min.jpg" {
            let pic = format!("/upload/pics/pic_{}.jpg", da[0]);
            let min_pic = format!("/upload/pics/min_{}.jpg", da[0]);
            fs::rename("./upload/pics/coin.jpg", format!(".{}", pic)).unwrap();
            fs::rename(
                "./upload/pics/min.jpg",
                format!("./upload/pics/min_{}.jpg", da[0]),
            )
            .unwrap();

            let conn = db.get().await.unwrap();
            let f_map = map_fields(&db, cate).await;
            let sql = format!(
                r#"update documents set {}='{}' WHERE 单号='{}'"#,
                f_map["图片"], pic, da[0]
            );
            let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();
            HttpResponse::Ok().json(min_pic)
        } else {
            HttpResponse::Ok().json(-2)
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//保存图片 - 出库单
#[post("/pic_in_save")]
pub async fn pic_in_save(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    save_pics(db, data, id, "出库单据").await
}

//保存图片 - 销售开票
#[post("/pic_kp_save")]
pub async fn pic_kp_save(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    save_pics(db, data, id, "销售开票").await
}

//保存图片 - 发货单
#[post("/pic_fh_save")]
pub async fn pic_fh_save(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    save_pics(db, data, id, "发货单据").await
}

//上传pdf
#[post("/pdf_in")]
pub async fn pdf_in(db: web::Data<Pool>, payload: Multipart, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let path = "./upload/pdf/lu.pdf".to_owned();
        let result = save_pic(payload, path.clone()).await.unwrap();
        if result == "-3" {
            return HttpResponse::Ok().json(-3);
        }
        HttpResponse::Ok().json(path)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/pdf_in_save")]
pub async fn pdf_in_save(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        // let sql = format!("delete from lu where 炉号 = '{}'", data);
        // let _result = &conn.execute(sql.as_str(), &[]).await.unwrap();
        // let lu_id = Uuid::new_v4();
        let pdf = format!("/upload/pdf/{}.pdf", data);
        fs::rename("./upload/pdf/lu.pdf", format!(".{}", pdf)).unwrap();

        let sql = format!(
            r#"insert into lu (炉号, 质保书) values ('{}', '{}')"#,
            data, pdf
        );
        let _result = &conn.execute(sql.as_str(), &[]).await.unwrap_or(0);
        HttpResponse::Ok().json(data)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
