use crate::service::*;
use actix_identity::Identity;
use actix_web::{get, post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde::Deserialize;
use time::now;

#[derive(Deserialize)]
pub struct StatisData {
    statis_cate: String,
    date1: String,
    date2: String,
}

#[post("/fetch_statis")]
pub async fn fetch_statis(
    db: web::Data<Pool>,
    post_data: web::Json<StatisData>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let mut num: Vec<i64> = Vec::new();
        let mut date_lables: Vec<String> = Vec::new();
        let mut sale_data: Vec<f64> = Vec::new();
        let limits = get_limits(&user).await;

        let da_cate: String;

        let mut date_sql = format!(
            "日期 >= '{}' and 日期 <= '{}' ",
            post_data.date1, post_data.date2
        );

        if post_data.statis_cate == "按月" {
            da_cate = format!("to_char(日期::date, 'YYYY-MM')");
            date_sql = format!(
                "日期 >= '{}' and 日期 <= '{}' ",
                post_data.date1, post_data.date2
            );
        } else if post_data.statis_cate == "按年" {
            da_cate = format!("to_char(日期::date, 'YYYY')");
        } else if post_data.statis_cate == "按日" {
            da_cate = format!("to_char(日期::date, 'YYYY-MM-DD')");
        } else {
            da_cate = format!("to_char(日期::DATE-(extract(dow from 日期::TIMESTAMP)-1||'day')::interval, 'YYYY-mm-dd')");
        }

        let sql = format!(
            r#"
            select {da_cate} as date_cate, sum(单据金额) as 销售额, ROW_NUMBER () OVER (order by {da_cate}) as 序号
            from documents join (select 单号, 应结金额 as 单据金额 from documents where 单号 in 
            (select 文本字段6 from documents where documents.类别='运输发货' and 文本字段10 != '' and 
                {date_sql} {NOT_DEL_SQL}) {NOT_DEL_SQL})
            as t on t.单号 = documents.文本字段6
            where {limits} 类别 = '运输发货' and 文本字段10 != '' and {date_sql} {NOT_DEL_SQL}            
            group by date_cate
            order by date_cate
            "#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        for row in rows {
            let date: String = row.get("date_cate");
            let sale: f64 = row.get("销售额");
            let n: i64 = row.get("序号");
            num.push(n);
            date_lables.push(date);
            sale_data.push(sale);
        }

        let mut date_lables2: Vec<String> = Vec::new();
        let mut sale_data2: Vec<f64> = Vec::new();

        let sql = format!(
            r#"select {da_cate} as date_cate, sum(应结金额) as 销售额
                from documents
                where {limits} 类别 = '销售退货' and 文本字段10 != '' and {date_sql} {NOT_DEL_SQL}
                group by date_cate
                "#,
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        for row in rows {
            let date: String = row.get("date_cate");
            let sale: f64 = row.get("销售额");
            date_lables2.push(date);
            sale_data2.push(sale);
        }

        for i in 0..date_lables2.len() {
            for n in 0..date_lables.len() {
                if date_lables2[i] == date_lables[n] {
                    sale_data[n] = sale_data[n] - sale_data2[i];
                    break;
                }
            }
        }

        HttpResponse::Ok().json((num, date_lables, sale_data))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/fetch_cost")]
pub async fn fetch_cost(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let mut date_lables: Vec<String> = Vec::new();
        let mut sale_data: Vec<String> = Vec::new();

        let date = now().strftime("%Y-%m-%d").unwrap().to_string();

        let sql = format!(
            r#"WITH month_ends AS (
                    WITH RECURSIVE month_series AS (
                        SELECT 
                            date_trunc('month', '${date}'::date) AS month_start,
                            (date_trunc('month', '${date}'::date) + interval '1 month - 1 day')::date AS month_end,
                            0 AS month_num
                        UNION ALL
                        SELECT 
                            date_trunc('month', month_start - interval '1 month'),
                            (date_trunc('month', month_start - interval '1 month') + interval '1 month - 1 day')::date,
                            month_num + 1
                        FROM 
                            month_series
                        WHERE 
                            month_num < 11
                    )
                    SELECT 
                        to_char(month_start, 'YYYY-MM') AS month,
                        to_char(month_end, 'YYYY-MM-DD') AS month_end
                    FROM 
                        month_series
                )
                SELECT 
                    me.month AS 月份,
                        (select COALESCE(sum(库存下限),0) -
                            COALESCE((select sum(理重) from tc_items
                                join documents on 单号id = 单号
                                where documents.日期 <= me.month_end and 
                                    documents.文本字段10 != '' and 作废=false),0) -
                            COALESCE((select sum(理重) from pout_items
                                join documents on 单号id = 单号
                                where documents.日期 <= me.month_end and 
                                    documents.文本字段10 != '' and 作废=false), 0) 
                            as 库存重量 
                        from products
                        JOIN documents on 单号id = 单号
                        where documents.日期 <= me.month_end  
                            and documents.文本字段10 != '' and 作废 = false
                    ) AS 库存重量
                FROM 
                    month_ends me
                ORDER BY 
                    me.month_end;
            "#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        for row in rows {
            let m: &str = row.get("月份");
            let w: f64 = row.get("库存重量");
            date_lables.push(m.to_string());
            sale_data.push(format!("{:.*}", 0, w / 1000.0));
        }

        HttpResponse::Ok().json((date_lables, sale_data))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/home_statis")]
pub async fn home_statis(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        // let today = now().strftime("%Y-%m-%d").unwrap().to_string();
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "销售单据").await;
        let f_map4 = map_fields(&db, "出库单据").await;
        let f_map5 = map_fields(&db, "采购单据").await;
        let limits = get_limits(&user).await;
        let mut limit = limits.clone();

        let mut others = Vec::new();

        // 对库管开放的条件限制
        if user.duty == "库管" {
            limit = format!(r#"documents.文本字段7 = '{}' and"#, user.area);
        }

        //反审单据 ------------------------
        let sql = format!(
            r#"select count(单号) as 数量 from documents
                    where {} 文本字段10 = '' and 布尔字段3 = false and 已记账 = true {}"#,
            limits, NOT_DEL_SQL
        );

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
        let num: i64 = row.get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "反审单据", num));
        }

        //采购退货未完成 ------------------------
        let sql = format!(
            r#"select count(单号) as 数量 from documents
                    where 类别='采购退货' and {} = false and 已记账 = false {}"#,
            f_map5["入库完成"], NOT_DEL_SQL
        );

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
        let num: i64 = row.get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "采购退货未完成", num));
        }

        //销售退货未完成 ------------------------
        let sql = format!(
            r#"select count(单号) as 数量 from documents
                    where 类别='销售退货' and {} = false and 文本字段10 != '' and 已记账 = false {}"#,
            f_map5["入库完成"], NOT_DEL_SQL
        );

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
        let num: i64 = row.get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "销售退货待入库", num));
        }

        //采购未入库 ------------------------
        let sql = format!(
            r#"select count(单号) 数量 from documents
            where documents.类别='材料采购' and documents.{} = false and 
            documents.文本字段10 != '' 
            and 单号 not in (select 文本字段6 from documents where documents.类别='采购入库' and 
            布尔字段3 = true and 文本字段10 = '' {NOT_DEL_SQL}) {NOT_DEL_SQL}
            "#,
            f_map5["入库完成"]
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let num: i64 = rows[0].get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "待入库", num));
        }

        //销售待出库单据 ------------------------
        let sql = format!(
            r#"select count(单号) 数量 from documents            
            where {limit} documents.类别='商品销售' and documents.文本字段10 != '' and
            documents.{} = false and
            单号 not in (select 文本字段6 from documents where documents.类别='销售出库' and 
            布尔字段3 = true and 文本字段10 = '' {NOT_DEL_SQL}) {NOT_DEL_SQL}
            "#,
            f_map["出库完成"]
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let num: i64 = rows[0].get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "待出库", num));
        }

        // 销售未发货
        let sql = format!(
            r#"select count(单号) 数量 from documents
            where {limits} documents.类别='商品销售' and documents.{} = false and documents.文本字段10 != ''
                and 单号 in (select documents.{} from documents where documents.{} <>''
                and documents.类别='销售出库' and documents.{} <> '' and {} = false {NOT_DEL_SQL}) 
                and 单号 not in (select 文本字段6 from documents where documents.类别='运输发货' and 
                布尔字段3 = true and 文本字段10 = '' {NOT_DEL_SQL})  {NOT_DEL_SQL}
            or 单号 in 
                (select 单号 from documents join
                    (select 文本字段6 from documents where documents.类别='运输发货' and
                        布尔字段3 = false and 文本字段10 = '' {NOT_DEL_SQL}) as foo
                on foo.文本字段6 = 单号
                where 类别 = '商品销售' and 布尔字段1 = false {NOT_DEL_SQL})
            "#,
            f_map["发货完成"],
            f_map4["销售单号"],
            f_map4["销售单号"],
            f_map4["审核"],
            f_map4["发货完成"]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let num: i64 = rows[0].get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "待发货", num));
        }

        //销售待开票 ------------------------
        let sql = format!(
            r#"select count(单号) 数量 from documents
            join customers on 客商id = customers.id
            WHERE documents.类别='商品销售' AND documents.{} = true AND documents.{} = true AND
            单号 not in (select 文本字段6 from documents where documents.类别='销售开票' and 
            布尔字段3 = true {NOT_DEL_SQL}) AND 名称 != '天津彩虹石油机械有限公司' AND 
            名称 != '实验室' {NOT_DEL_SQL}"#,
            f_map["是否欠款"], f_map["发货完成"]
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let num: i64 = rows[0].get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "待开票", num));
        }

        //销售未收款 ------------------------
        let sql = format!(
            r#"select count(单号) 数量 from documents
            join customers on 客商id = customers.id
            where {limits} documents.类别='商品销售' and 是否欠款=true and             
            documents.文本字段10 != '' and 名称 != '实验室' {NOT_DEL_SQL}
            "#
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let num: i64 = rows[0].get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "待收款", num));
        }

        // 未提交审核单据 ------------------------
        let sql = format!(
            r#"select count(单号) as 数量 from documents
            where {limits} 布尔字段3 = false and 已记账 = false and 类别 !='采购退货' {NOT_DEL_SQL}"#
        );

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
        let num: i64 = row.get("数量");

        if num > 0 {
            others.push(format!("{}　{}", "未提交审核", num));
        }

        // 已提交待审核单据
        let mut shen = Vec::new();

        let sql = format!(
            r#"select 类别, count(单号) 数量 from documents 
                where {limits} 布尔字段3 = true and 文本字段10 = '' {NOT_DEL_SQL}
                group by 类别
                order by max(开单时间) desc"#
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        for row in rows {
            let cate: &str = row.get("类别");
            let num: i64 = row.get("数量");
            let item = format!("{}　{}", cate, num);
            shen.push(item);
        }

        //反审单据 ------------------------
        let sql = format!(
            r#"select 单号, documents.类别 || '_FS' 类别, 日期, 经办人, 
                    case when documents.类别='运输发货' then documents.文本字段5 else customers.名称 end 名称 
                from documents
                JOIN customers ON documents.客商id=customers.id
                where {} documents.文本字段10 = '' and documents.布尔字段3 = false and 已记账 = true {}
                order by 日期 desc"#,
            limits, NOT_DEL_SQL
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut pre_shen = Vec::new();
        for row in rows {
            let d1: &str = row.get("类别");
            let d2: &str = row.get("单号");
            let d3: &str = row.get("名称");
            let d4: &str = row.get("日期");
            let d5: &str = row.get("经办人");

            let item = format!("{d1}　{d2}　{d3}　{d4}　{d5}");
            pre_shen.push(item);
        }

        // 获取未提交审核单据
        let sql = format!(
            r#"SELECT 单号, documents.类别, documents.日期, 经办人, 
                    case when documents.类别='运输发货' then documents.文本字段5 else customers.名称 end 名称  
                FROM documents 
                JOIN customers ON documents.客商id=customers.id
                WHERE  (documents.布尔字段3 = false and 已记账 = false and documents.类别 != '采购退货') and 作废 = false 
                ORDER BY 开单时间 desc"#
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        for row in rows {
            let d2: &str = row.get("单号");
            let d3: &str = row.get("名称");
            let d4: &str = row.get("日期");
            let d5: &str = row.get("经办人");

            let item = format!("待提交　{d2}　{d3}　{d4}　{d5}");
            pre_shen.push(item);
        }

        if pre_shen.len() < 10 {
            // 待入库单据
            let sql = format!(
                r#"SELECT 单号, documents.类别, documents.日期, 经办人, 
                    case when documents.类别='运输发货' then documents.文本字段5 else customers.名称 end 名称  
                FROM documents 
                JOIN customers ON documents.客商id=customers.id
                WHERE documents.类别 = '材料采购' and documents.布尔字段2 = false and documents.文本字段10 != '' 
                and 单号 not in (select 文本字段6 from documents where documents.类别='采购入库' and 
                布尔字段3 = true and 文本字段10 = '' and 作废=false)
                ORDER BY 开单时间 desc"#
            );

            let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
            for row in rows {
                let d2: &str = row.get("单号");
                let d3: &str = row.get("名称");
                let d4: &str = row.get("日期");
                let d5: &str = row.get("经办人");

                let item = format!("待入库　{d2}　{d3}　{d4}　{d5}");
                pre_shen.push(item);
            }

            // 获取待发货单据
            let sql = format!(
                r#"SELECT 单号, documents.类别, documents.日期, 经办人, 
                    case when documents.类别='运输发货' then documents.文本字段5 else customers.名称 end 名称  
                FROM documents 
                JOIN customers ON documents.客商id=customers.id
                WHERE documents.类别 = '商品销售' and documents.布尔字段1 = false and documents.文本字段10 != ''
                and 单号 in (select documents.文本字段6 from documents where documents.文本字段6 <>''
                and documents.类别='销售出库' and documents.文本字段10 != '' and 布尔字段1 = false and 作废=false) 
                and 单号 not in (select 文本字段6 from documents where documents.类别='运输发货' and 
                布尔字段3 = true and 文本字段10 = '' and 作废=false) and 作废=false 
                or 单号 in 
                (select 单号 from documents join
                    (select 文本字段6 from documents where documents.类别='运输发货' and
                        布尔字段3 = false and 文本字段10 = '' and 作废=false) as foo
                    on foo.文本字段6 = 单号
                where 类别 = '商品销售' and 布尔字段1 = false and 作废=false) 
                ORDER BY 开单时间 desc"#
            );

            let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
            for row in rows {
                let d2: &str = row.get("单号");
                let d3: &str = row.get("名称");
                let d4: &str = row.get("日期");
                let d5: &str = row.get("经办人");

                let item = format!("待发货　{d2}　{d3}　{d4}　{d5}");
                pre_shen.push(item);
            }
        }

        // 获取已提审核单据
        let sql = format!(
            r#"select documents.类别, 单号, 日期, 经办人,
                    case when documents.类别='运输发货' then documents.文本字段5 else c.名称 end 名称
                from documents
                join customers c on documents.客商id=c.id
                where {limits} documents.布尔字段3 = true and documents.文本字段10 = '' {NOT_DEL_SQL}
                order by 开单时间 desc
                "#
        );

        // println!("sql: {}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut wait_shen = Vec::new();
        for row in rows {
            let d1: &str = row.get("类别");
            let d2: &str = row.get("单号");
            let d3: &str = row.get("名称");
            let d4: &str = row.get("日期");
            let d5: &str = row.get("经办人");

            let item = format!("{d1}　{d2}　{d3}　{d4}　{d5}");
            wait_shen.push(item);
        }

        HttpResponse::Ok().json((others, shen, pre_shen, wait_shen))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/get_waitshen/{time}")]
pub async fn get_waitshen(db: web::Data<Pool>, time: web::Path<String>) -> String {
    let conn = db.get().await.unwrap();

    let sql = format!(
        r#"select count(单号) 数量 from documents where 布尔字段3 = true and 文本字段10 = '' and 提交时间 > '{}' {}"#,
        *time, NOT_DEL_SQL
    );

    let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
    let mut num = 0i64;
    for row in rows {
        num = row.get("数量");
    }

    return num.to_string();
}

//入库明细
#[post("/get_stockin_items")]
pub async fn get_stockin_items(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "入库明细".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let cate = post_data.cate.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let data: Vec<&str> = cate.split(SPLITER).collect();

        let f_map = map_fields(&db, "入库单据").await;
        let f_map2 = map_fields(&db, "商品规格").await;
        // let limits = get_limits(user, f_map).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(products.{}) LIKE '%{name}%'
                    OR LOWER(node_name) LIKE '%{name}%'  OR LOWER(规格型号) LIKE '%{name}%'
                    OR LOWER(products.{}) LIKE '%{name}%' OR LOWER(products.{}) LIKE '%{name}%'
                    OR LOWER(products.{}) LIKE '%{name}%' OR LOWER(documents.{}) LIKE '%{name}%'
                    OR LOWER(documents.{}) LIKE '%{name}%' OR LOWER(documents.备注) LIKE '%{name}%')"#,
                f_map2["物料号"],
                f_map2["状态"],
                f_map2["执行标准"],
                f_map2["生产厂家"],
                f_map["到货日期"],
                f_map["入库日期"],
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select documents.{} 到货日期, documents.{} 入库日期, 单号, split_part(node_name,' ',2) 名称,
                products.{} 物料号, split_part(node_name,' ',1) as 材质, 规格型号, products.{} 状态,
                products.{} 炉批号, products.{} 入库长度, products.{} 执行标准, products.{} 生产厂家,
                products.{} 理论重量, products.备注, ROW_NUMBER () OVER (ORDER BY {sort}) as 序号
            from products
            join documents on products.单号id = documents.单号
            join tree on tree.num = products.商品id
            where {query_date}{query_field} and products.整数字段1 != 0 {NOT_DEL_SQL}
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#,
            f_map["到货日期"],
            f_map["入库日期"],
            f_map2["物料号"],
            f_map2["状态"],
            f_map2["炉批号"],
            f_map2["入库长度"],
            f_map2["执行标准"],
            f_map2["生产厂家"],
            f_map2["理论重量"],
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("到货日期"),
                row.get::<&str, String>("入库日期"),
                row.get::<&str, String>("单号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("物料号"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格型号"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("炉批号"),
                row.get::<&str, i32>("入库长度").to_string(),
                row.get::<&str, String>("执行标准"),
                row.get::<&str, String>("生产厂家"),
                format!("{:.1}", row.get::<&str, f64>("理论重量")),
                row.get::<&str, String>("备注"),
            ];

            products.push(sp_query(fields));
        }

        let count_sql = format!(
            r#"select count(products.物料号) as 记录数, sum(products.库存下限) as 理论重量
            from products
            join documents on products.单号id = documents.单号
            join tree on tree.num = products.商品id
            where {query_date}{query_field} and documents.文本字段10 != '' {NOT_DEL_SQL}"#
        );

        let row = &conn.query_one(count_sql.as_str(), &[]).await.unwrap();
        let count: i64 = row.get("记录数");
        let sum1: f64 = row.get("理论重量");
        let pages = (count as f64 / rec as f64).ceil() as i32;

        HttpResponse::Ok().json((products, count, pages, sum1))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//出库明细
#[post("/get_stockout_items")]
pub async fn get_stockout_items(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "出库明细".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let cate = post_data.cate.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let data: Vec<&str> = cate.split(SPLITER).collect();
        let limit = get_limits(&user).await;

        let f_map = map_fields(&db, "出库单据").await;
        let f_map2 = map_fields(&db, "商品规格").await;
        // let limits = get_limits(user, f_map).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(di.物料号) LIKE '%{name}%' 
                OR LOWER(node_name) LIKE '%{name}%' OR LOWER(规格型号) LIKE '%{name}%' 
                OR LOWER(products.{}) LIKE '%{name}%' OR LOWER(documents.日期) LIKE '%{name}%' 
                OR LOWER(documents.{}) LIKE '%{name}%' OR LOWER(documents.{}) LIKE '%{name}%' 
                OR LOWER(documents.备注) LIKE '%{name}%')"#,
                f_map2["状态"], f_map["合同编号"], f_map["客户"],
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select documents.日期, documents.{} 公司名称, documents.{} 合同号, 单号, documents.{} 销售单号,
                 split_part(node_name,' ',2) as 名称, di.物料号, split_part(node_name,' ',1) as 材质,
                 规格型号, products.{} 状态, products.{} 炉批号, di.长度, pi.数量, pi.重量,
                 pi.备注, ROW_NUMBER () OVER (ORDER BY {sort}) as 序号
            from pout_items pi
            join documents on pi.单号id = documents.单号
            join sale_items di on di.id = pi.销售id
            join products on di.物料号 = products.物料号
            join customers on documents.客商id = customers.id
            join tree on tree.num = products.商品id
            where {limit} {query_date} {query_field} and documents.文本字段10 != '' {NOT_DEL_SQL} 
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#,
            f_map["客户"], f_map["合同编号"], f_map["销售单号"], f_map2["状态"], f_map2["炉批号"]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("日期"),
                row.get::<&str, String>("公司名称"),
                row.get::<&str, String>("合同号"),
                row.get::<&str, String>("单号"),
                row.get::<&str, String>("销售单号"),
                row.get::<&str, String>("物料号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格型号"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("炉批号"),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                row.get::<&str, f32>("重量").to_string(),
                row.get::<&str, String>("备注"),
            ];

            products.push(sp_query(fields));
        }

        let count_sql = format!(
            r#"select count(di.物料号) as 记录数, sum(di.长度) as 长度合计, sum(pout_items.重量) as 重量合计
            from pout_items
            join documents on pout_items.单号id = documents.单号
            join sale_items di on di.id = pout_items.销售id
            join products on di.物料号 = products.物料号
            join customers on documents.客商id = customers.id
            join tree on tree.num = products.商品id
            where {limit} {query_date}{query_field} and documents.文本字段10 != '' {NOT_DEL_SQL}"#
        );

        let row = &conn.query_one(count_sql.as_str(), &[]).await.unwrap();
        let count: i64 = row.get("记录数");
        let sum1: i64 = row.get("长度合计");
        let sum2: f32 = row.get("重量合计");
        let pages = (count as f64 / rec as f64).ceil() as i32;

        HttpResponse::Ok().json((products, count, pages, sum1, sum2))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//发货明细
#[post("/get_trans_items")]
pub async fn get_trans_items(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "出库明细".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let cate = post_data.cate.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let data: Vec<&str> = cate.split(SPLITER).collect();
        // let limit = get_limits(&user).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(r#" AND LOWER(d.文本字段5) LIKE '%{}%'"#, name,)
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"
            select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号,* from 
            (
                select d.日期, d.文本字段5 客户名称, d.文本字段3 合同号, di.单号id 发货单号, d.文本字段6 销售单号, 
                    split_part(node_name,' ',2) 名称, split_part(node_name,' ',1) 材质, 规格型号 规格, p.文本字段2 状态,
                    p.文本字段4 炉批号, 长度, pi.重量, di.备注, pi.顺序, 单价, pi.数量,  
                    case when si.类型='按重量' then 单价*pi.重量 else 单价*pi.数量 end 金额
                from fh_items di
                join pout_items pi on di.出库id = pi.id
                join sale_items si on pi.销售id = si.id
                join documents d on d.单号 = di.单号id
                join products p on p.物料号 = si.物料号
                join tree t on t.num = p.商品id
                where {query_date} {query_field} and d.类别 = '运输发货' and d.文本字段10 != '' {NOT_DEL_SQL}
                union all 
                    select foo.*,单价,数量,金额 from sale_items si 
                    join documents d on d.单号 = si.单号id
                    join 
                        (select distinct on (di.单号id) d.日期, d.文本字段5 客户名称, d.文本字段3 合同号, di.单号id 发货单号, d.文本字段6 销售单号, 
                                '锯口费' 名称, '--' 材质, '--' 规格,  '--' 状态, '--' 炉批号, 0 as 长度, 0::real 重量, di.备注, 99 as 顺序
                            from fh_items di
                            join documents d on d.单号 = di.单号id
                            where {query_date} {query_field} and 出库id = '锯口费' and d.类别 = '运输发货' and d.文本字段10 != ''  and 作废 = false                             
                        ) foo on 单号id = foo.销售单号
                    where 物料号 = '锯口费' and d.文本字段10 != ''  and 作废 = false
            )
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("日期"),
                row.get::<&str, String>("客户名称"),
                row.get::<&str, String>("合同号"),
                row.get::<&str, String>("发货单号"),
                row.get::<&str, String>("销售单号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("炉批号"),
                row.get::<&str, f32>("单价").to_string(),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                row.get::<&str, f32>("重量").to_string(),
                format!("{:.2}", row.get::<&str, f64>("金额")),
                row.get::<&str, String>("备注"),
            ];

            products.push(sp_query(fields));
        }

        let count_sql = format!(
            r#"
            select count(*) 记录数, COALESCE(sum(长度), 0) 长度合计, COALESCE(sum(重量), 0) 重量合计, COALESCE(sum(金额), 0) 金额合计 from
            (select 长度, pi.重量, case when si.类型='按重量' then 单价*pi.重量 else 单价*pi.数量 end 金额
            from fh_items di
            join pout_items pi on di.出库id = pi.id
            join sale_items si on pi.销售id = si.id
            join documents d on d.单号 = di.单号id
            join products p on p.物料号 = si.物料号
            join tree t on t.num = p.商品id
            where {query_date} {query_field} and d.类别 = '运输发货' and d.文本字段10 != '' {NOT_DEL_SQL}
            union all 
            select 0 长度, 0 重量, 金额
            from fh_items di
            join documents d on d.单号 = di.单号id
            join sale_items si on d.文本字段6 = si.单号id
            where {query_date} {query_field} and 出库id = '锯口费' and si.物料号 = '锯口费' and d.类别 = '运输发货' and d.文本字段10 != '' {NOT_DEL_SQL}) 
            "#
        );

        // println!("{}", count_sql);

        let row = &conn.query_one(count_sql.as_str(), &[]).await.unwrap();
        let count: i64 = row.get("记录数");
        let sum1: i64 = row.get("长度合计");
        let sum2: f32 = row.get("重量合计");
        let sum3: f64 = row.get("金额合计");
        let pages = (count as f64 / rec as f64).ceil() as i32;

        HttpResponse::Ok().json((products, count, pages, sum1, sum2, sum3))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//销售明细
#[post("/get_sale_items")]
pub async fn get_sale_items(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "出库明细".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let cate = post_data.cate.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let data: Vec<&str> = cate.split(SPLITER).collect();
        // let limit = get_limits(&user).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(r#" AND LOWER(c.名称) LIKE '%{}%'"#, name,)
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"d.日期>='{}' AND d.日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        // 对于多次发货有问题
        let sql = format!(
            r#"select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号, d.日期 日期, 名称 客户名称, d.文本字段6 合同号, d.文本字段8 客户PO, di.单号id 单号, 
                split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质, p.规格型号 规格, p.文本字段2 状态, p.文本字段3 执行标准, 
                di.类型, 单价, 长度, 数量, 理重, 重量, 金额::numeric(10,2)::text 金额, di.物料号, COALESCE(foo.发货日期, '未发货') 发货日期, di.备注
            FROM sale_items di
            join documents d on d.单号 =  di.单号id            
            join customers c on c.id = d.客商id
            JOIN products p ON p.物料号 = di.物料号
            JOIN tree ON p.商品id = tree.num
            left join (
                select distinct 文本字段6 销售单号, 日期 发货日期 from documents where 类别='运输发货' AND 文本字段10 != '' {NOT_DEL_SQL}
                ) foo on foo.销售单号 = d.单号
            where {query_date} {query_field} {NOT_DEL_SQL}
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // // 一种更合理的查询，锯口费还需完善
        // let sql = format!(
        //     r#"select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号, d.日期 日期, 名称 客户名称, d.文本字段6 合同号, d.文本字段8 客户PO, di.单号id 单号,
        //         split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质, p.规格型号 规格, p.文本字段2 状态, p.文本字段3 执行标准,
        //         di.类型, di.单价, di.长度, di.数量, di.理重, di.重量, 金额::numeric(10,2)::text 金额, di.物料号, COALESCE(dd.日期, '未发货') 发货日期, di.备注
        //         from sale_items di
        //         left join pout_items pi on di.id=pi.销售id
        //         left join fh_items fi on fi.出库id = pi.id
        //         left join documents d on d.单号= di.单号id
        //         left join documents dd on dd.单号= fi.单号id
        //         left join customers c on c.id = d.客商id
        //         left JOIN products p ON p.物料号 = di.物料号
        //         left JOIN tree ON p.商品id = tree.num
        //         where {query_date} {query_field} and d.文本字段10 != '' and d.作废 = false
        //         ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        // );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("日期"),
                row.get::<&str, String>("客户名称"),
                row.get::<&str, String>("合同号"),
                row.get::<&str, String>("客户PO"),
                row.get::<&str, String>("单号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("执行标准"),
                row.get::<&str, String>("类型"),
                row.get::<&str, f32>("单价").to_string(),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                row.get::<&str, f32>("理重").to_string(),
                row.get::<&str, f32>("重量").to_string(),
                row.get::<&str, String>("金额"),
                row.get::<&str, String>("物料号"),
                row.get::<&str, String>("发货日期"),
                row.get::<&str, String>("备注"),
            ];

            products.push(sp_query(fields));
        }

        let count_sql = format!(
            r#"select count(*) as 记录数, sum(重量) as 重量合计, sum(金额) as 金额合计
            FROM sale_items di
            join documents d on d.单号 =  di.单号id
            join customers c on c.id = d.客商id
            JOIN products p ON p.物料号 = di.物料号
            JOIN tree ON p.商品id = tree.num
            left join (
                select distinct 文本字段6 销售单号, 日期 发货日期 from documents where 类别='运输发货' AND 文本字段10 != '' {NOT_DEL_SQL}
            ) foo on foo.销售单号 = d.单号 
            where {query_date} {query_field} {NOT_DEL_SQL}"#
        );

        // println!("{}", count_sql);

        let row = &conn.query_one(count_sql.as_str(), &[]).await.unwrap();
        let count: i64 = row.get("记录数");
        let sum1: f32 = row.get("重量合计");
        let sum2: f32 = row.get("金额合计");
        let pages = (count as f64 / rec as f64).ceil() as i32;
        HttpResponse::Ok().json((products, count, pages, sum1, sum2))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//入库明细导出到 excel
#[post("/stockin_excel")]
pub async fn stockin_excel(
    db: web::Data<Pool>,
    post_data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "入库明细".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let data: Vec<&str> = post_data.split(SPLITER).collect();
        let name = data[2].trim().to_lowercase();

        let f_map = map_fields(&db, "入库单据").await;
        let f_map2 = map_fields(&db, "商品规格").await;
        // let limits = get_limits(user, f_map).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(products.{}) LIKE '%{name}%' 
                OR LOWER(node_name) LIKE '%{name}%' OR LOWER(规格型号) LIKE '%{name}%' 
                OR LOWER(products.{}) LIKE '%{name}%' OR LOWER(products.{}) LIKE '%{name}%'
                OR LOWER(products.{}) LIKE '%{name}%' OR LOWER(products.{}) LIKE '%{name}%'
                OR LOWER(documents.{}) LIKE '%{name}%' OR LOWER(documents.{}) LIKE '%{name}%' 
                OR LOWER(documents.备注) LIKE '%{name}%')"#,
                f_map2["物料号"],
                f_map2["状态"],
                f_map2["执行标准"],
                f_map2["生产厂家"],
                f_map2["炉批号"],
                f_map["到货日期"],
                f_map["入库日期"],
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select documents.{} 到货日期, documents.{} 入库日期, 单号, split_part(node_name,' ',2) as 名称, products.{} 物料号,
                 split_part(node_name,' ',1) as 材质, 规格型号 规格, products.{} 状态, products.{} 炉批号, products.{}::text 入库长度,
                 products.{} 执行标准, products.{} 生产厂家, products.{}::text 理论重量, products.备注,
                 ROW_NUMBER () OVER (ORDER BY documents.日期 DESC)::text as 序号 from products
            join documents on products.单号id = documents.单号
            join tree on tree.num = products.商品id
            where {query_date}{query_field} and documents.文本字段10 != '' {NOT_DEL_SQL}
            ORDER BY documents.日期 DESC"#,
            f_map["到货日期"],
            f_map["入库日期"],
            f_map2["物料号"],
            f_map2["状态"],
            f_map2["炉批号"],
            f_map2["入库长度"],
            f_map2["执行标准"],
            f_map2["生产厂家"],
            f_map2["理论重量"],
        );
        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let f_str = r#"[
            {"name": "序号", "width": 6},
            {"name": "到货日期", "width": 12},
            {"name": "入库日期", "width": 12},
            {"name": "单号", "width": 15},
            {"name": "名称", "width": 12},
            {"name": "物料号", "width": 12},
            {"name": "材质", "width": 12},
            {"name": "规格", "width": 12},
            {"name": "状态", "width": 18},
            {"name": "炉批号", "width": 18},
            {"name": "执行标准", "width": 18},
            {"name": "生产厂家", "width": 15},
            {"name": "入库长度", "width": 10},
            {"name": "理论重量", "width": 12},
            {"name": "备注", "width": 15}
        ]"#;

        let fields = serde_json::from_str(f_str).unwrap();

        out_excel("入库明细表", fields, rows.as_ref());

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//出库明细导出到 excel
#[post("/stockout_excel")]
pub async fn stockout_excel(
    db: web::Data<Pool>,
    post_data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "出库明细".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let data: Vec<&str> = post_data.split(SPLITER).collect();
        let name = data[2].trim().to_lowercase();

        let f_map = map_fields(&db, "出库单据").await;
        let f_map2 = map_fields(&db, "商品规格").await;

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(di.物料号) LIKE '%{name}%' 
                OR LOWER(node_name) LIKE '%{name}%' OR LOWER(规格型号) LIKE '%{name}%' 
                OR LOWER(products.{}) LIKE '%{name}%' OR LOWER(documents.日期) LIKE '%{name}%' 
                OR LOWER(documents.{}) LIKE '%{name}%' OR LOWER(documents.{}) LIKE '%{name}%' 
                OR LOWER(documents.备注) LIKE '%{name}%')"#,
                f_map2["状态"], f_map["合同编号"], f_map["客户"],
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select documents.日期, documents.{} 公司名称, documents.{} 合同号, 单号,
                split_part(node_name,' ',2) as 名称, di.物料号, split_part(node_name,' ',1) as 材质,
                规格型号 规格, products.{} 状态, products.{} 炉批号, di.长度::text, pout_items.数量::text,
                pout_items.重量::text, pout_items.备注,
                ROW_NUMBER () OVER (ORDER BY documents.日期 DESC)::text as 序号
            from pout_items
            join sale_items di on di.id = pout_items.销售id
            join documents on pout_items.单号id = documents.单号
            join products on di.物料号 = products.物料号
            join customers on documents.客商id = customers.id
            join tree on tree.num = products.商品id
            where {query_date}{query_field} and documents.文本字段10 != '' {NOT_DEL_SQL} 
            order by documents.日期 DESC"#,
            f_map["客户"], f_map["合同编号"], f_map2["状态"], f_map2["炉批号"],
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let f_str = r#"[
            {"name": "序号", "width": 6},
            {"name": "日期", "width": 12},
            {"name": "公司名称", "width": 25},
            {"name": "合同号", "width": 15},
            {"name": "单号", "width": 15},
            {"name": "物料号", "width": 12},
            {"name": "名称", "width": 12},
            {"name": "材质", "width": 12},
            {"name": "规格", "width": 12},
            {"name": "状态", "width": 18},
            {"name": "炉批号", "width": 15},
            {"name": "长度", "width": 10},
            {"name": "数量", "width": 10},
            {"name": "重量", "width": 10},
            {"name": "备注", "width": 15}
        ]"#;

        let fields = serde_json::from_str(f_str).unwrap();
        out_excel("出库明细表", fields, rows.as_ref());

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//发货明细导出到 excel
#[post("/trans_item_excel")]
pub async fn trans_item_excel(
    db: web::Data<Pool>,
    post_data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let data: Vec<&str> = post_data.split(SPLITER).collect();
        let name = data[2].trim().to_lowercase();

        let query_field = if name != "" {
            //注意前导空格
            format!(r#" AND LOWER(d.文本字段5) LIKE '%{}%'"#, name,)
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"
            select ROW_NUMBER () OVER (ORDER BY 发货日期 desc, 发货单号 desc, 顺序)::text as 序号,* from 
            (select d.日期::text 发货日期, d.文本字段5 客户名称, d.文本字段3 合同号, di.单号id 发货单号, d.文本字段6 销售单号, 
                split_part(node_name,' ',2) 名称, split_part(node_name,' ',1) 材质, 规格型号 规格, p.文本字段2 状态,
                p.文本字段4 炉批号,  长度::text 长度, pi.重量::text 重量, di.备注, pi.顺序, 单价::text 单价, pi.数量::text 数量, 
                case when si.类型='按重量' then round((单价*pi.重量)::numeric,2)::text else round((单价*pi.数量)::numeric,2)::text end 金额                 
            from fh_items di
            join pout_items pi on di.出库id = pi.id
            join sale_items si on pi.销售id = si.id
            join documents d on d.单号 = di.单号id
            join products p on p.物料号 = si.物料号
            join tree t on t.num = p.商品id
            where {query_date} {query_field} and d.类别 = '运输发货' and d.文本字段10 != '' {NOT_DEL_SQL}
            union all 
                select foo.*,单价::text 单价, 数量::text 数量, 金额::text 金额 from sale_items si 
                join documents d on d.单号 = si.单号id
                join 
                    (select distinct on (di.单号id) d.日期, d.文本字段5 客户名称, d.文本字段3 合同号, di.单号id 发货单号, d.文本字段6 销售单号, 
                            '锯口费' 名称, '--' 材质, '--' 规格,  '--' 状态, '--' 炉批号, '0' as 长度, '0' 重量, di.备注, 99 as 顺序
                        from fh_items di
                        join documents d on d.单号 = di.单号id
                        where {query_date} {query_field} and 出库id = '锯口费' and d.类别 = '运输发货' and d.文本字段10 != ''  and 作废 = false                             
                    ) foo on 单号id = foo.销售单号
                where 物料号 = '锯口费' and d.文本字段10 != ''  and 作废 = false)                    
            "#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let f_str = r#"[
            {"name": "序号", "width": 6},
            {"name": "发货日期", "width": 12},
            {"name": "客户名称", "width": 25},
            {"name": "合同号", "width": 15},
            {"name": "销售单号", "width": 15},
            {"name": "发货单号", "width": 15},   
            {"name": "名称", "width": 12},
            {"name": "材质", "width": 12},
            {"name": "规格", "width": 12},
            {"name": "状态", "width": 18},
            {"name": "炉批号", "width": 15},
            {"name": "单价", "width": 10},
            {"name": "长度", "width": 10},
            {"name": "数量", "width": 10},
            {"name": "重量", "width": 10},
            {"name": "金额", "width": 15},
            {"name": "备注", "width": 15}
        ]"#;

        let fields: Vec<Fields> = serde_json::from_str(f_str).unwrap();

        out_excel("发货明细表", fields, rows.as_ref());

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//销售明细导出到 excel
#[post("/sale_item_excel")]
pub async fn sale_item_excel(
    db: web::Data<Pool>,
    post_data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let data: Vec<&str> = post_data.split(SPLITER).collect();
        let name = data[2].trim().to_lowercase();

        let query_field = if name != "" {
            //注意前导空格
            format!(r#" AND LOWER(c.名称) LIKE '%{}%'"#, name,)
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#"日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select ROW_NUMBER () OVER (ORDER BY d.日期 DESC, di.单号id DESC, di.顺序)::text as 序号, d.日期 日期, 名称 客户名称, d.文本字段6 合同号, d.文本字段8 客户PO, di.单号id 销售单号, 
                split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质, p.规格型号 规格, p.文本字段2 状态, p.文本字段3 执行标准, 
                di.类型, 单价::text, 长度::text, 数量::text, 理重::text 理重, 重量::text 实重, 金额::numeric(10,2)::text 金额, di.物料号, COALESCE(foo.发货日期, '未发货') 发货日期, di.备注
            FROM sale_items di
            join documents d on d.单号 =  di.单号id            
            join customers c on c.id = d.客商id
            JOIN products p ON p.物料号 = di.物料号
            JOIN tree ON p.商品id = tree.num
            left join (
                select distinct 文本字段6 销售单号, 日期 发货日期 from documents where 类别='运输发货' AND 文本字段10 != '' {NOT_DEL_SQL}
            ) foo on foo.销售单号 = d.单号 
            where {query_date} {query_field} {NOT_DEL_SQL}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let f_str = r#"[
            {"name": "序号", "width": 6},
            {"name": "日期", "width": 15},
            {"name": "客户名称", "width": 25},
            {"name": "合同号", "width": 15},
            {"name": "客户PO", "width": 15},
            {"name": "销售单号", "width": 15},
            {"name": "名称", "width": 12},
            {"name": "材质", "width": 12},
            {"name": "规格", "width": 12},
            {"name": "状态", "width": 15},
            {"name": "执行标准", "width": 18},
            {"name": "单价", "width": 10},
            {"name": "长度", "width": 10},
            {"name": "数量", "width": 10},
            {"name": "理重", "width": 10},
            {"name": "实重", "width": 10},
            {"name": "金额", "width": 15},
            {"name": "物料号", "width": 15},
            {"name": "发货日期", "width": 15},
            {"name": "备注", "width": 15}
        ]"#;

        let fields: Vec<Fields> = serde_json::from_str(f_str).unwrap();

        out_excel("销售明细表", fields, rows.as_ref());

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///业务往来
#[post("/fetch_business")]
pub async fn fetch_business(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "业务往来".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.trim().to_lowercase();
        let cate = post_data.cate.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let data: Vec<&str> = cate.split(SPLITER).collect();

        let limits = get_limits(&user).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(documents.类别) LIKE '%{name}%' OR
                LOWER(node_name) LIKE '%{name}%' OR LOWER(documents.文本字段6) LIKE '%{name}%' OR
                LOWER(规格型号) LIKE '%{name}%' OR LOWER(products.文本字段2) LIKE '%{name}%' OR
                LOWER(customers.名称) LIKE '%{name}%' OR LOWER(documents.备注) LIKE '%{name}%')"#
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#" AND 日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select 日期, 单号, customers.名称 客户名称, documents.文本字段6 as 合同编号, documents.类别,
                应结金额, split_part(node_name,' ',2) as 名称, split_part(node_name,' ',1) as 材质,
                规格型号 规格, products.文本字段2 状态, 长度, 数量, 单价, 重量, documents.备注,
                ROW_NUMBER () OVER (ORDER BY {sort}) 序号
            from sale_items
            join documents on documents.单号 = sale_items.单号id
            join customers on documents.客商id = customers.id
            join products on sale_items.物料号 = products.物料号
            join tree on tree.num = products.商品id
            where {limits} documents.文本字段10 != '' and 
                customers.类别='客户' {query_field}{query_date} {NOT_DEL_SQL}
            ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, i64>("序号").to_string(),
                row.get::<&str, String>("日期"),
                row.get::<&str, String>("单号"),
                row.get::<&str, String>("客户名称"),
                row.get::<&str, String>("合同编号"),
                row.get::<&str, String>("类别"),
                format!("{:.2}", row.get::<&str, f64>("应结金额")),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                row.get::<&str, f32>("单价").to_string(),
                row.get::<&str, f32>("重量").to_string(),
                row.get::<&str, String>("备注"),
            ];

            products.push(sp_query(fields));
        }

        let count_sql = format!(
            r#"select count(单号) as 记录数, COALESCE(sum(金额), 0) as 金额
            from sale_items
            join documents on documents.单号 = sale_items.单号id
            join customers on documents.客商id = customers.id
            join products on sale_items.物料号 = products.物料号
            join tree on tree.num = products.商品id
            where {limits} documents.文本字段10 != '' and 
                customers.类别='客户' {query_field}{query_date} {NOT_DEL_SQL}"#
        );

        let rows = &conn.query(count_sql.as_str(), &[]).await.unwrap();

        let mut count: i64 = 0;
        let mut money: f32 = 0f32;

        for row in rows {
            count = row.get("记录数");
            money = row.get("金额");
        }
        let pages = (count as f64 / post_data.rec as f64).ceil() as i32;

        let money2 = format!("{:.*}", 0, money);

        HttpResponse::Ok().json((products, count, pages, money2))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//业务往来导出到 excel
#[post("/business_excel")]
pub async fn business_excel(
    db: web::Data<Pool>,
    post_data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "业务往来".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let data: Vec<&str> = post_data.split(SPLITER).collect();
        let name = data[2].trim().to_lowercase();

        let limits = get_limits(&user).await;

        let query_field = if name != "" {
            //注意前导空格
            format!(
                r#" AND (LOWER(单号) LIKE '%{name}%' OR LOWER(documents.类别) LIKE '%{name}%' OR 
                LOWER(node_name) LIKE '%{name}%' OR LOWER(documents.文本字段6) LIKE '%{name}%' OR 
                LOWER(规格) LIKE '%{name}%' OR LOWER(状态) LIKE '%{name}%' OR
                LOWER(customers.名称) LIKE '%{name}%' OR LOWER(documents.备注) LIKE '%{name}%')"#
            )
        } else {
            "".to_owned()
        };

        let query_date = if data[0] != "" && data[1] != "" {
            format!(r#" AND 日期>='{}' AND 日期 <='{}'"#, data[0], data[1])
        } else {
            "".to_owned()
        };

        let sql = format!(
            r#"select 日期, 单号, customers.名称 客户名称, documents.文本字段6 合同编号, documents.类别,
                 应结金额::text, split_part(node_name,' ',2) 名称, split_part(node_name,' ',1) 材质,
                 规格型号 规格, products.文本字段2 状态, 长度::text, 数量::text, 单价::text, 重量::text,
                 documents.备注, ROW_NUMBER () OVER (ORDER BY documents.日期 DESC)::text 序号
            from sale_items
            join documents on documents.单号 = sale_items.单号id
            join customers on documents.客商id = customers.id
            join products on sale_items.物料号 = products.物料号
            join tree on tree.num = products.商品id
            where {limits} documents.文本字段10 != '' and 
                customers.类别='客户' {query_field}{query_date} {NOT_DEL_SQL}
            ORDER BY documents.日期 DESC"#
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let sss = r#"[
            {"name": "序号", "width": 6},
            {"name": "日期", "width": 12},
            {"name": "单号", "width": 15},
            {"name": "客户名称", "width": 25},
            {"name": "合同编号", "width": 15},
            {"name": "类别", "width": 12},
            {"name": "应结金额", "width": 12},
            {"name": "名称", "width": 12},
            {"name": "材质", "width": 12},
            {"name": "规格", "width": 12},
            {"name": "状态", "width": 18},
            {"name": "长度", "width": 10},
            {"name": "数量", "width": 10},
            {"name": "单价", "width": 10},
            {"name": "重量", "width": 10},
            {"name": "备注", "width": 15}
        ]"#;

        let fields: Vec<Fields> = serde_json::from_str(sss).unwrap();

        out_excel("业务往来明细表", fields, rows.as_ref());

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
