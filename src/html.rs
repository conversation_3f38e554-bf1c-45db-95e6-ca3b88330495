use crate::service::{get_user, r2s, Search, UserData};
use actix_identity::Identity;
use actix_web::http::header::ContentType;
use actix_web::{get, web, web::Path, HttpRequest, HttpResponse};
use deadpool_postgres::Pool;
use crate::templates::statics::StaticFile;
use crate::templates::*; // 模板已在 lib.rs 中统一导出

pub async fn static_file(path: Path<String>) -> HttpResponse {
    let name = &path.into_inner();
    if let Some(data) = StaticFile::get(name) {
        HttpResponse::Ok()
            .insert_header(ContentType(data.mime.clone()))
            .body(data.content)
    } else {
        HttpResponse::NotFound()
            .reason("No such static file.")
            .finish()
    }
}

fn goto_login() -> HttpResponse {
    HttpResponse::Found()
        .append_header(("location", format!("/{}", "login")))
        .finish()
}

fn name_show(user: &UserData) -> String {
    if user.duty != "总经理" {
        format!("｜{}区 {}｜ 　{}", user.area, user.duty, user.name)
    } else {
        format!("{} 　{}", user.duty, user.name)
    }
}

///主页
#[get("/")]
pub async fn index(_req: HttpRequest, db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let name = name_show(&user);
        let html = r2s(|o| home_html(o, name));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}


///商品设置
#[get("/product_set")]
pub async fn product_set(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "库存状态".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| productset_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///字段设置
#[get("/field_set")]
pub async fn field_set(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| fieldset_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///客户管理
#[get("/customer_manage")]
pub async fn customer_manage(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "客户管理".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| customer_html(o, user, "客户"));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///供应商管理
#[get("/supplier_manage")]
pub async fn supplier_manage(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "供应商管理".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| customer_html(o, user, "供应商"));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///库存调整-入库
#[get("/stock_change_in/{dh}")]
pub async fn stock_change_in(
    db: web::Data<Pool>,
    dh_num: web::Path<String>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "调整库存".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" {
            "新单据"
        } else {
            &*dh_num
        };
        let setup = vec!["调整入库", "供应商", "近期调整", dh];
        user.show = name_show(&user);
        let html = r2s(|o| stockin_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///库存调整-出库
#[get("/stock_change_out/{dh}")]
pub async fn stock_change_out(
    db: web::Data<Pool>,
    dh_num: web::Path<String>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "调整库存".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" {
            "新单据"
        } else {
            &*dh_num
        };
        let setup = vec!["调整出库", "供应商", "近期调整", dh];
        user.show = name_show(&user);
        let html = r2s(|o| stockout_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 采购入库
#[get("/material_in/{dh}")]
pub async fn material_in(
    db: web::Data<Pool>,
    dh_num: web::Path<String>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "采购入库".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" {
            "新单据"
        } else {
            &*dh_num
        };
        let setup = vec!["采购入库", "客户", "采购条目", dh, "no_customer"];
        user.show = name_show(&user);
        let html = r2s(|o| material_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

// 销售出库
#[get("/material_out/{dh}")]
pub async fn material_out(
    db: web::Data<Pool>,
    dh_num: web::Path<String>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "销售出库".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" {
            "新单据"
        } else {
            &*dh_num
        };
        let setup = vec!["销售出库", "客户", "出库条目", dh, "no_customer"];
        user.show = name_show(&user);
        let html = r2s(|o| materialout_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/query_in")]
pub async fn query_in(
    db: web::Data<Pool>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "入库查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| queryin_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/change_query_out")]
pub async fn change_query_out(
    db: web::Data<Pool>,
    limit: web::Query<Search>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "出库查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "仓储管理", "出库查询", "pout_items", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/stock_query_in")]
pub async fn stock_query_in(
    db: web::Data<Pool>,
    limit: web::Query<Search>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "调库查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "仓储管理", "调入查询", "products", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/stock_query_out")]
pub async fn stock_query_out(
    db: web::Data<Pool>,
    limit: web::Query<Search>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "调库查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "仓储管理", "调出查询", "pout_items", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/kp_query")]
pub async fn kp_query(
    db: web::Data<Pool>,
    limit: web::Query<Search>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "财务开票".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "采购销售", "开票查询", "kp_items", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/other_query")]
pub async fn other_query(
    db: web::Data<Pool>,
    limit: web::Query<Search>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query2_html(o, user, "待办单据", &limit.s, "", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///业务往来
#[get("/business_query")]
pub async fn business_query(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "业务往来".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| businessquery_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///入库明细
#[get("/stockin_items")]
pub async fn stockin_items(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "入库明细".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| stockinitems_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///出库明细
#[get("/stockout_items")]
pub async fn stockout_items(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "出库明细".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| stockoutitems_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///发货明细
///销售明细
///客户管理
#[get("/customer_visit")]
pub async fn customer_visit(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "客户管理".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| visit_html(o, user, "客户"));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///客户管理
#[get("/information")]
pub async fn information(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "用户设置".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| information_html(o, user, "客户"));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 商品显示设置
#[get("/set_show")]
pub async fn set_show(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "用户设置".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| setshow_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///技术管理-采购新建
#[get("/tech_buy/{id}")]
pub async fn tech_buy(db: web::Data<Pool>, id: Identity, pid: web::Path<String>,) -> HttpResponse {
    let mut user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let dh = if *pid == "new" {
            "新文档".to_owned()
        } else {
            (&*pid).to_owned()
        };        
        user.show = name_show(&user);
        let html = r2s(|o| techbuy_html(o, user, dh));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///技术管理-客户新建
#[get("/tech_sale/{id}")]
pub async fn tech_sale(db: web::Data<Pool>, id: Identity, pid: web::Path<String>,) -> HttpResponse {
    let mut user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        let dh = if *pid == "new" {
            "新文档".to_owned()
        } else {
            (&*pid).to_owned()
        };        
        user.show = name_show(&user);
        let html = r2s(|o| techsale_html(o, user, dh));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///技术管理-采购技术查询
#[get("/tech_buy_query")]
pub async fn tech_buy_query(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| techbuyquery_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}
///技术管理-客户协议查询
#[get("/tech_sale_query")]
pub async fn tech_sale_query(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "技术管理".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| techsalequery_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///价格管理
#[get("/price_manage")]
pub async fn price_manage(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| price_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///商品类别管理
#[get("/product_cate")]
pub async fn product_cate(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "商品类别".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| cateset_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///产品信息
#[get("/product_info")]
pub async fn product_info(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "产品信息".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| productinfo_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

#[get("/anti_query")]
pub async fn anti_query(
    db: web::Data<Pool>,
    id: Identity,
) -> HttpResponse {
    let mut user = get_user(&db, id, "反审单据".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| antiquery_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}