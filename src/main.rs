use actix_files as fs;
use actix_identity::{CookieIdentityPolicy, IdentityService};
use actix_web::{cookie::time::Duration, web, web::Data, App, HttpServer};
use deadpool_postgres::Runtime;
use dotenv::dotenv;
use serde::Deserialize;

mod buysale;
mod ck;
mod customer;
mod fh;
mod fields;
use sales::html;
mod kp;
mod product;
mod query;
mod rk;
mod service;
mod statis;
mod tree;
mod user;
mod tech;
mod price;
mod funcset;

#[derive(Deserialize)]
struct Config {
    pg: deadpool_postgres::Config,
}

impl Config {
    pub fn from_env() -> Result<Self, config::ConfigError> {
        let cfg = config::Config::builder()
            .add_source(config::Environment::default().separator("__"))
            .build()?;

        cfg.try_deserialize()
    }
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    dotenv().ok();
    let port = dotenv::var("port").unwrap();

    let config = Config::from_env().unwrap();
    let pool = config
        .pg
        .create_pool(Some(Runtime::Tokio1), tokio_postgres::NoTls)
        .unwrap();

    println!("服务已启动: 127.0.0.1:{}", port);

    HttpServer::new(move || {
        App::new()
            .app_data(Data::new(pool.clone()))
            .app_data(web::PayloadConfig::new(20 * 1024 * 1024))
            .wrap(IdentityService::new(
                CookieIdentityPolicy::new(&[6; 32])
                    .name("auth-newsales")
                    .max_age(Duration::days(30))
                    .secure(false),
            ))
            
            // --- Base 模块 ---
            .service(html::index)
            .service(html::login)
            .service(user::login)
            .service(user::logon)
            .service(user::logout)
            .service(user::forget_pass)
            .service(user::change_pass)
            .service(user::phone_number)
            
            // --- 销售模块 (Sales) ---
            .service(
                web::scope("/sales")
                    // --- 页面 ---
                    // .service(
                    //     web::scope("/pages")
                    //         .service(web::resource("/new").to(html::material_out)) // 新建销售单
                    //         .service(web::resource("/{dh}").to(html::material_out)) // 打开已有销售单
                    //         .service(web::resource("/items/{dh}").to(html::stockout_items)) // 销售明细
                    //         .service(web::resource("/transport/{dh}").to(html::trans_items)) // 发货单
                    //         .service(web::resource("/kp/{dh}").to(html::kp)) // 开票
                    // )
                    // --- API ---
                    .service(
                        web::scope("/api")
                            // buysale.rs (通用)
                            .service(buysale::make_sumit_shen)
                            .service(buysale::make_formal)
                            // buysale.rs (销售相关)
                            .service(buysale::save_document_sale)
                            .service(buysale::fetch_document_items_sales)
                            .service(buysale::get_sale_dh)
                            .service(buysale::save_sale_money)
                            .service(buysale::check_ku)
                            .service(buysale::check_ku2)
                            .service(buysale::check_contract)
                            .service(buysale::po_check)
                            .service(buysale::get_po_check)
                            // fh.rs (发货)
                            .service(fh::get_sale_out)
                            .service(fh::fetch_document_fh)
                            .service(fh::save_transport)
                            .service(fh::fetch_trans_items)
                            .service(fh::get_items_trans)
                            .service(fh::materialout_docs)
                            .service(fh::materialout_saved_docs)
                            .service(fh::get_trans_info)
                            .service(fh::make_fh_complete)
                            // kp.rs (开票)
                            .service(kp::make_xs_kp)
                            .service(kp::fetch_sale_docs)
                            .service(kp::fetch_sale_saved_docs)
                            .service(kp::save_document_kp)
                            .service(kp::fetch_kp_items)
                            .service(kp::fetch_fh_items)
                            // statis.rs (销售统计)
                            .service(statis::get_sale_items)
                            .service(statis::sale_item_excel)
                            // rk.rs (图片保存)
                            .service(rk::pic_fh_save)
                            .service(rk::pic_kp_save)
                    )
            )

            // --- 采购模块 (Purchase) ---
            // ... 此处为以后重构采购模块留出位置 ...

            // -- 页面 (待归类) --
            .service(html::user_set)
            .service(html::user_manage)
            .service(html::product_set)
            .service(html::field_set)
            .service(html::customer_manage)
            .service(html::supplier_manage)
            .service(html::material_in)
            .service(html::stock_change_in)
            .service(html::stock_change_out)
            .service(html::kp_query)
            .service(html::query_in)
            .service(html::change_query_out)
            .service(html::stock_query_in)
            .service(html::stock_query_out)
            .service(html::business_query)
            .service(html::other_query)
            .service(html::stockin_items)
            .service(html::customer_visit)
            .service(html::information)
            .service(html::set_show)
            .service(html::tech_buy)
            .service(html::tech_sale)
            .service(html::tech_buy_query)
            .service(html::tech_sale_query)
            .service(html::price_manage)
            .service(html::product_cate)
            .service(html::product_info)
            .service(html::anti_query)

            // -- 技术协议 (待归类) --
            .service(tech::get_material)
            .service(tech::get_number)
            .service(tech::save_techbuy)
            .service(tech::shen_techbuy)
            .service(tech::get_tech_buy)
            .service(tech::get_tech_buy_data)
            .service(tech::pass_techbuy)
            .service(tech::reject_techbuy)
            .service(tech::get_suplier_auto)
            .service(tech::pdf_save)
            .service(tech::delete_suppliers)
            .service(tech::add_supplier)
            .service(tech::fetch_suppliers)
            .service(tech::revise_techbuy)
            .service(tech::submit_revision)
            .service(tech::get_revision)
            .service(tech::get_revise_data)
            .service(tech::fei_techbuy)
            .service(tech::save_techsale)
            .service(tech::get_tech_sale)
            .service(tech::get_tech_sale_data)
            .service(tech::pdf_save_sale)
            .service(tech::fei_techsale)

            // -- 采购销售 (待归类) --
            .service(buysale::fetch_inout_fields)
            .service(buysale::buyin_auto)
            .service(buysale::get_status_auto)
            .service(buysale::get_factory_auto)
            .service(buysale::fetch_product_buyin)
            .service(buysale::fetch_one_product)
            .service(buysale::save_document)
            .service(buysale::fetch_document)
            .service(buysale::fetch_document_items)
            .service(buysale::get_truck_auto)
            .service(buysale::get_truck2_auto)
            .service(buysale::fetch_other_documents)
            .service(buysale::anti_formal)
             // -- 入库 (待归类) --
            .service(rk::material_auto)
            .service(rk::material_auto_kt)
            .service(rk::get_items)
            .service(rk::fetch_max_num)
            .service(rk::save_material)
            .service(rk::make_rk_complete)
            .service(rk::make_xs_wight)
            .service(rk::fetch_document_rkd)
            .service(rk::fetch_document_items_rk)
            .service(rk::fetch_document_items_tr)
            .service(rk::materialin_docs)
            .service(rk::materialin_saved_docs)
            .service(rk::pic_in)
            .service(rk::pic_in_save)
            .service(rk::pdf_in)
            .service(rk::pdf_in_save)
            // -- 出库 (待归类) --
            .service(ck::materialout_auto)
            .service(ck::material_auto_out)
            .service(ck::material_auto_sotckout)
            .service(ck::get_docs_out)
            .service(ck::get_items_out)
            .service(ck::save_material_ck)
            .service(ck::make_ck_complete)
            .service(ck::fetch_document_ck)
            .service(ck::fetch_document_items_ck)
            .service(ck::fetch_document_items_tc)
            .service(ck::materialsale_docs)
            .service(ck::materialsale_saved_docs)
            .service(ck::get_material_tag)
            // -- 查询 (待归类) --
            .service(query::fetch_show_fields)
            .service(query::fetch_all_documents)
            .service(query::fetch_a_documents)
            .service(query::documents_del)
            .service(query::before_fei)
            .service(query::documents_fei)
            .service(query::trans_excel)
            .service(query::fetch_in_docs)
            // -- 员工用户 (待归类) --
            .service(user::pull_users)
            .service(user::edit_user)
            .service(user::del_user)
            .service(user::reset_user_password)
            // -- 树 (待归类) --
            .service(tree::tree)
            .service(tree::tree_add)
            .service(tree::tree_edit)
            .service(tree::tree_del)
            .service(tree::tree_auto)
            .service(tree::tree_drag)
            .service(tree::save_tree_show)
            // -- 产品 (待归类) --
            .service(product::fetch_product)
            .service(product::fetch_statistic)
            .service(product::update_product)
            .service(product::add_product)
            .service(product::product_auto)
            .service(product::fetch_pout_items)
            .service(product::fetch_lu)
            .service(product::product_out)
            .service(product::product_in)
            .service(product::product_datain)
            .service(product::product_updatein)
            .service(product::fetch_filter_items)
            .service(product::fetch_all_info)
            // -- 字段设置 (待归类) --
            .service(fields::fetch_fields)
            .service(fields::fetch_fields2)
            .service(fields::update_tableset)
            .service(fields::update_tableset2)
            // -- 客户供应商 (待归类) --
            .service(customer::fetch_customer)
            .service(customer::update_customer)
            .service(customer::add_customer)
            .service(customer::customer_auto)
            .service(customer::customer_out)
            .service(customer::customer_in)
            .service(customer::supplier_in)
            .service(customer::customer_addin)
            .service(customer::customer_updatein)
            .service(customer::fetch_supplier)
            .service(customer::fetch_inout_customer)
            .service(customer::get_customer_po)
            .service(customer::get_customer_simp)
            .service(customer::fetch_visit)
            .service(customer::fetch_information)
            .service(customer::save_information)
            .service(customer::get_customer_address)
            .service(customer::change_customer_address)
            .service(customer::del_customer_address)
            .service(customer::reset_customer_password)
            // -- 统计 (待归类) --
            .service(statis::get_stockin_items)
            .service(statis::get_stockout_items)
            .service(statis::get_trans_items)
            .service(statis::stockin_excel)
            .service(statis::stockout_excel)
            .service(statis::business_excel)
            .service(statis::trans_item_excel)
            .service(statis::fetch_business)
            .service(statis::fetch_statis)
            .service(statis::fetch_cost)
            .service(statis::home_statis)
            .service(statis::get_waitshen)
            // -- 价格管理 (待归类) --
            .service(price::fetch_price_customer)
            .service(price::price_in)
            .service(price::fetch_price_items)
            .service(price::get_customer_auto)
            .service(price::add_price_customer)
            .service(price::edit_price_customer)
            .service(price::del_price_customer)
            .service(price::add_price_item)
            .service(price::edit_price_item)
            .service(price::del_price_item)
            .service(price::fetch_price_history)
            .service(price::price_out)

            // -- 功能设置 (待归类) --
            .service(funcset::fetch_product_cate)
            .service(funcset::create_cate)
            .service(funcset::edit_cate)
            .service(funcset::order_cate)
            .service(funcset::get_product_info)
            .service(funcset::get_product_info_filter_items)
            .service(funcset::export_product_info)
            .service(funcset::add_product_info)
            .service(funcset::edit_product_info)
            .service(funcset::get_material_auto)
            .service(funcset::get_status_info_auto)
            .service(funcset::get_tech_no_auto)
            .service(funcset::fetch_anti_shen)

            // -- 服务 --
            .service(service::fetch_nothing)
            .service(service::serve_download)
            .service(web::resource("static/{name}").to(html::static_file))
            .service(fs::Files::new("/assets", "assets"))
            .service(fs::Files::new("/upload", "upload"))
    })
    .bind(format!("127.0.0.1:{}", port))?
    .run()
    .await
}