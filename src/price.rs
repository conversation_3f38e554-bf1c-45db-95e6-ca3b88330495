use crate::service::*;
use actix_identity::Identity;
use actix_multipart::Multipart;
use actix_web::{get, post, web, HttpResponse};
use calamine::{open_workbook, Reader, Xlsx};
use chrono::{Datelike, NaiveDate};
use deadpool_postgres::Pool;
use rust_xlsxwriter::{Format, FormatAlign, Workbook};
use serde::Deserialize;
use serde_json::{json, Value};

#[derive(Deserialize)]
pub struct CustomerData {
    pub customer: String,
}

// 获取价格客户
#[get("/fetch_price_customer")]
pub async fn fetch_price_customer(
    db: web::Data<Pool>,
    data: web::Query<CustomerData>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let query_customer = if data.customer != "all" {
            format!(" where customers.文本字段7 ILIKE '%{}%'", data.customer)
        } else {
            String::new()
        };

        let sql = format!(
            r#"
                select price.id, customer_id, 文本字段7 名称, exe_date::text from price 
                join customers on price.customer_id = customers.id
                {query_customer}
                order by exe_date desc;
            "#,
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut docs = Vec::new();

        for row in rows {
            let doc = json!({
                "id": row.get::<&str, i32>("id"),
                "customer_id": row.get::<&str, i32>("customer_id"),
                "name": row.get::<&str, String>("名称"),
                "date": row.get::<&str, String>("exe_date"),
            });
            docs.push(doc);
        }

        HttpResponse::Ok().json(docs)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取价格明细
#[post("/fetch_price_items")]
pub async fn fetch_price_items(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        // let search_name = post_data.name.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let price_id = post_data.id.parse::<i32>().unwrap_or(1);
        let cate = post_data.cate.to_lowercase();

        let search_sql = if cate != "" {
            format!("and (pc.name ILIKE '%{}%' OR material ILIKE '%{}%' OR status ILIKE '%{}%' OR factory ILIKE '%{}%')", cate, cate, cate, cate)
        } else {
            "".to_string()
        };

        let sql = &format!(
            r#"
                select ROW_NUMBER () OVER (ORDER BY {sort}) as 序号,
                    pi.id::text, pc.name as cate, pi.name, material, size, status, factory, 
                    all_price, retail_price, cut_price, note 
                from price_items pi
                join product_cate pc on pi.cate_id = pc.id
                where price_id = {price_id} {search_sql}
                ORDER BY {sort} OFFSET {skip} LIMIT {rec}
            "#
        );

        // println!("Executing SQL: {}", sql); // 调试输出SQL语句

        let rows = &conn.query(sql, &[]).await.unwrap();

        let mut docs = Vec::new();

        for row in rows {
            let doc = json!({
                "序号": row.get::<&str, i64>("序号"),
                "id": row.get::<&str, String>("id"),
                "cate": row.get::<&str, String>("cate"),
                "name": row.get::<&str, String>("name"),
                "material": row.get::<&str, String>("material"),
                "size": row.get::<&str, String>("size"),
                "status": row.get::<&str, String>("status"),
                "factory": row.get::<&str, String>("factory"),
                "all_price": row.get::<&str, String>("all_price"),
                "retail_price": row.get::<&str, String>("retail_price"),
                "cut_price": row.get::<&str, String>("cut_price"),
                "note": row.get::<&str, String>("note"),
            });
            docs.push(doc);
        }

        let count_sql = format!(
            r#"select count(*) as 记录数
                from price_items pi
                join product_cate pc on pi.cate_id = pc.id
                where price_id = {price_id} {search_sql}
            "#
        );

        let (pages, count) = pages(&conn, count_sql, post_data.rec).await;
        HttpResponse::Ok().json((docs, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//供应商自动输入
#[get("/get_customer_auto")]
pub async fn get_customer_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let s = search.s.to_lowercase();
        let cate_s = if search.cate != "" {
            format!("类别 like '%{}%' AND ", search.cate)
        } else {
            "".to_string()
        };

        let sql = if cate_s == "客户" {
            &format!(
                r#"SELECT id::text, 文本字段7 AS label FROM customers 
                    WHERE {cate_s} (助记码 LIKE '%{s}%' OR LOWER(名称) LIKE '%{s}%' OR LOWER(文本字段7) LIKE '%{s}%') AND 停用=false LIMIT 10"#
            )
        } else {
            &format!(
                r#"SELECT id::text, 文本字段1 AS label FROM customers 
                    WHERE {cate_s} (助记码 LIKE '%{s}%' OR LOWER(名称) LIKE '%{s}%' OR LOWER(文本字段1) LIKE '%{s}%') AND 停用=false LIMIT 10"#
            )
        };

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 新增客户协议价格
#[post("/add_price_customer")]
pub async fn add_price_customer(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();

        // 开启事务
        let transaction = conn.transaction().await.unwrap();

        // 1. 先插入到 price 表
        let insert_price_sql = format!(
            r#"
                INSERT INTO price (customer_id, exe_date) 
                VALUES ({}, '{}') 
                RETURNING id
            "#,
            data["customer_id"].to_string().trim_matches('"'),
            data["new_date"].to_string().trim_matches('"'),
        );

        let price_result = transaction
            .query(insert_price_sql.as_str(), &[])
            .await
            .unwrap();

        let new_price_id: i32 = price_result[0].get(0);

        // 2. 复制 price_items 表中对应的记录
        let copy_items_sql = format!(
            r#"
                INSERT INTO price_items (price_id, cate_id, name, material, size, status, factory, all_price, retail_price, cut_price, note)
                SELECT {}, cate_id, name, material, size, status, factory, all_price, retail_price, cut_price, note
                FROM price_items 
                WHERE price_id = {}
            "#,
            new_price_id,
            data["price_id"].to_string().trim_matches('"'),
        );

        let copy_result = transaction.execute(copy_items_sql.as_str(), &[]).await;

        match copy_result {
            Ok(_) => {
                // 提交事务
                transaction.commit().await.unwrap();
                HttpResponse::Ok().json(1)
            }
            Err(e) => {
                // 回滚事务
                transaction.rollback().await.unwrap();
                println!("复制价格明细失败: {}", e);
                HttpResponse::Ok().json(-1)
            }
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 编辑客户协议
#[post("/edit_price_customer")]
pub async fn edit_price_customer(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"
                UPDATE price SET customer_id = {}, exe_date = '{}' 
                WHERE id = {}
                "#,
            data["customer_id"].to_string().trim_matches('"'),
            data["new_date"].to_string().trim_matches('"'),
            data["price_id"].to_string().trim_matches('"')
        );

        let _result = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 删除客户协议
#[post("/del_price_customer")]
pub async fn del_price_customer(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"
                DELETE FROM price WHERE id = {}
            "#,
            data["id"].to_string().trim_matches('"'),
        );

        let _result = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 增加价格条目
#[post("/add_price_item")]
pub async fn add_price_item(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"
                INSERT INTO price_items (price_id, cate_id, name, material, size, status, factory, all_price, retail_price, cut_price, note)
                VALUES ({}, {}, '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}')
            "#,
            data["price_id"].to_string().trim_matches('"'),
            data["cate_id"].to_string().trim_matches('"'),
            data["name"].to_string().trim_matches('"'),
            data["material"].to_string().trim_matches('"'),
            data["size"].to_string().trim_matches('"'),
            data["status"].to_string().trim_matches('"'),
            data["factory"].to_string().trim_matches('"'),
            data["all_price"].to_string().trim_matches('"'),
            data["retail_price"].to_string().trim_matches('"'),
            data["cut_price"].to_string().trim_matches('"'),
            data["note"].to_string().trim_matches('"'),
        );

        let _result = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 编辑价格条目
#[post("/edit_price_item")]
pub async fn edit_price_item(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"
                update price_items set cate_id={}, name='{}', material='{}', size='{}', 
                    status='{}', factory='{}', all_price='{}', retail_price='{}', 
                    cut_price='{}', note='{}'
                where id::text='{}'
            "#,
            data["cate_id"].to_string().trim_matches('"'),
            data["name"].to_string().trim_matches('"'),
            data["material"].to_string().trim_matches('"'),
            data["size"].to_string().trim_matches('"'),
            data["status"].to_string().trim_matches('"'),
            data["factory"].to_string().trim_matches('"'),
            data["all_price"].to_string().trim_matches('"'),
            data["retail_price"].to_string().trim_matches('"'),
            data["cut_price"].to_string().trim_matches('"'),
            data["note"].to_string().trim_matches('"'),
            data["id"].to_string().trim_matches('"'),
        );

        let _result = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 删除客户协议
#[post("/del_price_item")]
pub async fn del_price_item(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"
                DELETE FROM price_items WHERE id::text = '{}'
            "#,
            data["id"].to_string().trim_matches('"'),
        );

        let _result = conn.query(sql.as_str(), &[]).await.unwrap();
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//批量导入数据
#[post("/price_in")]
pub async fn price_in(db: web::Data<Pool>, payload: Multipart, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let path = save_file(payload).await.unwrap();
        let mut excel: Xlsx<_> = open_workbook(path).unwrap();

        let conn = db.get().await.unwrap();

        // 清空现有数据（如果需要的话）
        let _clear_result = conn
            .execute("DELETE FROM price_items WHERE price_id = $1", &[&1i32])
            .await;

        // 读取Excel文件的第一个工作表
        if let Some(Ok(range)) = excel.worksheet_range_at(0) {
            let mut rows_inserted = 0;

            // 跳过标题行，从第二行开始处理数据
            for (row_index, row) in range.rows().enumerate() {
                if row_index == 0 {
                    continue; // 跳过标题行
                }

                // 确保行有足够的列数
                if row.len() >= 10 {
                    // 提取各列数据
                    let cate = row[0].to_string().trim().to_string();
                    let name = row[1].to_string().trim().to_string();
                    let material = row[2].to_string().trim().to_string();
                    let size = row[3].to_string().trim().to_string();
                    let status = row[4].to_string().trim().to_string();
                    let factory = row[5].to_string().trim().to_string();
                    let all_price = row[6].to_string().trim().to_string();
                    let retail_price = row[7].to_string().trim().to_string();
                    let cut_price = row[8].to_string().trim().to_string();
                    let note = row[9].to_string().trim().to_string();

                    let sql = format!(
                        r#"
                        INSERT INTO price_items 
                        (price_id, cate, name, material, size, status, factory, all_price, retail_price, cut_price, note)
                        VALUES (1, '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}', '{}')
                        "#,
                        cate,
                        name,
                        material,
                        size,
                        status,
                        factory,
                        all_price,
                        retail_price,
                        cut_price,
                        note
                    );

                    // println!("Executing SQL: {}", sql); // 调试输出SQL语句

                    let insert_result = conn.execute(&sql, &[]).await;

                    match insert_result {
                        Ok(_) => rows_inserted += 1,
                        Err(e) => {
                            println!("插入数据时出错 (行 {}): {}", row_index + 1, e);
                        }
                    }
                }
            }

            println!("成功导入 {} 行数据", rows_inserted);
        }

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 导出价格明细
#[post("/price_out")]
pub async fn price_out(db: web::Data<Pool>, data: web::Json<Value>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let file_name_for_download = format!(
            "{}_价格协议_{}.xlsx",
            data["name"].to_string().trim_matches('"'),
            data["date"].to_string().trim_matches('"')
        );
        let file_path = format!("./download/{}", file_name_for_download);
        let mut wb = Workbook::new();
        let sheet = wb.add_worksheet().set_name("价格明细").unwrap();

        let format = Format::new().set_align(FormatAlign::Center).set_bold();

        let headers = [
            "类别",
            "名称",
            "钢种",
            "规格",
            "状态",
            "产地",
            "整支单价（元/KG)",
            "切分单价（元/KG)",
            "锯料费 (元/件)",
            "备注",
        ];
        for (i, header) in headers.iter().enumerate() {
            sheet
                .write_with_format(0, i as u16, *header, &format)
                .unwrap();
            sheet.set_column_width(i as u16, 15.0).unwrap();
        }

        let search_name = data["search"].to_string().trim_matches('"').to_lowercase();

        let search_sql = if search_name != "" {
            format!("and (pc.name ILIKE '%{}%' OR material ILIKE '%{}%' OR status ILIKE '%{}%' OR factory ILIKE '%{}%')", 
            search_name, search_name, search_name, search_name)
        } else {
            "".to_string()
        };

        let sql = format!(
            r#"
                select pc.name as cate, pi.name, material, size, status, factory, 
                    all_price, retail_price, cut_price, note 
                from price_items pi
                join product_cate pc on pi.cate_id = pc.id
                where price_id = {} {search_sql}
                ORDER BY pc.ord, pi.material
            "#,
            data["price_id"].to_string().trim_matches('"'),
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        for (row_num, row) in rows.iter().enumerate() {
            let row_num = (row_num + 1) as u32;
            sheet
                .write(row_num, 0, row.get::<&str, &str>("cate"))
                .unwrap();
            sheet
                .write(row_num, 1, row.get::<&str, &str>("name"))
                .unwrap();
            sheet
                .write(row_num, 2, row.get::<&str, &str>("material"))
                .unwrap();
            sheet
                .write(row_num, 3, row.get::<&str, &str>("size"))
                .unwrap();
            sheet
                .write(row_num, 4, row.get::<&str, &str>("status"))
                .unwrap();
            sheet
                .write(row_num, 5, row.get::<&str, &str>("factory"))
                .unwrap();
            sheet
                .write(row_num, 6, row.get::<&str, &str>("all_price"))
                .unwrap();
            sheet
                .write(row_num, 7, row.get::<&str, &str>("retail_price"))
                .unwrap();
            sheet
                .write(row_num, 8, row.get::<&str, &str>("cut_price"))
                .unwrap();
            sheet
                .write(row_num, 9, row.get::<&str, &str>("note"))
                .unwrap();
        }

        wb.save(&file_path).unwrap();

        HttpResponse::Ok().json(file_name_for_download)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取价格历史数据
#[post("/fetch_price_history")]
pub async fn fetch_price_history(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "价格体系".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        // 获取价格历史数据
        let sql = format!(
            r#"
                WITH monthly_data AS (
                    SELECT
                        DATE_TRUNC('month', p.exe_date) as month_date,
                        pi.all_price,
                        pi.retail_price,
                        p.exe_date,
                        ROW_NUMBER() OVER (
                            PARTITION BY DATE_TRUNC('month', p.exe_date)
                            ORDER BY p.exe_date ASC
                        ) as rn
                    FROM price p
                    JOIN price_items pi ON p.id = pi.price_id
                    WHERE p.customer_id = {}
                        AND pi.name = '{}'
                        AND pi.material = '{}'
                        AND pi.size = '{}'
                        AND pi.status = '{}'
                        AND pi.factory = '{}'
                )
                SELECT
                    month_date,
                    all_price,
                    retail_price,
                    exe_date
                FROM monthly_data
                WHERE rn = 1
                ORDER BY month_date ASC
            "#,
            data["customer_id"].to_string().trim_matches('"'),
            data["name"].to_string().trim_matches('"'),
            data["material"].to_string().trim_matches('"'),
            data["size"].to_string().trim_matches('"'),
            data["status"].to_string().trim_matches('"'),
            data["factory"].to_string().trim_matches('"'),
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        if rows.is_empty() {
            let result = json!([[], [], [], []]);
            return HttpResponse::Ok().json(result);
        }

        let mut price_data = std::collections::HashMap::new();
        let mut all_dates: Vec<NaiveDate> = Vec::new();

        for row in rows {
            let all_price: String = row.get("all_price");
            let retail_price: String = row.get("retail_price");

            // 直接从数据库获取日期
            let actual_date: NaiveDate = row.get("exe_date");
            all_dates.push(actual_date);
            let month_key = actual_date.format("%Y-%m").to_string();
            price_data.insert(
                month_key,
                (
                    all_price.parse::<f64>().unwrap_or(0.0),
                    retail_price.parse::<f64>().unwrap_or(0.0),
                ),
            );
        }

        all_dates.sort();

        // 确保有数据
        if all_dates.is_empty() {
            return HttpResponse::Ok().json(json!([[], [], [], []]));
        }

        let first_date = all_dates.first().unwrap();
        let last_date = all_dates.last().unwrap();

        let mut months = Vec::new();
        let mut indexes = Vec::new();
        let mut all_prices = Vec::new();
        let mut retail_prices = Vec::new();
        let mut last_all_price = 0.0;
        let mut last_retail_price = 0.0;

        let mut current_month_iter =
            NaiveDate::from_ymd_opt(first_date.year(), first_date.month(), 1).unwrap();
        let last_month_start =
            NaiveDate::from_ymd_opt(last_date.year(), last_date.month(), 1).unwrap();

        let mut index_counter = 1;

        // 从第一个月开始，直到最后一个月
        while current_month_iter <= last_month_start {
            let month_str = current_month_iter.format("%Y-%m").to_string();
            months.push(month_str.clone());
            indexes.push(index_counter);

            // 获取当前月份的价格数据
            if let Some((all_price, retail_price)) = price_data.get(&month_str) {
                all_prices.push(*all_price);
                retail_prices.push(*retail_price);
                last_all_price = *all_price;
                last_retail_price = *retail_price;
            } else {
                all_prices.push(last_all_price);
                retail_prices.push(last_retail_price);
            }

            index_counter += 1;

            // 移动到下一个月
            if current_month_iter.month() == 12 {
                current_month_iter = NaiveDate::from_ymd_opt(current_month_iter.year() + 1, 1, 1)
                    .unwrap_or(current_month_iter);
            } else {
                current_month_iter = current_month_iter
                    .with_month(current_month_iter.month() + 1)
                    .unwrap_or(current_month_iter);
            }
        }

        let result = json!([indexes, months, all_prices, retail_prices]);
        HttpResponse::Ok().json(result)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
