use crate::service::*;
use actix_identity::Identity;
use actix_multipart::Multipart;
use actix_web::{get, post, web, HttpResponse};
use calamine::{open_workbook, Reader, Xlsx};
use deadpool_postgres::Pool;
use rust_xlsxwriter::{Format, FormatAlign, Workbook};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

#[derive(Deserialize, Serialize)]
pub struct CustomerData {
    pub data: String,
    pub cate: String,
}

///获取客户
#[post("/fetch_customer")]
pub async fn fetch_customer(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();
        let cate: Vec<&str> = post_data.cate.split('#').collect();
        let cate0 = cate[0];
        let sort = &post_data.sort;
        let rec = post_data.rec;

        let cate_sql = if cate.len() > 1 && cate[1] != "全部" {
            format!("AND 文本字段10 = '{}'", cate[1])
        } else {
            "".to_owned()
        };

        let fields = get_fields(db.clone(), cate0).await;

        let mut sql_fields = "SELECT id::text,".to_owned();

        for f in &fields {
            sql_fields += &format!("{},", f.field_name);
        }

        let sql = format!(
            r#"{sql_fields} ROW_NUMBER () OVER (ORDER BY {sort}) as 序号 FROM customers WHERE 
            类别='{cate0}' AND LOWER(名称) LIKE '%{name}%' {cate_sql} ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#,
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let products = build_string_from_base(rows, fields);

        let count_sql = format!(
            r#"SELECT count(id) as 记录数 
            FROM customers 
            WHERE 类别='{cate0}' AND LOWER(名称) LIKE '%{name}%' {cate_sql}"#
        );

        let (pages, count) = pages(&conn, count_sql, rec).await;

        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///编辑更新客户
#[post("/update_customer")]
pub async fn update_customer(
    db: web::Data<Pool>,
    p: web::Json<CustomerData>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, format!("{}管理", p.cate)).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let fields = get_fields(db.clone(), &p.cate).await;
        let field_names: Vec<&str> = p.data.split(SPLITER).collect();
        let py = rust_pinyin::get_pinyin(&field_names[2]); //[2] 是名称
        let init = "UPDATE customers SET ".to_owned();
        let mut sql = build_sql_for_update(field_names.clone(), init, fields, 2);
        sql += &format!(r#"助记码='{}' WHERE id={}"#, py, field_names[0]);

        let _ = &conn.execute(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///编辑增加客户
#[post("/add_customer")]
pub async fn add_customer(
    db: web::Data<Pool>,
    p: web::Json<CustomerData>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, format!("{}管理", p.cate)).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let fields = get_fields(db.clone(), &p.cate).await;
        let field_names: Vec<&str> = p.data.split(SPLITER).collect();
        let py = rust_pinyin::get_pinyin(&field_names[2]); //[2] 是名称

        let mut init = "INSERT INTO customers (".to_owned();

        for f in &fields {
            init += &format!("{},", &*f.field_name);
        }

        init += "助记码,类别) VALUES(";
        let mut sql = build_sql_for_insert(&field_names, init, &fields, 2);
        sql += &format!("'{}', '{}') RETURNING id", py, p.cate);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let id: i32 = rows[0].get(0);

        HttpResponse::Ok().json(id)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//自动完成
#[get("/customer_auto")]
pub async fn customer_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let s = search.s.to_lowercase();
        let cate_s = if search.cate != "" {
            format!("类别 like '%{}%' AND ", search.cate)
        } else {
            "".to_string()
        };
        let sql = &format!(
            r#"SELECT id::text, 名称 AS label FROM customers 
            WHERE {cate_s} (助记码 LIKE '%{s}%' OR LOWER(名称) LIKE '%{s}%') AND 停用=false LIMIT 10"#
        );

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[derive(Deserialize)]
pub struct OutCate {
    cate: String,
}

//导出数据
#[post("/customer_out")]
pub async fn customer_out(
    db: web::Data<Pool>,
    out_data: web::Json<OutCate>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "导出数据".to_owned()).await;
    if user.name != "" {
        let fields = get_fields(db.clone(), &out_data.cate).await;
        let file_name = format!("./download/{}.xlsx", out_data.cate);
        let mut wb = Workbook::new();
        let sheet = wb.add_worksheet().set_name("数据").unwrap();

        let format = Format::new().set_align(FormatAlign::Center).set_bold();
        let format2 = Format::new().set_align(FormatAlign::Center);

        sheet.write_with_format(0, 0, "编号", &format).unwrap();
        sheet.set_column_width(0, 10.0).unwrap();

        let mut n = 1;
        for f in &fields {
            sheet
                .write_with_format(0, n, &f.show_name, &format)
                .unwrap();
            sheet.set_column_width(n, f.show_width * 2.5).unwrap();
            n += 1;
        }

        let init = r#"SELECT id as 编号,"#.to_owned();
        let mut sql = build_sql_for_excel(init, &fields, "customers".to_owned());
        sql = sql.trim_end_matches(",").to_owned();

        sql += &format!(" FROM customers WHERE 类别='{}'", out_data.cate);

        let conn = db.get().await.unwrap();
        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut n = 1u32;
        for row in rows {
            let id: i32 = row.get("编号");
            sheet.write_with_format(n, 0, id as f64, &format2).unwrap();
            let mut m = 1u16;
            for f in &fields {
                let name: &str = row.get(&*f.field_name);
                if f.data_type == "布尔" {
                    sheet.write_with_format(n, m, name, &format2).unwrap();
                } else {
                    sheet.write(n, m, name).unwrap();
                }

                m += 1;
            }

            n += 1;
        }

        wb.save(file_name).unwrap();

        HttpResponse::Ok().json(&out_data.cate)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//批量导入、批量更新返回数据
#[post("/customer_in")]
pub async fn customer_in(db: web::Data<Pool>, payload: Multipart, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "批量导入".to_owned()).await;
    if user.name != "" {
        let (records, total_rows) = data_in(db, payload, "客户").await;
        if total_rows == -1 {
            HttpResponse::Ok().json(-2)
        } else {
            HttpResponse::Ok().json((records, total_rows))
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//批量导入、批量更新返回数据
#[post("/supplier_in")]
pub async fn supplier_in(db: web::Data<Pool>, payload: Multipart, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "批量导入".to_owned()).await;
    if user.name != "" {
        let (records, total_rows) = data_in(db, payload, "供应商").await;
        if total_rows == -1 {
            HttpResponse::Ok().json(-2)
        } else {
            HttpResponse::Ok().json((records, total_rows))
        }
    } else {
        HttpResponse::Ok().json(-1)
    }
}

async fn data_in(db: web::Data<Pool>, payload: Multipart, cate: &str) -> (Vec<String>, i32) {
    let path = save_file(payload).await.unwrap();

    let mut total_rows = 0;
    let fields = get_fields(db.clone(), cate).await;
    let mut records = Vec::new();
    let mut excel: Xlsx<_> = open_workbook(path).unwrap();

    if let Ok(r) = excel.worksheet_range("数据") {
        let mut num = 0;
        let total_coloum = r.get_size().1;
        total_rows = r.get_size().0 - 1;

        if total_coloum - 1 != fields.len() {
            return (records, -1);
        }

        if total_rows > 0 {
            //制作表头数据
            let mut rec = "".to_owned();
            rec += &format!("{}{}", "编号", SPLITER);
            for f in &fields {
                rec += &format!("{}{}", &*f.show_name, SPLITER);
            }

            records.push(rec);

            for i in 0..total_rows {
                let mut rec = "".to_owned();
                for j in 0..total_coloum {
                    rec += &format!("{}{}", r[(i + 1, j)], SPLITER);
                }

                records.push(rec);

                num += 1;
                if num == 50 {
                    break;
                }
            }
        }
    }

    (records, total_rows as i32)
}

//批量导入数据写库
#[post("/customer_addin")]
pub async fn customer_addin(
    db: web::Data<Pool>,
    data_cate: web::Json<OutCate>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "批量导入".to_owned()).await;
    if user.name != "" {
        let mut excel: Xlsx<_> = open_workbook("./upload/upload_in.xlsx").unwrap();

        if let Ok(r) = excel.worksheet_range("数据") {
            let fields = get_fields(db.clone(), &data_cate.cate).await;
            let conn = db.get().await.unwrap();
            let total_rows = r.get_size().0 - 1;
            if total_rows > 0 {
                let mut init = "INSERT INTO customers (".to_owned();

                for f in &fields {
                    init += &format!("{},", &*f.field_name);
                }
                init += "助记码, 类别) VALUES(";

                for j in 0..total_rows {
                    let mut sql = init.clone();

                    for i in 0..fields.len() {
                        if fields[i].data_type == "文本" {
                            sql += &format!("'{}',", r[(j + 1, i + 1)]);
                        } else if fields[i].data_type == "实数" || fields[i].data_type == "整数"
                        {
                            sql += &format!("{},", r[(j + 1, i + 1)]);
                        } else {
                            let op: Vec<&str> = fields[i].option_value.split("_").collect();
                            let value = format!("{}", r[(j + 1, i + 1)]);
                            let val = if value == op[0] { true } else { false };
                            sql += &format!("{},", val);
                        }
                    }

                    let name = &format!("{}", r[(j + 1, 1)]);
                    let py = rust_pinyin::get_pinyin(name);
                    sql += &format!("'{}','{}')", py, data_cate.cate);

                    let _ = &conn.query(sql.as_str(), &[]).await.unwrap();
                }
            }
        }
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

//批量更新数据写库
#[post("/customer_updatein")]
pub async fn customer_updatein(
    db: web::Data<Pool>,
    data_cate: web::Json<OutCate>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "批量导入".to_owned()).await;
    if user.name != "" {
        let mut excel: Xlsx<_> = open_workbook("./upload/upload_in.xlsx").unwrap();

        if let Ok(r) = excel.worksheet_range("数据") {
            let fields = get_fields(db.clone(), &data_cate.cate).await;
            let conn = db.get().await.unwrap();
            let total_rows = r.get_size().0 - 1;

            if total_rows > 0 {
                for i in 0..total_rows {
                    let mut sql = "UPDATE customers SET ".to_owned();

                    for j in 0..fields.len() {
                        if fields[j].data_type == "文本" {
                            sql += &format!("{}='{}',", fields[j].field_name, r[(i + 1, j + 1)]);
                        } else if fields[j].data_type == "实数" || fields[j].data_type == "整数"
                        {
                            sql += &format!("{}={},", fields[j].field_name, r[(i + 1, j + 1)]);
                        } else {
                            let op: Vec<&str> = fields[j].option_value.split("_").collect();
                            let value = format!("{}", r[(i + 1, j + 1)]);
                            let val = if value == op[0] { true } else { false };
                            sql += &format!("{}={},", fields[j].field_name, val);
                        }
                    }
                    let name = &format!("{}", r[(i + 1, 1)]);
                    let id = format!("{}", r[(i + 1, 0)]);
                    let py = rust_pinyin::get_pinyin(name);
                    sql += &format!(r#"助记码='{}' WHERE id={}"#, py, id);

                    let _ = &conn.query(sql.as_str(), &[]).await.unwrap();
                }
            }
        }
        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取客户PO
#[post("/get_customer_po")]
pub async fn get_customer_po(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"select 文本字段8 from documents WHERE 单号='{}' {}"#,
            data, NOT_DEL_SQL
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut po: String = "".to_owned();
        for row in rows {
            po = row.get(0);
        }

        HttpResponse::Ok().json(po)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取客户简称
#[post("/get_customer_simp")]
pub async fn get_customer_simp(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(r#"select 文本字段7 from customers WHERE 名称='{data}'"#);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut po: String = "".to_owned();
        for row in rows {
            po = row.get(0);
        }

        HttpResponse::Ok().json(po)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 获取客户地址
#[post("/get_customer_address")]
pub async fn get_customer_address(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"select id, 收货地址, 收货人, 电话, 默认 from address WHERE 客户id={data} order by 默认 desc"#
        );
        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut address = Vec::new();
        for row in rows {
            let json = json!({
                "id": row.get::<&str, i32>("id"),
                "address": row.get::<&str, &str>("收货地址"),
                "name": row.get::<&str, &str>("收货人"),
                "phone": row.get::<&str, &str>("电话"),
                "default": row.get::<&str, bool>("默认"),
            });

            address.push(json);
        }
        HttpResponse::Ok().json(address)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 添加编辑客户地址
#[post("/change_customer_address")]
pub async fn change_customer_address(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        if data["default"] == true {
            let sql = format!(
                r#"update address set 默认=false where 客户id={}"#,
                data["cusid"]
            );
            let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

            let sql = format!(
                r#"update customers set 信用评价='{}',地区='{}', 文本字段9='{}' where id={}"#,
                data["name"].to_string().trim_matches('"'),
                data["phone"].to_string().trim_matches('"'),
                data["address"].to_string().trim_matches('"'),
                data["cusid"]
            );
            let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        }

        let sql = if data["cate"].to_string().trim_matches('"') == "add" {
            format!(
                r#"insert into address (客户id, 收货地址, 收货人, 电话, 默认) values ({}, '{}', '{}', '{}', {})"#,
                data["cusid"],
                data["address"].to_string().trim_matches('"'),
                data["name"].to_string().trim_matches('"'),
                data["phone"].to_string().trim_matches('"'),
                data["default"]
            )
        } else {
            format!(
                r#"update address set 客户id={}, 收货地址='{}', 收货人='{}', 电话='{}', 默认={} where id={}"#,
                data["cusid"],
                data["address"].to_string().trim_matches('"'),
                data["name"].to_string().trim_matches('"'),
                data["phone"].to_string().trim_matches('"'),
                data["default"],
                data["id"].to_string().trim_matches('"')
            )
        };

        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 删除客户地址
#[post("/del_customer_address")]
pub async fn del_customer_address(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(r#"delete from address WHERE id={data}"#);
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取指定 id 的供应商和客户
#[post("/fetch_supplier")]
pub async fn fetch_supplier(
    db: web::Data<Pool>,
    supplier: web::Json<Customer>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let fields = get_inout_fields(db.clone(), &supplier.cate).await;

        let mut sql = "SELECT 信用评价,优惠折扣,".to_owned();
        for f in &fields {
            sql += &format!("{},", f.field_name);
        }

        sql = sql.trim_end_matches(",").to_owned();

        sql += &format!(r#" FROM customers WHERE id={}"#, supplier.id);
        let conn = db.get().await.unwrap();
        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut supplier = "".to_owned();
        let mut trust = "".to_owned();
        let mut sale_cut = 1f32;
        for row in rows {
            supplier += &simple_string_from_base(row, &fields);
            trust = row.get("信用评价");
            sale_cut = row.get("优惠折扣");
        }
        HttpResponse::Ok().json((fields, supplier, trust, sale_cut))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///出入库获取客户供应商信息
#[post("/fetch_inout_customer")]
pub async fn fetch_inout_customer(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();
        let sort = &post_data.sort;
        let rec = post_data.rec;
        let cate = &post_data.cate;

        let fields = get_inout_fields(db.clone(), &post_data.cate).await;

        let mut sql_fields = "SELECT id::text,".to_owned();

        for f in &fields {
            sql_fields += &format!("{},", f.field_name);
        }

        let sql = format!(
            r#"{sql_fields} ROW_NUMBER () OVER (ORDER BY {sort}) as 序号 FROM customers WHERE 
            类别='{cate}' AND LOWER(名称) LIKE '%{name}%' AND 停用=false ORDER BY {sort} OFFSET {skip} LIMIT {rec}"#
        );

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let products = build_string_from_base(rows, fields);

        let count_sql = format!(
            r#"SELECT count(id) as 记录数 FROM customers WHERE 类别='{cate}' AND LOWER(名称) LIKE '%{name}%' AND 停用=false"#
        );

        let (pages, count) = pages(&conn, count_sql, rec).await;
        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取客户
#[post("/fetch_visit")]
pub async fn fetch_visit(
    db: web::Data<Pool>,
    post_data: web::Json<TablePager>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let skip = (post_data.page - 1) * post_data.rec;
        let name = post_data.name.to_lowercase();

        let sql = format!(
            r#"
            select
                max(username) username, max(名称) 名称, to_char(max(last_visit), 'YYYY年MM月DD日 HH24:MI:SS') 最近查询,	
                SUM(case when visit_date::DATE = CURRENT_DATE then num else 0 end) as 今日查询,
                SUM(case when visit_date::DATE >= CURRENT_DATE - interval '1 month' then num else 0 end) as 近一月查询,
                SUM(case when visit_date::DATE >= CURRENT_DATE - interval '1 year' then num else 0 end) as 近一年查询,
                ROW_NUMBER () OVER (ORDER BY {}) 序号 
            from visits
            join customers on visits.user_id = customers.id
            where LOWER(名称) like '%{}%'
            group by user_id
            ORDER BY {} OFFSET {} LIMIT {}"#,
            post_data.sort, name, post_data.sort, skip, post_data.rec
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        let mut products = Vec::new();
        for row in rows {
            let mut product = "".to_owned();
            product += &format!("{}{}", row.get::<&str, i64>("序号"), SPLITER);
            product += &format!("{}{}", row.get::<&str, &str>("username"), SPLITER);
            product += &format!("{}{}", row.get::<&str, &str>("名称"), SPLITER);
            product += &format!("{}{}", row.get::<&str, &str>("最近查询"), SPLITER);
            product += &format!("{}{}", row.get::<&str, i64>("今日查询"), SPLITER);
            product += &format!("{}{}", row.get::<&str, i64>("近一月查询"), SPLITER);
            product += &format!("{}{}", row.get::<&str, i64>("近一年查询"), SPLITER);
            products.push(product);
        }

        let count_sql = format!(
            r#"SELECT COUNT(DISTINCT user_id) as 记录数 FROM visits
            join customers on visits.user_id = customers.id
            where LOWER(名称) like '%{name}%'"#,
        );

        let rows = &conn.query(count_sql.as_str(), &[]).await.unwrap();

        let mut count: i64 = 0;
        for row in rows {
            count = row.get("记录数");
        }
        let pages = (count as f64 / post_data.rec as f64).ceil() as i32;
        HttpResponse::Ok().json((products, count, pages))
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取公告信息
#[get("/fetch_information")]
pub async fn fetch_information(db: web::Data<Pool>) -> HttpResponse {
    let conn = db.get().await.unwrap();
    let sql = format!(
        r#"select title as 标题, content as 内容, show 
            from information where id=1"#,
    );

    let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

    if rows.len() == 0 {
        return HttpResponse::Ok().json(-1);
    }

    let title: String = rows[0].get("标题");
    let content: String = rows[0].get("内容");
    let show: bool = rows[0].get("show");

    let data = json!({
        "title": title,
        "content": content,
        "show": show
    });

    HttpResponse::Ok().json(data)
}

///保存公告
#[post("/save_information")]
pub async fn save_information(
    db: web::Data<Pool>,
    data: web::Json<Value>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"UPDATE information SET title='{}', content='{}', show={} WHERE id=1"#,
            data["title"].to_string().trim_matches('"'),
            data["content"].to_string().trim_matches('"'),
            data["show"]
        );

        let _ = &conn.query(sql.as_str(), &[]).await.unwrap();
        return HttpResponse::Ok().json(1);
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///重置客户密码
#[post("/reset_customer_password")]
pub async fn reset_customer_password(
    db: web::Data<Pool>,
    data: web::Json<i32>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"UPDATE customers SET failed=0, password='6b54cef8ddfee032435c0ebaefbf8349' WHERE id={}"#,
            data
        );

        let _ = &conn.query(sql.as_str(), &[]).await.unwrap();
        return HttpResponse::Ok().json(1);
    } else {
        HttpResponse::Ok().json(-1)
    }
}
