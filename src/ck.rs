use crate::service::*;
use actix_identity::Identity;
use actix_web::{get, post, web, HttpResponse};
use deadpool_postgres::Pool;
use serde_json::json;

#[get("/materialout_auto")]
pub async fn materialout_auto(
    db: web::Data<Pool>,
    search: web::Query<SearchCate>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        let f_map2 = map_fields(&db, "客户").await;
        let s = search.s.to_uppercase();
        let cate_s = if search.cate != "" {
            format!("documents.类别='{}' AND ", search.cate)
        } else {
            "".to_string()
        };
        let sql = &format!(
            r#"SELECT 单号 as id, 单号 || '　' || customers.{} AS label FROM documents
            JOIN customers on 客商id = customers.id
            WHERE {} 单号 like '%{}%' AND documents.{}=false AND documents.{} <> '' {} LIMIT 10"#,
            f_map2["简称"], cate_s, s, f_map["发货完成"], f_map["审核"], NOT_DEL_SQL
        );

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 出库单获得销售单据
#[post("/materialsale_docs")]
pub async fn materialsale_docs(
    db: web::Data<Pool>,
    search: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "销售出库".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        // let f_map2 = map_fields(&db, "出库单据").await;
        let f_map3 = map_fields(&db, "客户").await;

        let sql = &format!(
            r#"SELECT 单号 as id, 单号 || '　' || customers.{} AS label FROM documents
            join customers on 客商id = customers.id
            WHERE documents.类别='{search}' AND documents.{} <> '' AND documents.{} = false and
            单号 not in (select 文本字段6 from documents where 类别='销售出库' and 文本字段10 = '' {NOT_DEL_SQL})
            {NOT_DEL_SQL}
            order by 单号 desc"#,
            f_map3["简称"], f_map["审核"], f_map["出库完成"]
        );

        // println!("{}",sql);

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 出库单获得销售单据
#[post("/materialsale_saved_docs")]
pub async fn materialsale_saved_docs(
    db: web::Data<Pool>,
    search: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "销售出库".to_owned()).await;
    if user.name != "" {
        let f_map = map_fields(&db, "销售单据").await;
        // let f_map2 = map_fields(&db, "出库单据").await;
        let f_map3 = map_fields(&db, "客户").await;

        let sql = &format!(
            r#"SELECT 出库单号 as id, 单号 || '　' || customers.{} || '　' || 出库单.经办人 AS label
            FROM documents
            join customers on 客商id = customers.id
            join 
            (select 单号 出库单号, 文本字段6, 经办人 from documents where documents.类别='销售出库' and 
            布尔字段3 = false and 文本字段10 = '' {NOT_DEL_SQL}) as 出库单
            on 出库单.文本字段6 = documents.单号
            WHERE documents.类别='{search}' AND documents.{} <> '' AND documents.{} = false {NOT_DEL_SQL}
            order by 单号 desc"#,
            f_map3["简称"], f_map["审核"], f_map["出库完成"]
        );

        // println!("{}",sql);

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/material_auto_out")]
pub async fn material_auto_out(
    db: web::Data<Pool>,
    search: web::Query<SearchPlus>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "商品规格").await;
        let ss: Vec<&str> = search.ss.split('　').collect();
        let ss2 = ss[2].replace(SPLITER, "+"); //加号传不过来

        let sql = &format!(
            r#"SELECT num as id, products.物料号 || '{SPLITER}' || split_part(node_name,' ',2) || '{SPLITER}' ||
                split_part(node_name,' ',1) || '{SPLITER}' || products.{} || '{SPLITER}' || products.{} || '{SPLITER}' ||
                products.{} || '{SPLITER}'|| (products.{}-COALESCE(长度合计,0)-COALESCE(切分次数,0)*2)::integer  label
                FROM products
                JOIN tree ON products.商品id = tree.num
                JOIN documents ON 单号id = 单号
                LEFT JOIN cut_length() as foo
                ON products.物料号 = foo.物料号
                WHERE LOWER(products.{}) LIKE LOWER('%{}%') AND num='{}' AND 
                products.{} = '{}' and products.{} = '{}' and
                (products.{}-COALESCE(长度合计,0)-COALESCE(切分次数,0)*2)::integer + 10 >= {} AND
                products.{} != '是' AND documents.文本字段10 <> '' {NOT_DEL_SQL}
                order by products.{}-COALESCE(长度合计,0)-COALESCE(切分次数,0)*2"#,
            f_map["规格"],
            f_map["状态"],
            f_map["炉批号"],
            f_map["库存长度"],
            f_map["物料号"],
            search.s,
            ss[0],
            f_map["规格"],
            ss[1].trim(),
            f_map["状态"],
            ss2.trim(),
            f_map["库存长度"],
            ss[3],
            f_map["切完"],
            f_map["库存长度"],
        );

        // println!("{}", sql);

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[get("/material_auto_sotckout")]
pub async fn material_auto_sotckout(
    db: web::Data<Pool>,
    search: web::Query<Search>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let f_map = map_fields(&db, "商品规格").await;
        let sql = &format!(
            r#"SELECT num as id, products.物料号 || '{SPLITER}' || split_part(node_name,' ',2) || '{SPLITER}' ||
                split_part(node_name,' ',1) || '{SPLITER}' || products.{} || '{SPLITER}' || products.{} || '{SPLITER}' ||
                products.{} || '{SPLITER}' || products.{} || '{SPLITER}' || products.{} || '{SPLITER}' || 库存长度 label
                FROM products
                JOIN tree ON products.商品id = tree.num
                JOIN documents ON 单号id = 单号
                LEFT JOIN length_weight() foo ON products.物料号 = foo.物料号
                WHERE LOWER(products.物料号) LIKE '%{}%' AND documents.文本字段10 <> '' {NOT_DEL_SQL} LIMIT 10"#,
            f_map["规格"],
            f_map["状态"],
            f_map["执行标准"],
            f_map["炉批号"],
            f_map["生产厂家"],
            search.s,
        );

        // println!("{}", sql);

        autocomplete(db, sql).await
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取出库单据字段
#[post("/fetch_document_ck")]
pub async fn fetch_document_ck(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let fields = get_inout_fields(db.clone(), &data.cate).await;
        let f_map = map_fields(&db, &data.cate).await;
        let mut sql_fields = "SELECT ".to_owned();

        for f in &fields {
            sql_fields += &format!("documents.{},", f.field_name);
        }

        let sql = format!(
            r#"{} 作废, documents.{} as 审核, 经办人, customers.id, documents.{} as 图片, 
                    documents.{} as 提交审核 FROM documents
                JOIN customers ON documents.客商id=customers.id WHERE 单号='{}'"#,
            sql_fields, f_map["审核"], f_map["图片"], f_map["提交审核"], data.dh
        );

        // println!("{}", sql);

        let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();

        let f_str = simple_string_from_base(row, &fields);
        let fds = f_str.split(SPLITER).collect::<Vec<&str>>();

        if data.cate.contains("出库") {
            let document = json!({
                "销售单号": fds[0],
                "合同编号": fds[1],
                "客户": fds[2],
                "日期": fds[3],
                "备注": fds[4],
                "图片": row.get::<&str, String>("图片"),
                "提交审核": row.get::<&str, bool>("提交审核"),
                "客户id": row.get::<&str, i32>("id"),
                "审核": row.get::<&str, String>("审核"),
                "经办人": row.get::<&str, String>("经办人"),
                "作废": row.get::<&str, bool>("作废"),
            });

            HttpResponse::Ok().json(document)
        } else {
            let document = json!({
                "销售单号": fds[0],
                "合同编号": fds[1],
                "客户名称": fds[2],
                "单据金额": fds[3],
                "是否欠款": fds[4],
                "开票日期": fds[5],
                "发票金额": fds[6],
                "发票号": fds[7],
                "备注": fds[8],
                "图片": row.get::<&str, String>("图片"),
                "提交审核": row.get::<&str, bool>("提交审核"),
                "客户id": row.get::<&str, i32>("id"),
                "审核": row.get::<&str, String>("审核"),
                "经办人": row.get::<&str, String>("经办人"),
                "作废": row.get::<&str, bool>("作废"),
            });

            HttpResponse::Ok().json(document)
        }
    } else {
        return HttpResponse::Ok().json(-1);
    }
}

// 获取出库条目，用于出库单
#[post("/get_items_out")]
pub async fn get_items_out(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let dh = data.split('#').collect::<Vec<&str>>()[0];
        let sql = &format!(
            r#"SELECT di.物料号 || '　' || p.文本字段4  || '{SPLITER}' || split_part(node_name,' ',2) || '　' ||
                split_part(node_name,' ',1) || '　' || p.规格型号 || '　' || p.文本字段2 || '　' ||
                长度 || '　' || 数量 || '　' || di.备注 || '　' || 库位 || '{SPLITER}' || 数量 - coalesce(pi.已出, 0)
                || '{SPLITER}' || id as item
            from sale_items di
            LEFT JOIN (
                select 销售id, sum(数量) 已出 from pout_items pi
                where 销售id like '{dh}%' and not_fei(单号id)
                group by 销售id
            ) pi on pi.销售id = di.id
            JOIN products p on p.物料号 = di.物料号
            JOIN tree ON p.商品id = tree.num
            WHERE di.单号id = '{dh}' and di.物料号 <> '锯口费'
            order by 顺序"#,
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let item = row.get("item");
            document_items.push(item);
        }
        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/get_docs_out")]
pub async fn get_docs_out(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "销售单据").await;
        let dh = data.split('#').collect::<Vec<&str>>()[0];
        let sql = &format!(
            r#"SELECT documents.{} as 合同编号,名称, 客商id, documents.备注 from documents
            JOIN customers ON 客商id = customers.id
            WHERE 单号 = '{dh}' {NOT_DEL_SQL}"#,
            f_map["合同编号"]
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut item = "".to_owned();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("合同编号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, i32>("客商id").to_string(),
                row.get::<&str, String>("备注"),
            ];

            item = sp_query(fields);
        }
        HttpResponse::Ok().json(item)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///保存出库单据
#[post("/save_material_ck")]
pub async fn save_material_ck(
    db: web::Data<Pool>,
    data: web::Json<Document>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id.clone(), "".to_owned()).await;
    if user.name != "" {
        let mut conn = db.get().await.unwrap();
        // let conn2 = db.get().await.unwrap();
        let doc_data: Vec<&str> = data.document.split(SPLITER).collect();
        let mut doc_sql;

        let fields_cate = if data.rights == "销售出库" {
            "出库单据"
        } else {
            "库存调出"
        };

        let f_map = map_fields(&db, fields_cate).await;

        let fields = get_inout_fields(db.clone(), fields_cate).await;
        let mut dh = doc_data[1].to_owned();

        if dh == "新单据" {
            dh = get_dh(db.clone(), doc_data[0]).await;

            let mut init = "INSERT INTO documents (单号, 客商id,".to_owned();
            for f in &fields {
                init += &format!("{},", &*f.field_name);
            }

            init += &format!(
                "类别,{},{}) VALUES('{}', {},",
                f_map["经办人"], f_map["区域"], dh, doc_data[2]
            );

            doc_sql = build_sql_for_insert(&doc_data, init, &fields, 3);
            doc_sql += &format!("'{}','{}', '{}')", doc_data[0], user.name, user.area);
        } else {
            let init = "UPDATE documents SET ".to_owned();
            doc_sql = build_sql_for_update(doc_data.clone(), init, fields, 3);
            doc_sql += &format!(
                "客商id={}, 类别='{}', {}='{}', {}='{}' WHERE 单号='{}'",
                doc_data[2], doc_data[0], f_map["经办人"], user.name, f_map["区域"], user.area, dh
            );
        }

        // println!("{}", doc_sql);

        let transaction = conn.transaction().await.unwrap();
        transaction.execute(doc_sql.as_str(), &[]).await.unwrap();

        // 单据明细
        if dh != "新单据" && fields_cate == "出库单据" {
            transaction
                .execute("DELETE FROM pout_items WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        } else if dh != "新单据" {
            transaction
                .execute("DELETE FROM tc_items WHERE 单号id=$1", &[&dh])
                .await
                .unwrap();
        }

        for item in &data.items {
            let value: Vec<&str> = item.split(SPLITER).collect();

            let id = format!("{}-{}", dh, value[0]);
            let items_sql = if fields_cate == "出库单据" {
                format!(
                    r#"INSERT INTO pout_items (id, 单号id, 数量, 重量, 理重, 备注, 顺序, 销售id)
                     VALUES('{}', '{}', {}, {}, {}, '{}', {}, '{}')"#,
                    id, dh, value[1], value[2], value[3], value[4], value[0], value[5]
                )
            } else {
                format!(
                    r#"INSERT INTO tc_items (id, 单号id, 物料号, 长度, 理重, 备注, 顺序)
                     VALUES('{}', '{}', '{}', {}, {}, '{}', {})"#,
                    id, dh, value[1], value[2], value[3], value[4], value[0]
                )
            };

            transaction.execute(items_sql.as_str(), &[]).await.unwrap();
        }

        let _result = transaction.commit().await;

        HttpResponse::Ok().json(dh)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取出库单据明细 - 销售出库
#[post("/fetch_document_items_ck")]
pub async fn fetch_document_items_ck(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "商品规格").await;
        let sql = format!(
            r#"select split_part(node_name,' ', 2) 名称, split_part(node_name,' ', 1) 材质,
                    {} 规格, {} 状态, {} 炉号, di.长度, pi.数量, (di.长度*pi.数量)::integer as 总长度,
                    di.物料号, pi.重量, pi.理重, 库位, pi.备注, 销售id
                FROM pout_items pi
                join sale_items di on di.id = pi.销售id
                JOIN products ON products.物料号 = di.物料号
                JOIN tree ON 商品id = tree.num
                WHERE pi.单号id = '{}' ORDER BY pi.顺序"#,
            f_map["规格"], f_map["状态"], f_map["炉批号"], data.dh
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();
        for row in rows {
            let fields = vec![
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, String>("炉号"),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, i32>("数量").to_string(),
                row.get::<&str, i32>("总长度").to_string(),
                row.get::<&str, String>("物料号"),
                row.get::<&str, f32>("重量").to_string(),
                row.get::<&str, f32>("理重").to_string(),
                row.get::<&str, String>("库位"),
                row.get::<&str, String>("备注"),
                "checked".to_string(),
                row.get::<&str, String>("销售id"),
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///获取出库单据明细 - 调整出库
#[post("/fetch_document_items_tc")]
pub async fn fetch_document_items_tc(
    db: web::Data<Pool>,
    data: web::Json<DocumentDh>,
    id: Identity,
) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "商品规格").await;
        let sql = format!(
            r#"select dt.物料号, split_part(node_name,' ', 2) 名称, split_part(node_name,' ', 1) 材质,
                    {} 规格, {} 状态, 长度, 理重, dt.备注
                FROM tc_items dt
                JOIN products ON products.物料号 = dt.物料号
                JOIN tree ON 商品id = tree.num
                WHERE dt.单号id = '{}'
                ORDER BY 顺序"#,
            f_map["规格"], f_map["状态"], data.dh
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut document_items: Vec<String> = Vec::new();

        for row in rows {
            let fields = vec![
                row.get::<&str, String>("物料号"),
                row.get::<&str, String>("名称"),
                row.get::<&str, String>("材质"),
                row.get::<&str, String>("规格"),
                row.get::<&str, String>("状态"),
                row.get::<&str, i32>("长度").to_string(),
                row.get::<&str, f32>("理重").to_string(),
                row.get::<&str, String>("备注"),
            ];

            document_items.push(sp_query(fields));
        }

        HttpResponse::Ok().json(document_items)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

#[post("/make_formal_out")]
pub async fn make_formal_out(
    db: web::Data<Pool>,
    data: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    // let user_name = id.identity().unwrap_or("".to_owned());
    let user = get_user(&db, id, "单据审核".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();
        let f_map = map_fields(&db, "出库单据").await;
        let sql = format!(
            r#"update documents set {}='{}' WHERE 单号='{}'"#,
            f_map["审核"], user.name, data
        );
        let _rows = &conn.query(sql.as_str(), &[]).await.unwrap();

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

// 确认出库完成
#[post("/make_ck_complete")]
pub async fn make_ck_complete(
    db: web::Data<Pool>,
    dh: web::Json<String>,
    id: Identity,
) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let conn = db.get().await.unwrap();

        let sql = format!(
            r#"update documents set 布尔字段2 = true where 单号 ='{dh}' and
                (select sum(数量) from sale_items where 单号id ='{dh}' and 物料号 <> '锯口费') =
                (select sum(数量) from pout_items where 销售id like '{dh}%' and not_fei(单号id))"#,
        );

        let _ = conn.query(sql.as_str(), &[]).await;

        HttpResponse::Ok().json(1)
    } else {
        HttpResponse::Ok().json(-1)
    }
}

///打印标签时获取出库单据字段
#[post("/get_material_tag")]
pub async fn get_material_tag(db: web::Data<Pool>, data: String, id: Identity) -> HttpResponse {
    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let sql = format!(
            r#"select 销售id id, p.文本字段3 标准, p.文本字段5 厂家 from pout_items pi 
                join sale_items si on si.id = pi.销售id 
                join products p on si.物料号 = p.物料号
                where pi.单号id = '{data}'
                order by pi.顺序"#,
        );

        // println!("{}", sql);

        let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
        let mut tags = Vec::new();
        for row in rows {
            let document = json!({
                "d_id": row.get::<&str, String>("id"),
                "标准": row.get::<&str, String>("标准"),
                "厂家": row.get::<&str, String>("厂家"),
            });

            tags.push(document);
        }

        HttpResponse::Ok().json(tags)
    } else {
        HttpResponse::Ok().json(-1)
    }
}
