#!/bin/sh

# 炉号
pg_dump wx_sales2 -t lu -f lu.sql
psql sales -c 'drop table lu'
psql sales < lu.sql

# 客户
pg_dump wx_sales2 -t customers -f customers.sql
psql sales -c 'drop table customers CASCADE'
psql sales < customers.sql

psql sales -c "insert into public.tableset
(id, table_name, field_name, data_type, show_name, show_width, ctr_type, option_value, is_show, show_order, inout_show, inout_order, default_value, all_edit, is_use, inout_width)
values 
(369,'客户','username','文本','用户名',4,'普通输入','',false,29,false,27,'',true,false,4)"

# 访问记录, 在恢复完 customers 后运行
pg_dump wx_sales2 -t visits -f visits.sql
psql sales -c 'drop table visits'
psql sales < visits.sql

# tree
pg_dump wx_sales2 -t tree -f tree.sql
psql sales -c 'drop table tree CASCADE'
psql sales < tree.sql

# users
pg_dump wx_sales2 -t users -f users.sql
psql sales -c 'drop table users CASCADE'
psql sales < users.sql

# kp_items, 恢复完 documents 后运行
# pg_dump wx_sales2 -t kp_items -f kp_items.sql
# psql sales -c 'drop table kp_items CASCADE'
# psql sales < kp_items.sql