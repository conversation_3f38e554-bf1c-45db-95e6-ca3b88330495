
ALTER TABLE documents ADD COLUMN 作废 bool default false;

--
-- Name: documents buy_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.documents
    ADD CONSTRAINT buy_documents_pkey PRIMARY KEY ("单号");


--
-- Name: documents_客商id_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "documents_客商id_idx" ON public.documents USING btree ("客商id");


--
-- Name: documents_日期_idx; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "documents_日期_idx" ON public.documents USING btree ("日期");


--
-- Name: idx_documents_文本字段6; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "idx_documents_文本字段6" ON public.documents USING btree ("文本字段6");


--
-- Name: idx_documents_类别; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "idx_documents_类别" ON public.documents USING btree ("类别");


--
-- PostgreSQL database dump complete
--

