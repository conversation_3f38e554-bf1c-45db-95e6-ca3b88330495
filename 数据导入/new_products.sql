ALTER TABLE products ADD COLUMN 库存状态 text default ''::text;
ALTER TABLE products ADD COLUMN 入库id text default ''::text;
ALTER TABLE public.products RENAME COLUMN 文本字段1 TO 物料号;
update products set 库存状态='已切完' where 文本字段7 = '是';
update products set 文本字段8='';
update products set 规格型号='--', 文本字段2 = '--', 文本字段4 = '--' where 物料号 = '锯口费';

-- 创建索引

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pk PRIMARY KEY ("物料号");


--
-- Name: idx_products_入库id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "idx_products_入库id" ON public.products USING btree ("入库id");


--
-- Name: idx_products_单号id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "idx_products_单号id" ON public.products USING btree ("单号id");


--
-- Name: idx_products_商品id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX "idx_products_商品id" ON public.products USING btree ("商品id");


--
-- Name: products products_单号id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT "products_单号id_fkey" FOREIGN KEY ("单号id") REFERENCES public.documents("单号");


--
-- PostgreSQL database dump complete
--

