一、不作处理的表: 
    1. table_set

三、直接导入的表:
    1. lu
    2. customers
    3. visits
    4. tree
    5. users
    6. kp_items(需在 documents 表导入后进行)
    运行 restore.sh

三、先通过备份导入, 然后修改设置
导入顺序: documents -> products:
(一) 导入 dcouments 步骤:
    1. pg_dump wx_sales2 -t documents -f documents.sql
    2. 删除 documents.sql 中的索引语句
    3. 运行:
        psql sales -c 'drop table documents CASCADE'
        psql sales < documents.sql

    4. 运行 psql sales < new_documents.sql

    5. 此时可导入 kp_items 表

(二) 导入 products 步骤:
    1. pg_dump wx_sales2 -t products -f products.sql
    2. 将 products.sql 的索引语句删除
    3. 运行如下语句:
        psql sales -c 'drop table products CASCADE'
        psql sales < products.sql

    4. 运行 psql sales < new_products.sql

四、导入为临时表, 然后通过 insert select 进行导入
    首先导入 document_items 为临时表:
    pg_dump wx_sales2 -t document_items -f document_items.sql
    psql sales < document_items.sql

    pg_dump wx_sales2 -t pout_items -f pout_items_old.sql
    这里要修改 pout_items_old.sql 中所建表格名称替换为 pout_items_old
    psql sales < pout_items_old.sql

    创建自增序列,用于 id 自增, 否则冲突太多
    DROP SEQUENCE id_seq;
    CREATE SEQUENCE id_seq;

(一) sale_items 销售数据 (退货数据在后面导入)
    目标: 从旧的数据库中, 拼凑出符合要求的数据
    1. 从出库数据中拼凑出符合要求的数据, 仅出库中才有相关物料号数据

        truncate table sale_items

        alter table sale_items add column uid text;   // 加入 uid 临时字段, 用于与出库数据一一对应, 方便出库数据导入

        INSERT INTO sale_items (id, 单号id, 物料号, 单价, 数量, 长度, 理重, 重量, 金额, 类型, 备注, 顺序, uid)
        select 文本字段6 || '-' || nextval('id_seq') id, 文本字段6 单号id, 物料号, 单价, 数量, 长度, 理重, 重量, 
            单价*理重 金额, '按重量' 类型, '' 备注, 顺序, id
        from pout_items_old pi2 
        join documents d on pi2.单号id = d.单号 
        where 文本字段6 like 'XS%' or 文本字段6 like 'XT%'

    2. 从明细表中取出"锯口费"作为物料号的数据
        INSERT INTO sale_items (id, 单号id, 物料号, 单价, 数量, 长度, 理重, 重量, 金额, 类型, 备注, 顺序)
        select 单号id || '-' || nextval('id_seq') id, 单号id, '锯口费' 物料号, 单价, 数量, 长度, 理重, 重量, 
            单价*数量 金额, '按件' 类型, 备注, 9999 顺序
        from document_items where 单号id like 'XS%' and 商品id = '4_111'

(二) 从 pout_items 导入出库数据

    truncate table pout_items

    1. 销售出库

    INSERT INTO pout_items (id, 单号id, 销售id, 数量, 理重, 重量, 备注, 顺序)
    select pi2.单号id || '-' || pi2.顺序 id, pi2.单号id, si.id, pi2.数量, pi2.理重, pi2.重量, pi2.备注, pi2.顺序  
    from pout_items_old pi2 
    join documents d on pi2.单号id = d.单号 
    join sale_items si on si.uid = pi2.id::text

    2. 调整出库

    truncate table tc_items

    INSERT INTO tc_items (id, 单号id, 物料号, 长度, 理重, 备注, 顺序)
    select 单号id || '-' || 顺序 id, 单号id, 物料号, 长度, 理重, 备注, 顺序 
    from pout_items_old
    where 单号id like 'TC%'


(三) fh_items 发货数据

    -- 清空表并去除字段空格
    truncate table fh_items
    update documents set 文本字段6 = trim(文本字段6)

    -- 可正常导入的数据
    INSERT INTO fh_items (id, 单号id, 备注, 顺序, 金额, 出库id)
    select di.单号id || '-' || di.顺序 id, di.单号id, di.备注, di.顺序, 
        case when 商品id = '4_111' then 单价*数量 else 单价*di.重量 end 金额,
        case when 商品id = '4_111' then '锯口费' else pi.id end 出库id
    from document_items di 
    join documents d on d.单号 = di.单号id 
    left join 
        (select pi.id, si.单号id, pi.顺序,pi.重量 from pout_items pi
        join sale_items si on pi.销售id = si.id) pi
    on  pi.单号id = 文本字段6 and di.顺序 = pi.顺序 and di.重量=pi.重量
    where di.单号id like 'FH%'and pi.id is not null and di.单号id <> 'FH202312-15'

    -- 导入锯口费
    INSERT INTO fh_items (id, 单号id, 备注, 顺序, 金额, 出库id)
    select di.单号id || '-' || di.顺序 id, di.单号id, di.备注, di.顺序, 
        case when 商品id = '4_111' then 单价*数量 else 单价*di.重量 end 金额,
        case when 商品id = '4_111' then '锯口费' else pi.id end 出库id
    from document_items di 
    join documents d on d.单号 = di.单号id 
    left join 
        (select pi.id, si.单号id, pi.顺序,pi.重量 from pout_items pi
        join sale_items si on pi.销售id = si.id) pi
    on  pi.单号id = 文本字段6 and di.顺序 = pi.顺序 and di.重量=pi.重量
    where di.单号id like 'FH%' and 商品id = '4_111'

    -- 无法正常导入有问题的记录, 逐个分析, 能导入的尽量导入
    select di.单号id || '-' || di.顺序 id, di.单号id, di.备注, di.顺序, 
        case when 商品id = '4_111' then 单价*数量 else 单价*di.重量 end 金额,
        case when 商品id = '4_111' then '锯口费' else pi.id end 出库id
    from document_items di 
    join documents d on d.单号 = di.单号id 
    left join 
        (select pi.id, si.单号id, pi.顺序,pi.重量 from pout_items pi
        join sale_items si on pi.销售id = si.id) pi
    on  pi.单号id = 文本字段6 and di.顺序 = pi.顺序 and di.重量=pi.重量
    --where di.单号id like 'FH%' and 商品id = '4_111'
    where di.单号id like 'FH%'and pi.id is null and 商品id <> '4_111'

(四) buy_itmes 采购数据

    truncate table buy_items

    INSERT INTO buy_items (id, 单号id, 商品id, 规格, 状态, 执行标准, 单价, 重量, 金额, 长度, 顺序, 备注)
    select 单号id || '-' || 顺序, 单号id, 商品id, 规格, 状态, 执行标准, 单价, 重量, 单价*重量 金额, 长度, 顺序, 备注
    from document_items di 
    where 单号id like 'CG%'

五、检查索引
六、清理数据
    DROP SEQUENCE id_seq;
    alter table sale_items drop column uid;
    drop table pout_items_old;
    drop table document_items;