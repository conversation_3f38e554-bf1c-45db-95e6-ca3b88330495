use actix_web::middleware::Logger;
use actix_web::{App, HttpServer};

use crate::registry::ModuleRegistry;

pub async fn run_http(registry: ModuleRegistry) -> std::io::Result<()> {
    registry.start();
    let registry = actix_web::web::Data::new(registry);

    println!("服务已启动：http://localhost:8080");

    HttpServer::new(move || {
        App::new()
            .app_data(registry.clone())
            .wrap(Logger::default())
            .configure(|cfg| registry.configure_routes(cfg))
    })
    .bind(("0.0.0.0", 8080))?
    .run()
    .await
}
