#[derive(<PERSON><PERSON>, <PERSON>bug)]
pub struct Permission {
    pub key: &'static str,     // e.g. "sales:order:create"
    pub label: &'static str,   // i18n key or plain text
}

#[derive(<PERSON><PERSON>, Debug)]
pub struct MenuItem {
    pub key: &'static str,
    pub label: &'static str,
    pub path: &'static str,
    pub icon: Option<&'static str>,
    pub required_permissions: &'static [&'static str],
}

#[derive(<PERSON><PERSON>, Debug)]
pub struct Migration {
    pub name: &'static str,
    pub up_sql: Option<&'static str>,
    pub down_sql: Option<&'static str>,
}
