use actix_web::web::ServiceConfig;
use std::sync::Arc;

use crate::{context::AppContext, Module};

pub struct ModuleRegistry {
    modules: Vec<Arc<dyn Module>>, // 运行期已实例化模块
    pub ctx: AppContext,
}

impl ModuleRegistry {
    pub fn new() -> Self {
        Self { modules: Vec::new(), ctx: AppContext::new() }
    }

    pub fn with_context(ctx: AppContext) -> Self {
        Self { modules: Vec::new(), ctx }
    }

    pub fn register<M: Module + 'static>(&mut self, module: M) {
        let m: Arc<dyn Module> = Arc::new(module);
        // 生命周期：注册服务
        let mut services = self.ctx.services.clone();
        m.register_services(&mut services);
        self.modules.push(m);
    }

    pub fn start(&self) {
        for m in &self.modules {
            m.on_start(&self.ctx);
        }
    }

    pub fn stop(&self) {
        for m in &self.modules {
            m.on_stop();
        }
    }

    pub fn configure_routes(&self, cfg: &mut ServiceConfig) {
        for m in &self.modules {
            m.configure_routes(cfg);
        }
    }
}
