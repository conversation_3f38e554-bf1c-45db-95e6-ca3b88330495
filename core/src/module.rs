use actix_web::web::ServiceConfig;
use semver::{Version, VersionReq};

use crate::context::AppContext;
use crate::types::{MenuItem, Migration, Permission};

#[derive(Clone, Debug)]
pub struct ModuleDependency {
    pub name: &'static str,
    pub min_version: VersionReq,
}

pub trait Module: Send + Sync {
    fn name(&self) -> &'static str;
    fn version(&self) -> Version { Version::new(0, 1, 0) }
    fn dependencies(&self) -> &'static [ModuleDependency] { &[] }

    fn register_services(&self, _c: &mut crate::context::ServiceContainer) {}
    fn configure_routes(&self, _cfg: &mut ServiceConfig) {}
    fn permissions(&self) -> &'static [Permission] { &[] }
    fn menu(&self) -> &'static [MenuItem] { &[] }
    fn migrations(&self) -> &'static [Migration] { &[] }

    fn on_start(&self, _ctx: &AppContext) {}
    fn on_stop(&self) {}
}
