use std::any::{Any, TypeId};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

// 存储为 Box<dyn Any>，内部装 Arc<T>，获取时 downcast_ref::<Arc<T>>() 后 clone
#[derive(<PERSON><PERSON>, Default)]
pub struct ServiceContainer(Arc<RwLock<HashMap<TypeId, Box<dyn Any + Send + Sync>>>>);

impl ServiceContainer {
    pub fn new() -> Self { Self::default() }

    pub fn insert<T: 'static + Send + Sync>(&mut self, svc: Arc<T>) {
        self.0
            .write()
            .unwrap()
            .insert(TypeId::of::<T>(), Box::new(svc));
    }

    pub fn get<T: 'static + Send + Sync>(&self) -> Option<Arc<T>> {
        let guard = self.0.read().ok()?;
        guard
            .get(&TypeId::of::<T>())
            .and_then(|b| b.downcast_ref::<Arc<T>>())
            .map(Arc::clone)
    }
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct AppContext {
    pub services: ServiceContainer,
    // 将来可加入：db pool、cache、tenant、config、tracing 等
}

impl AppContext {
    pub fn new() -> Self { Self::default() }
}
