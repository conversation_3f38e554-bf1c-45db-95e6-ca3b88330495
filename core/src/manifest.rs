use serde::Deserialize;
use std::{fs, path::Path};

#[derive(Debug, Deserialize, Clone)]
pub struct ModuleManifest {
    pub name: String,
    pub version: String,
    pub display_name: Option<String>,
    pub description: Option<String>,
    #[serde(default)]
    pub dependencies: Vec<String>, // e.g. ["base>=0.1.0"]
}

pub fn load_manifests<P: AsRef<Path>>(modules_root: P) -> std::io::Result<Vec<ModuleManifest>> {
    let mut out = Vec::new();
    let root = modules_root.as_ref();
    if !root.exists() { return Ok(out); }
    for entry in fs::read_dir(root)? {
        let entry = entry?;
        if entry.file_type()?.is_dir() {
            let manifest_path = entry.path().join("module.toml");
            if manifest_path.exists() {
                let s = fs::read_to_string(&manifest_path)?;
                let m: ModuleManifest = toml::from_str(&s)
                    .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, format!("toml parse error: {}", e)))?;
                out.push(m);
            }
        }
    }
    Ok(out)
}
