// 占位的 ructe 模板聚合构建脚本（示意）。
// 后续可启用 ructe，递归扫描 modules/*/templates，统一生成模板绑定代码。

fn main() {
    // 1) 监听模块模板路径变化以触发重建（示意）
    println!("cargo:rerun-if-changed=modules");
    println!("cargo:rerun-if-changed=templates");

    // 2) 如需启用 ructe：
    // use ructe::{Ructe, RucteError};
    // let mut r = Ructe::from_env().expect("ructe");
    // r.compile_templates("templates").unwrap();
    // 也可以自定义扫描 modules/*/templates 并合并。
}
