use actix_identity::Identity;
use actix_web::{get, web, HttpResponse, HttpRequest};
use deadpool_postgres::Pool; // may be unused for index/login
use modules_common::service::{get_user, r2s, Search, UserData};
use modules_common::templates::*;

fn goto_login() -> HttpResponse {
    HttpResponse::Found()
        .append_header(("location", format!("/{}", "login")))
        .finish()
}

fn name_show(user: &UserData) -> String {
    if user.duty != "总经理" {
        format!("｜{}区 {}｜ 　{}", user.area, user.duty, user.name)
    } else {
        format!("{} 　{}", user.duty, user.name)
    }
}

/// 首页
#[get("/")]
pub async fn index(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let name = name_show(&user);
        let html = r2s(|o| home_html(o, name));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        let html = r2s(|o| login_html(o));
        HttpResponse::Ok().content_type("text/html").body(html)
    }
}

/// 登录页
#[get("/login")]
pub async fn login() -> HttpResponse {
    let html = r2s(|o| login_html(o));
    HttpResponse::Ok().content_type("text/html").body(html)
}

/// 静态资源（优先 ructe 内嵌，找不到则回退到磁盘 static/*）
#[get("/static/{name:.*}")]
pub async fn static_file(req: HttpRequest, name: web::Path<String>) -> HttpResponse {
    use actix_files::NamedFile;
    use std::path::PathBuf;

    let name = name.into_inner();
    if let Some(data) = modules_common::templates::statics::StaticFile::get(&name) {
        HttpResponse::Ok()
            .body(data.content)
    } else {
        // fallback to disk for non-hashed plain files under workspace_root/static
        let path: PathBuf = [env!("CARGO_MANIFEST_DIR"), "../../static", &name].iter().collect();
        match NamedFile::open(path) {
            Ok(f) => f.into_response(&req),
            Err(_) => HttpResponse::NotFound().finish(),
        }
    }
}

/// 商品销售
#[get("/sale/{dh}")]
pub async fn sale(db: web::Data<Pool>, dh_num: web::Path<String>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "商品销售".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" { "新单据" } else { &*dh_num };
        let setup = vec!["商品销售", "客户", "出库、发货及开票单号", dh, "customer"];
        user.show = name_show(&user);
        let html = r2s(|o| sale_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 销售退货
#[get("/saleback/{dh}")]
pub async fn saleback(db: web::Data<Pool>, dh_num: web::Path<String>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "商品销售".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" { "新单据" } else { &*dh_num };
        let setup = vec!["销售退货", "客户", "入库单号", dh, "customer"];
        user.show = name_show(&user);
        let html = r2s(|o| sale_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 销售开票
#[get("/kp/{dh}")]
pub async fn kp(db: web::Data<Pool>, dh_num: web::Path<String>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "财务开票".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" { "新单据" } else { &*dh_num };
        let setup = vec!["销售开票", "客户", "出库及发货单号", dh, ""];
        user.show = name_show(&user);
        let html = r2s(|o| kp_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 运输发货
#[get("/transport/{dh}")]
pub async fn transport(db: web::Data<Pool>, dh_num: web::Path<String>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "商品销售".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" { "新单据" } else { &*dh_num };
        let setup = vec!["运输发货", "客户", "出库单", dh, "no_customer"];
        user.show = name_show(&user);
        let html = r2s(|o| saletrans_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 销售查询
#[get("/sale_query")]
pub async fn sale_query(db: web::Data<Pool>, limit: web::Query<Search>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "销售查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "采购销售", "销售查询", "sale_items", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 发货查询
#[get("/trans_query")]
pub async fn trans_query(db: web::Data<Pool>, limit: web::Query<Search>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "销售查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "采购销售", "发货查询", "fh_items", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 销售明细
#[get("/sale_items")]
pub async fn sale_items(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| saleitems_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 发货明细
#[get("/trans_items")]
pub async fn trans_items(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| transitems_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}
