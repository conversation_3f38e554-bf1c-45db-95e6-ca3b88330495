use actix_web::web;
use erp_core::Module;

mod pages;

pub struct SalesModule;

impl Default for SalesModule {
    fn default() -> Self {
        Self
    }
}

impl Module for SalesModule {
    fn name(&self) -> &'static str {
        "sales"
    }

    fn configure_routes(&self, cfg: &mut web::ServiceConfig) {
        cfg.service(
            web::scope("/sales/pages")
                .service(pages::index)
                .service(pages::sale)
                .service(pages::saleback)
                .service(pages::kp)
                .service(pages::transport)
                .service(pages::sale_query)
                .service(pages::trans_query)
                .service(pages::sale_items)
                .service(pages::trans_items),
        );

        // TODO: 销售相关 API 将在 buysale.rs 等拆分完成后，接入到 /sales/api 下。
        // 迁移阶段先保留空 scope，确保页面功能可运行与编译通过。
        cfg.service(web::scope("/sales/api"));
    }
}
