@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData, setup: Vec<&str>)

    @:base_html({
    <link rel="stylesheet" href="/assets/plugins/laydate/theme/default/laydate.css">

    <div class="buy-in">
        <div class="top-title"><span><i class="fa fa-shopping-cart"></i> 采购销售 </span>
            <span class="t1" id="document-bz"><i class="fa fa-angle-double-right"></i> @setup[0] </span>
        </div>
        <div class="buy-content">
            <div class="inout-top has-bak" style="width: 99%;">
                <div class="fields-show">
                    @if setup[4] == "customer" {
                    <div class="form-group has-auto" id="customer-div">
                        <div class="form-label" style="margin-right: 8px;">
                            <label id="customer-suplier">@setup[1]</label>
                        </div>
                        <div class="form-input autocomplete">
                            <input class="form-control input-sm" type="text" id="supplier-input" />
                        </div>
                        <button class="btn btn-info btn-sm" id="supplier-serach">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                    }
                </div>
                <div class="buy-buttons">
                    <div class="has-dh">№: <span id="dh">@setup[3]</span> <span id="owner"></span></div>
                    <div class="buttons">
                        <button class="btn btn-info btn-sm button-buy" id="save-button">保存</button>
                        <button class="btn btn-info btn-sm button-buy" id="remember-button">审核</button>
                    </div>
                </div>
            </div>
            <div class="buy-items has-bak">
                <div class="table-container table-items not-scroll-left">
                    <table>
                        <thead>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <div class="table-ctrl">
                        <div class="table-button">
                            <button class="btn btn-info btn-sm" id="row-insert" title="插入"><i
                                    class="fa fa-plus-square"></i></button>
                            <button class="btn btn-info btn-sm" id="row-del" title="删除"><i
                                    class="fa fa-trash"></i></button>
                            <button class="btn btn-info btn-sm" id="row-up" title="上移"><i
                                    class="fa fa-arrow-up"></i></button>
                            <button class="btn btn-info btn-sm" id="row-down" title="下移"><i
                                    class="fa fa-arrow-down"></i></button>
                        </div>

                        <div class="table-tips" id="sum-money">
                            金额合计： 元
                        </div>

                        <div class="table-info">
                            共 <span id="total-records"></span> 条记录
                        </div>
                    </div>
                </div>
                <div class="right-tables">
                    <div class="table-container table-history">
                        <table>
                            <thead>
                                <tr>
                                    <th>@setup[2]</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="table-container table-note" hidden>
                        <table>
                            <thead>
                                <tr>
                                    <th>退货相关单据</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/assets/plugins/laydate/laydate.js"></script>
    <script src="/static/@tools_service_js.name"></script>
    <script src="/static/@sale_js.name"></script>
    }, user.show.clone())

