use actix_identity::Identity;
use actix_web::{get, web, HttpResponse};
use deadpool_postgres::Pool;

use modules_common::service::{get_user, r2s, Search, UserData};
use modules_common::templates::{buyin_html, query_html};
use modules_common::pages::{name_show, goto_login};

/// 材料采购
#[get("/buy_in/{dh}")]
pub async fn buy_in(db: web::Data<Pool>, dh_num: web::Path<String>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "材料采购".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" { "新单据" } else { &*dh_num };
        let setup = vec!["材料采购", "供应商", "入库单号", dh, "customer"];
        user.show = name_show(&user);
        let html = r2s(|o| buyin_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 采购退货
#[get("/buy_back/{dh}")]
pub async fn buy_back(db: web::Data<Pool>, dh_num: web::Path<String>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "材料采购".to_owned()).await;
    if user.name != "" {
        let dh = if *dh_num == "new" { "新单据" } else { &*dh_num };
        let setup = vec!["采购退货", "供应商", "出库单号", dh, "customer"];
        user.show = name_show(&user);
        let html = r2s(|o| buyin_html(o, user, setup));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

/// 采购查询
#[get("/buy_query")]
pub async fn buy_query(db: web::Data<Pool>, limit: web::Query<Search>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "采购查询".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| query_html(o, user, "采购销售", "采购查询", "buy_items", &limit.s));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}
