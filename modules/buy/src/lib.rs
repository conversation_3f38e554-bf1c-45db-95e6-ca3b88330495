use actix_web::{web, HttpResponse};
use erp_core::{Module, ModuleDependency};
use semver::Version;

mod pages;

pub struct PurchaseModule;

impl Default for PurchaseModule { fn default() -> Self { Self } }

impl Module for PurchaseModule {
    fn name(&self) -> &'static str { "purchase" }
    fn version(&self) -> Version { Version::new(0, 1, 0) }
    fn dependencies(&self) -> &'static [ModuleDependency] { &[] }

    fn configure_routes(&self, cfg: &mut web::ServiceConfig) {
        // 页面路由（按规范挂载 /buy/pages/*）
        cfg.service(
            web::scope("/buy/pages")
                .service(pages::buy_in)
                .service(pages::buy_back)
                .service(pages::buy_query)
        );

        // TODO: 采购相关 API 由旧 buysale.rs 拆分后接入到此处的 /buy/api 下。
        // 迁移阶段先留空，避免引用未迁移的旧模块导致编译失败。
        cfg.service(web::scope("/buy/api"));

        // 健康检查
        cfg.route("/buy/api/health", web::get().to(|| async { HttpResponse::Ok().body("purchase ok") }));
    }
}
