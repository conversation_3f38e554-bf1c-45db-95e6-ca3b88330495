@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="user-manage">
    <div class="top-title"><span><i class="fa fa-wrench"></i> 功能设置 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 用户设置 </span>
    </div>
    <div class="user-content has-bak">
        <div id="users-show">
            <div class="table-top">
                <div class="table-search">
                    <input type="text" class="form-control" id="search-input" placeholder="用户搜索">
                    <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                </div>
                <div class="table-edit">
                    <button class="btn btn-info btn-sm" id="edit-button">编辑</button>
                    <button class="btn btn-info btn-sm" id="del-button">删除</button>
                    <button class="btn btn-info btn-sm hide" id="sumit-button">提交</button>
                    <button class="btn btn-info btn-sm hide" id="cancel-button">取消</button>
                    <button class="btn btn-info btn-sm" id="reset-button">重置密码</button>
                </div>
            </div>

            <div class="table-container table-users">
                <table>
                    <thead>
                        <tr>
                            <th width="4%">序号</th>
                            <th>用户名</th>
                            <th>职务</th>
                            <th width="13%">手机号</th>
                            <th>区域</th>
                            <th width="40%">工作权限</th>
                            <th>是否确认</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
        <div id="user-rights">
            <div class="rights-top">
                工作权限
            </div>
            <div class="table-container rights-show">
                <table>
                    <thead>
                        <tr>
                            <th><label class="check-radio"><input type="checkbox" id="um_goods_buy">
                                    <span class="checkmark"></span>材料采购</label></th>
                            <th><label class="check-radio"><input type="checkbox" id="um_goods_sale">
                                    <span class="checkmark"></span>商品销售</label></th>
                            <th><label class="check-radio"><input type="checkbox" id="um_goods_manage">
                                    <span class="checkmark"></span>仓储管理</label></th>
                            <th><label class="check-radio"><input type="checkbox" id="um_statics">
                                    <span class="checkmark"></span>业务报表</label></th>
                            <th><label class="check-radio"><input type="checkbox" id="um_base_info">
                                    <span class="checkmark"></span>基础信息</label></th>
                            <th><label class="check-radio"><input type="checkbox" id="um_setup">
                                    <span class="checkmark"></span>功能设置</label></th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl"></div>
            </div>
        </div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/users/js/usermanage.js"></script>
}, user.show.clone())