.user-set {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    margin-top: 60px;

    > div {
        width: 32%;
        min-width: 450px;
    }

    .title {
        font-size: $fs-lg;
        padding-top: 10px;
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid #e8e8e8;

        span {
            margin-left: 6px;
        }
    }

    form {
        margin-left: 25px;
    }

    input {
        height: 20px;
    }

    #phone-num {
        display: flex;
        align-items: center;
    }

    // .form-label {
    //     display: inline-block;
    //     width: 110px;
    //     text-align: right;
    // }

    #show-rights {
        display: flex;
        align-items: flex-start;
    }

    #rights {
        width: 80%;
        margin-left: 15px;
    }
}
#password-set {
    button {
        margin-left: 185px;
    }
}

#theme-set {
    .theme-container {
        margin-left: 60px;
        div {
            display: inline-block;
            width: 40px;
            height: 40px;
            margin-right: 20px;
            cursor: pointer;
        }
    }
}
