.user-manage {
    .user-content {
        padding-top: 10px;
        display: flex;
        justify-content: first baseline;
    }

    #users-show {
        width: 50%;

        .table-top {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .table-edit button {
            margin-right: 10px;
        }

        .table-search {
            display: flex;
            align-items: center;
        }

        #search-input {
            width: 180px;
            height: 26px;
            padding: 2px 8px;
            border-color: #e8e8e8;

            &:hover,
            &:focus {
                border-color: $primary-200;
            }
        }

        #serach-button {
            color: white;
            margin-left: 5px;
        }

        tbody tr td:nth-child(1),
        tbody tr td:nth-last-child(1) {
            text-align: center;
        }

        .confirm-info {
            display: inline-block;
            padding: 0 8px;
            color: white;
            height: 22px;
            font-size: 12px;
            border-radius: 2px;
        }

        td .check-radio {
            display: inline;
        }
    }

    #user-rights {
        width: 50%;
        margin-left: 30px;

        tbody tr td {
            text-align: left;
            padding-left: 16px;
            border: none;
            border-left: 1px solid rgba(0, 0, 0, 0.09);
        }

        tbody tr:nth-last-child(1) {
            border-bottom: 1px solid rgba(0, 0, 0, 0.09);
        }

        // tbody tr:nth-child(1) {
        //     border-left: none !important;
        // }

        tr:hover {
            background: none;
        }

        .rights-top {
            margin-bottom: 11px;
            margin-top: 8.5px;
        }

        .rights-show {
            height: auto;
        }

        .check-radio {
            padding-left: 24px;
            .checkmark {
                height: 13px;
                width: 13px;
                top: 3px;

                &::after {
                    left: 3.6px;
                    top: 0;
                }
            }
        }
    }
}
