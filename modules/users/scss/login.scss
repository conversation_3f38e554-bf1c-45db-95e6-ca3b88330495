a,
a:active,
a:focus,
button,
button:focus,
button:active {
    outline: none;
    outline: 0;
}
input::-moz-focus-inner {
    border: 0;
}

body {
    font-size: 14px;
    padding-top: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    background: url(/assets/img/4.jpg) no-repeat center;
    background-size:100% 100%;
    background-attachment:fixed;
}

input {
    width: 100%;
    padding: 12px 20px;
    margin: 8px 0;
    box-sizing: border-box;
    border: 3px solid #ccc;
    background-color: white;
    -webkit-transition: 0.5s;
    transition: 0.5s;
    outline: none;

    &：focus {
        border: 1px solid #45a3e9;
    }
}

a {
    text-decoration: none;
}

.top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    height: 60px;
    width: 100%;
    z-index: 30;
    padding-top: 2px;

    #logo {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-left: 15%;

        img {
            width: 28px;
        }
    }

    #title {
        font-size: 18px;
        color: white;
        margin-left: 8px;
    }

    .menu-log {
        color: white;
        margin-right: 15%;

        a {
            color: white;
        }
    }
}

.main {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    flex: 1;

    background-size: cover;
    background-color: transparent;

    .log-show {
        width: 260px;
        margin-top: 5%;
        border-radius: 10px;
        background-color: #f7f9fa;
        box-shadow: 0 0 13px 0 rgba(74, 53, 107, 0.08);

        padding: 1.5rem;
        word-wrap: break-word;
        border: 1px solid rgba(0, 0, 0, 0.08);

        display: flex;
        flex-direction: column;

        .form-group {
            margin-bottom: 1.5rem;

            label {
                font-weight: 500;
                display: block;
                margin-bottom: 0.3rem;
            }
        }

        .form-control {
            height: 45px;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 4px;
            color: #495057;
            background-clip: padding-box;
            border: 1px solid #dbdbdb;

            &:focus {
                border: 1px solid #45a3e9;
            }
        }

        .to-center {
            display: flex;
            flex-direction: row;
            justify-content: center;
        }

        button {
            cursor: pointer;
            display: block;
            width: 60%;
            box-shadow: 0 2px 6px 0 rgba(33, 150, 243, 0.5);
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: 4px;
            border: 1px solid transparent;
            color: #fff;
            background-color: #2196f3;
            border-color: #2196f3;
            font-weight: 400;
            text-align: center;
            transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
                box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;

            &:hover {
                background-color: #1385e2;
            }
        }
    }

    footer {
        height: 60px;
        color: white;
        a {
            color: white;
            text-decoration-skip-ink: auto;
            font-weight: 600;
        }
    }
}

.select-large {
    font-size: 14px;
    height: 45px;
    width: 100%;
    margin-top: 10px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background-color: #fff;
    outline: none;
    padding-left: 23px;
  }

@import "parts/notify";
