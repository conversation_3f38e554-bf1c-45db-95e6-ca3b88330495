[package]
name = "modules-users"
version = "0.1.0"
edition = "2021"

[lib]
name = "modules_users"
path = "src/lib.rs"

[dependencies]
erp-core = { path = "../../core" }
actix-web = "4"
actix-identity = "0.4"
deadpool-postgres = { version = "0.12.1", features = ["serde"] }
semver = "1"
modules-common = { path = "../common" }
rust-crypto = "0.2.36"
rand = "0.8"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
reqwest = { version = "0.11" }
