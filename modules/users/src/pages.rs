use modules_common::service::{get_user, r2s, Search, UserData};
use modules_common::pages::{name_show, goto_login};
use actix_identity::Identity;
use actix_web::{get, web, HttpRequest, HttpResponse};
use deadpool_postgres::Pool;
use modules_common::templates::*; // 从 common 模块导出的模板

///登录
#[get("/login")]
pub async fn login(_req: HttpRequest) -> HttpResponse {
    let html = r2s(|o| login_html(o));
    HttpResponse::Ok().content_type("text/html").body(html)
}

///用户自己设置
#[get("/user_set")]
pub async fn user_set(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| userset_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}

///用户管理
#[get("/user_manage")]
pub async fn user_manage(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let mut user = get_user(&db, id, "用户设置".to_owned()).await;
    if user.name != "" {
        user.show = name_show(&user);
        let html = r2s(|o| usermanage_html(o, user));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        goto_login()
    }
}