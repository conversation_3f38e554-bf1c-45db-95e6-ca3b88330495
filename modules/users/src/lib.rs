use actix_web::web;
use erp_core::{Module, ModuleDependency};
use semver::Version;

mod pages;
pub mod user;

pub struct UsersModule;

impl Default for UsersModule { fn default() -> Self { Self } }

impl Module for UsersModule {
    fn name(&self) -> &'static str { "users" }
    fn version(&self) -> Version { Version::new(0, 1, 0) }
    fn dependencies(&self) -> &'static [ModuleDependency] { &[] }

    fn configure_routes(&self, cfg: &mut web::ServiceConfig) {
        // 页面路由：/user/pages/*
        cfg.service(
            web::scope("/user/pages")
                // .service(pages::login)
                .service(pages::user_set)
                .service(pages::user_manage)
        );

        // 用户 API：/user/api/*
        cfg.service(
            web::scope("/user/api")
                .service(user::login)
                .service(user::logon)
                .service(user::logout)
                .service(user::forget_pass)
                .service(user::change_pass)
                .service(user::phone_number)
                .service(user::pull_users)
                .service(user::edit_user)
                .service(user::del_user)
                .service(user::reset_user_password)
        );
    }
}
