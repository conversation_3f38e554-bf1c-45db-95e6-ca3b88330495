use actix_identity::Identity;
use actix_web::{get, web, HttpResponse};
use actix_web::web::Path;
use deadpool_postgres::Pool;
use crate::service::{get_user, r2s, UserData};
use crate::templates::*;
use crate::templates::statics::StaticFile;

pub async fn static_file(path: Path<String>) -> HttpResponse {
    let name = &path.into_inner();
    if let Some(data) = StaticFile::get(name) {
        HttpResponse::Ok()
            .body(data.content)
    } else {
        HttpResponse::NotFound()
            .reason("No such static file.")
            .finish()
    }
}

pub fn goto_login() -> HttpResponse {
    HttpResponse::Found()
        .append_header(("location", format!("/{}", "login")))
        .finish()
}

pub fn name_show(user: &UserData) -> String {
    if user.duty != "总经理" {
        format!("｜{}区 {}｜ 　{}", user.area, user.duty, user.name)
    } else {
        format!("{} 　{}", user.duty, user.name)
    }
}

/// 首页
#[get("/")]
pub async fn index(db: web::Data<Pool>, id: Identity) -> HttpResponse {
    let user = get_user(&db, id, "".to_owned()).await;
    if user.name != "" {
        let name = name_show(&user);
        let html = r2s(|o| home_html(o, name));
        HttpResponse::Ok().content_type("text/html").body(html)
    } else {
        let html = r2s(|o| login_html(o));
        HttpResponse::Ok().content_type("text/html").body(html)
    }
}

/// 登录页（Base 顶层）
#[get("/login")]
pub async fn login() -> HttpResponse {
    let html = r2s(|o| login_html(o));
    HttpResponse::Ok().content_type("text/html").body(html)
}
