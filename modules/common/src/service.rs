use actix_files as fs;
use actix_identity::Identity;
use actix_multipart::Multipart;
use actix_web::Either;
use actix_web::{get, web, Error, HttpRequest, HttpResponse};
use deadpool_postgres::{Object, Pool};
use futures::TryStreamExt;
use image::imageops::FilterType;
use image::GenericImageView;
use rust_xlsxwriter::{Format, Workbook};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::io::{self, Write};
use time::now;
use deadpool_postgres::tokio_postgres::Row;
// use chrono::{Datelike, NaiveDate};

pub static SPLITER: &str = "<`*_*`>";
pub static NOT_DEL_SQL: &str = " and 作废 = false";

//自动完成搜索字符串
#[derive(Deserialize)]
pub struct Search {
    pub s: String,
}

#[derive(Deserialize)]
pub struct SearchCate {
    pub s: String,
    pub cate: String,
}

#[derive(Deserialize)]
pub struct SearchPlus {
    pub s: String,
    pub ss: String,
}

#[derive(Deserialize, Serialize)]
pub struct UserData {
    pub name: String,
    pub duty: String,
    pub phone: String,
    pub area: String,
    pub get_pass: i32,
    pub rights: String,
    pub confirm: bool,
    pub theme: String,
    pub show: String,
}

//表格分页、搜索和分类参数
#[derive(Deserialize, Serialize)]
pub struct TablePager {
    pub id: String,
    pub name: String,
    pub page: i32,
    pub sort: String,
    pub rec: i32,
    pub cate: String,
}

#[derive(Deserialize, Serialize)]
pub struct TablePagerExt {
    pub id: String,
    pub name: String,
    pub page: i32,
    pub sort: String,
    pub rec: i32,
    pub cate: String,
    pub filter: String,
}

//自动完成使用
#[derive(Deserialize, Serialize)]
pub struct Message {
    pub id: String,
    pub label: String,
}

//存放显示字段信息：字段名称，显示名称，数据类型，可选值，显示宽度
#[derive(Deserialize, Serialize)]
pub struct FieldsData {
    pub field_name: String,
    pub show_name: String,
    pub data_type: String,
    pub ctr_type: String,
    pub option_value: String,
    pub default_value: String,
    pub show_width: f32,
    pub all_edit: bool,
}

#[derive(Deserialize, Serialize)]
pub struct DocumentDh {
    pub cate: String,
    pub dh: String,
}

#[derive(Deserialize, Serialize)]
pub struct Document {
    pub rights: String,
    pub document: String,
    pub remember: String,
    pub items: Vec<String>,
}

#[derive(Deserialize, Serialize)]
pub struct Customer {
    pub rights: String,
    pub cate: String,
    pub id: i32,
}

// 自动生成单号
pub async fn get_dh(db: web::Data<Pool>, doc_data: &str) -> String {
    let conn = db.get().await.unwrap();
    let dh_pre = if doc_data == "材料采购" {
        "CG"
    } else if doc_data == "采购退货" {
        "CT"
    } else if doc_data == "商品销售" {
        "XS"
    } else if doc_data == "销售退货" {
        "XT"
    } else if doc_data == "采购入库" {
        "RK"
    } else if doc_data == "销售出库" {
        "CK"
    } else if doc_data == "运输发货" {
        "FH"
    } else if doc_data == "调整入库" {
        "TR"
    } else if doc_data == "销售开票" {
        "KP"
    } else {
        "TC"
    };

    let date_string = now().strftime("%Y-%m-%d").unwrap().to_string();
    let local: Vec<&str> = date_string.split("-").collect();
    let y = local[0];
    let date = format!("{}{}{}-", dh_pre, &y[2..4], local[1]); //按月

    //获取尾号
    let sql = format!(
        "SELECT COALESCE(max(单号),'0') as 单号 FROM documents WHERE 单号 like '{}%'",
        dh_pre
    );

    let rows = &conn.query(sql.as_str(), &[]).await.unwrap();

    let mut dh_first = "".to_owned();

    for row in rows {
        dh_first = row.get("单号");
    }

    let keep = 3usize; // 单号位数
    let len = dh_first.len();
    let mut num = 1i32;
    if dh_first != "0" {
        if let Some(n) = dh_first.get(len - keep..len) {
            if dh_first == format!("{}{}", date, n) {
                num = n.parse::<i32>().unwrap() + 1;
            }
        }
    }

    return format!("{}{:0pad$}", date, num, pad = keep);
}

///下载文件服务
#[get("/download/{filename:.*}")]
pub async fn serve_download(
    req: HttpRequest,
    db: web::Data<Pool>,
    id: Identity,
) -> Either<Result<fs::NamedFile, Error>, Result<&'static str, Error>> {
    let user = get_user(&db, id, "导出数据".to_owned()).await;
    if user.name != "" {
        let path = req.match_info().query("filename");
        Either::Left(Ok(
            fs::NamedFile::open(format!("./download/{}", path)).unwrap()
        ))
    } else {
        Either::Right(Ok("你没有权限下载该文件"))
    }
}

///模板转换成网页字符串
pub fn r2s<Call>(call: Call) -> String
where
    Call: FnOnce(&mut Vec<u8>) -> io::Result<()>,
{
    let mut buf = Vec::new();
    call(&mut buf).unwrap();
    String::from_utf8(buf).unwrap()
}

///获取用户信息
/// right 如果是空串 "", 则不检查权限
pub async fn get_user(db: &web::Data<Pool>, id: Identity, right: String) -> UserData {
    let mut user = UserData {
        name: "".to_owned(),
        duty: "".to_owned(),
        phone: "".to_owned(),
        area: "".to_owned(),
        get_pass: 0,
        rights: "".to_owned(),
        confirm: false,
        theme: "".to_owned(),
        show: "".to_owned(),
    };

    let user_name = id.identity().unwrap_or("".to_owned());
    if user_name != "" {
        let conn = db.get().await.unwrap();
        let right = format!("%{}%", right);
        let rows = &conn
            .query(
                r#"SELECT name, duty, phone, area, 6-get_pass as get_pass, rights, confirm, theme 
                FROM users WHERE name=$1 AND confirm=true AND rights LIKE $2"#,
                &[&user_name, &right],
            )
            .await
            .unwrap();

        if rows.len() > 0 {
            user.name = rows[0].get("name");
            user.duty = rows[0].get("duty");
            user.phone = rows[0].get("phone");
            user.area = rows[0].get("area");
            user.get_pass = rows[0].get("get_pass");
            user.rights = rows[0].get("rights");
            user.confirm = rows[0].get("confirm");
            user.theme = rows[0].get("theme");
        }
    }
    user
}

//自动完成
pub async fn autocomplete(db: web::Data<Pool>, sql: &str) -> HttpResponse {
    let conn = db.get().await.unwrap();
    let rows = &conn.query(sql, &[]).await.unwrap();

    let mut data = Vec::new();
    for row in rows {
        let message = Message {
            id: row.get("id"),
            label: row.get("label"),
        };

        data.push(message);
    }

    HttpResponse::Ok().json(data)
}

//列表查询，获取记录和分页数
pub async fn pages(conn: &Object, sql: String, rec: i32) -> (i32, i64) {
    let row = &conn.query_one(sql.as_str(), &[]).await.unwrap();
    let count: i64 = row.get("记录数");
    let pages = (count as f64 / rec as f64).ceil() as i32;
    (pages, count)
}

///获取一条空记录
#[get("/fetch_nothing")]
pub async fn fetch_nothing() -> HttpResponse {
    HttpResponse::Ok().json(1)
}

//上传文件保存
pub async fn save_file(mut payload: Multipart) -> Result<String, Error> {
    let path = "./upload/upload_in.xlsx".to_owned();
    while let Some(mut field) = payload.try_next().await? {
        let filepath = path.clone();
        let mut f = web::block(|| std::fs::File::create(filepath)).await??;
        while let Some(chunk) = field.try_next().await? {
            f = web::block(move || f.write_all(&chunk).map(|_| f)).await??;
        }
    }
    Ok(path)
}

pub async fn save_pic(mut payload: Multipart, path: String) -> Result<String, Error> {
    use actix_web::error::ErrorInternalServerError;
    use tokio::fs::File;
    use tokio::io::AsyncWriteExt;

    while let Some(mut field) = payload.try_next().await? {
        let filepath = path.clone();
        let mut f = File::create(&filepath)
            .await
            .map_err(ErrorInternalServerError)?;

        // 检查文件大小，不能超过 10 MB
        let mut size: usize = 0;
        while let Some(chunk) = field.try_next().await? {
            size += chunk.len();
            if size > 10 * 1024 * 1024 {
                // 超限直接返回错误码，前端提示
                return Ok("-3".to_owned());
            }
            f.write_all(&chunk).await.map_err(ErrorInternalServerError)?;
        }
        // 确保落盘
        f.flush().await.map_err(ErrorInternalServerError)?;
    }
    Ok(path)
}

//缩小图片
pub fn smaller(path: String, path2: String) -> String {
    let img = image::open(path).unwrap();
    let (width, height) = img.dimensions();
    let r = if height < width { 200 } else { 800 };
    let scaled = img.resize(100, r, FilterType::Lanczos3);
    let path3 = format!("{}min.jpg", path2);
    scaled.save(path3.clone()).unwrap();
    path3
}

//获取编辑用的显示字段 is_show
pub async fn get_fields(db: web::Data<Pool>, table_name: &str) -> Vec<FieldsData> {
    let conn = db.get().await.unwrap();
    let rows = &conn
        .query(
            r#"SELECT field_name, show_name, data_type, ctr_type, option_value, default_value, show_width, all_edit
                    FROM tableset WHERE table_name=$1 AND is_show=true ORDER BY show_order"#,
            &[&table_name],
        )
        .await
        .unwrap();

    return_fields(rows)
}

//映射使用的字段 is_use
pub async fn map_fields(db: &web::Data<Pool>, table_name: &str) -> HashMap<String, String> {
    let conn = db.get().await.unwrap();
    let rows = &conn
        .query(
            r#"SELECT field_name, show_name, data_type, ctr_type, option_value, default_value, show_width, all_edit
                    FROM tableset WHERE table_name=$1 AND is_use=true ORDER BY show_order"#,
            &[&table_name],
        )
        .await
        .unwrap();

    let mut f_map: HashMap<String, String> = HashMap::new();

    for row in rows {
        f_map.insert(row.get("show_name"), row.get("field_name"));
    }

    f_map
}

// 获取查询单据的权限
pub async fn get_limits(user: &UserData) -> String {
    let mut limits = "".to_owned();
    if user.duty == "主管" {
        limits = format!("documents.文本字段7 = '{}' AND", user.area); // 文本字段7 为 区域
    } else if user.duty == "库管" {
        limits = format!(
            "documents.文本字段7 = '{}' AND 经办人 = '{}' AND",
            user.area, user.name
        ); // 文本字段7 为 区域
    } else if user.duty == "销售" {
        limits = format!("经办人 = '{}' AND", user.name);
    }

    limits
}

//获取出入库用的显示字段 is_show
pub async fn get_inout_fields(db: web::Data<Pool>, table_name: &str) -> Vec<FieldsData> {
    let conn = db.get().await.unwrap();
    let rows = &conn
        .query(
            r#"SELECT field_name, show_name, data_type, ctr_type, option_value, default_value, inout_width as show_width, all_edit
                FROM tableset WHERE table_name=$1 AND inout_show=true ORDER BY inout_order"#,
            &[&table_name],
        )
        .await
        .unwrap();

    return_fields(rows)
}

//返回字段数组，内部辅助函数
fn return_fields(rows: &Vec<Row>) -> Vec<FieldsData> {
    let mut fields: Vec<FieldsData> = Vec::new();
    for row in rows {
        let data = FieldsData {
            field_name: row.get("field_name"),
            show_name: row.get("show_name"),
            data_type: row.get("data_type"),
            ctr_type: row.get("ctr_type"),
            option_value: row.get("option_value"),
            default_value: row.get("default_value"),
            show_width: row.get("show_width"),
            all_edit: row.get("all_edit"),
        };

        fields.push(data);
    }

    fields
}

//从数据库读取数据后，按显示字段，组合成字符串数组。返回给前端
pub fn build_string_from_base(
    rows: &Vec<Row>,
    fields: Vec<FieldsData>,
) -> Vec<String> {
    let mut products = Vec::new();
    for row in rows {
        let mut product = "".to_owned();
        let num: &str = row.get("id"); //字段顺序已与前端配合一致，后台不可自行更改
        product += &format!("{}{}", num, SPLITER);
        let num: i64 = row.get("序号");
        product += &format!("{}{}", num, SPLITER);

        product += &simple_string_from_base(row, &fields);

        products.push(product);
    }
    products
}

//将数据库查询结果字段组合成字符串，即是内部辅助函数，也可外部调用
pub fn simple_string_from_base(row: &Row, fields: &Vec<FieldsData>) -> String {
    let mut product = "".to_owned();
    for f in fields {
        if f.data_type == "文本" {
            let s: String = row.get(&*f.field_name);
            let s1 = if s != "" { s } else { " ".to_owned() };
            product += &format!("{}{}", s1, SPLITER);
        } else if f.data_type == "整数" {
            let num: i32 = row.get(&*f.field_name);
            product += &format!("{}{}", num, SPLITER);
        } else if f.data_type == "实数" {
            let num: f64 = row.get(&*f.field_name);
            product += &format!("{}{}", num, SPLITER);
        } else {
            let op: Vec<&str> = f.option_value.split("_").collect();
            let b: bool = row.get(&*f.field_name);
            let val = if b == true { op[0] } else { op[1] };
            product += &format!("{}{}", val, SPLITER);
        }
    }

    product
}

//从前端传过来字符串数组，按显示字段，组合成 update 语句。供更新数据用
//参数：n 是字段名数组的偏移量
pub fn build_sql_for_update(
    field_names: Vec<&str>,
    mut sql: String,
    fields: Vec<FieldsData>,
    n: usize,
) -> String {
    for i in 0..fields.len() {
        if fields[i].data_type == "文本" {
            sql += &format!("{}='{}',", fields[i].field_name, field_names[i + n]);
        } else if fields[i].data_type == "实数" || fields[i].data_type == "整数" {
            sql += &format!("{}={},", fields[i].field_name, field_names[i + n]);
        } else {
            let op: Vec<&str> = fields[i].option_value.split("_").collect();
            let val = if field_names[i + n] == op[0] || field_names[i + n] == "true" {
                true
            } else {
                false
            };
            sql += &format!("{}={},", fields[i].field_name, val);
        }
    }
    sql
}

//从前端传过来字符串数组，按显示字段，组合成 insert 语句。供追加数据用
//参数：n 是字段名数组的偏移量，即从第 n 个元素算起，才是自定义字段
pub fn build_sql_for_insert(
    field_names: &Vec<&str>,
    mut sql: String,
    fields: &Vec<FieldsData>,
    n: usize,
) -> String {
    for i in 0..fields.len() {
        if fields[i].data_type == "文本" {
            sql += &format!("'{}',", field_names[i + n]);
        } else if fields[i].data_type == "实数" || fields[i].data_type == "整数" {
            sql += &format!("{},", field_names[i + n]);
        } else {
            let op: Vec<&str> = fields[i].option_value.split("_").collect();
            let val = if field_names[i + n] == op[0] || field_names[i + n] == "true" {
                true
            } else {
                false
            };
            sql += &format!("{},", val);
        }
    }
    sql
}

//将显示字段拼接成导出 excel 用的查询语句
pub fn build_sql_for_excel(mut sql: String, fields: &Vec<FieldsData>, table: String) -> String {
    for f in fields {
        if f.data_type == "文本" {
            sql += &format!("{}.{},", table, f.field_name);
        } else if f.data_type == "整数" || f.data_type == "实数" {
            sql += &format!("cast({}.{} as VARCHAR),", table, f.field_name);
        } else {
            let op: Vec<&str> = f.option_value.split("_").collect();
            sql += &format!(
                "case when {}.{} then '{}' else '{}' end as {},",
                table, f.field_name, op[0], op[1], f.field_name
            );
        }
    }
    sql
}

/// 构建带分隔符的字符串，用于数据库查询返回  
/// ```
/// let fields = vec![
///     row.get::<&str, String>("物料号"),
///     row.get::<&str, String>("名称"),
///     row.get::<&str, i32>("长度").to_string()
/// ];
/// println!("{}", sp_query(fields));
///
/// // 输出："M001265<`*_*`>圆钢<`*_*`>1234"
/// ```
pub fn sp_query(fields: Vec<String>) -> String {
    let mut result = "".to_owned();
    let len = fields.len();
    for n in 0..len {
        if n != len - 1 {
            result += &format!("{}{}", fields[n], SPLITER);
        } else {
            result += &format!("{}", fields[n]);
        }
    }

    result
}

#[derive(Deserialize, Serialize)]
pub struct Fields {
    pub name: &'static str,
    pub width: i32,
}

/// ### 导出到 Excel
/// ```
/// // sql 语句中的字段名称与 fields 中的 name 一致
/// let rows = &conn.query(sql.as_str(), &[]).await.unwrap();
/// // 注意最后一句无逗号
/// let f_str = r#"[
///     {"name": "序号", "width": 6},
///     {"name": "名称", "width": 12},
///     {"name": "长度", "width": 10},
///     {"name": "备注", "width": 15}
/// ]"#;
/// let fields = serde_json::from_str(f_str).unwrap();
/// out_excel("入库明细表", fields, rows.as_ref());
/// ```
pub fn out_excel(name: &str, fields: Vec<Fields>, rows: &Vec<Row>) {
    // NOTE: Excel导出实现依赖 rust_xlsxwriter 旧版 API；迁移阶段先占位，待你确认版本后再实现。
    let _ = (name, fields, rows);
}

/// 单据保存使用，返回单号 和 插入语句
pub async fn get_doc_sql(
    db: web::Data<Pool>,
    user: UserData,
    doc_data: Vec<&str>,
    table_name: &str,
) -> (String, String) {
    let f_map = map_fields(&db, table_name).await;
    let fields = get_inout_fields(db.clone(), table_name).await;
    let mut dh = doc_data[1].to_owned();
    let mut doc_sql;
    // 更新单据信息
    if dh == "新单据" {
        dh = get_dh(db.clone(), doc_data[0]).await;

        let mut init = "INSERT INTO documents (单号,".to_owned();
        for f in &fields {
            init += &format!("{},", &*f.field_name);
        }

        init += &format!(
            "客商id,类别,{},{}) VALUES('{}',",
            f_map["经办人"], f_map["区域"], dh
        );

        doc_sql = build_sql_for_insert(&doc_data, init, &fields, 4);
        doc_sql += &format!(
            "{},'{}','{}', '{}')",
            doc_data[2], doc_data[0], doc_data[3], user.area
        );
    } else {
        let init = "UPDATE documents SET ".to_owned();
        doc_sql = build_sql_for_update(doc_data.clone(), init, fields, 4);
        doc_sql += &format!(
            "客商id={}, 类别='{}', {}='{}', {}='{}' WHERE 单号='{}'",
            doc_data[2], doc_data[0], f_map["经办人"], doc_data[3], f_map["区域"], user.area, dh
        );
    }
    (dh, doc_sql)
}

// 传入一个日期，返回上个月的最后一天
// pub fn last_month_end_date(date_str: &str) -> Result<String, chrono::ParseError> {
//     let date = NaiveDate::parse_from_str(date_str, "%Y-%m-%d")?;

//     let last_day = date
//         .with_day(1) // 将日期设置为当前月的第一天
//         .and_then(|d| d.pred_opt()) // 获取前一天（即上个月的最后一天）
//         .unwrap(); // 由于逻辑正确，unwrap 是安全的

//     Ok(last_day.format("%Y-%m-%d").to_string())
// }
