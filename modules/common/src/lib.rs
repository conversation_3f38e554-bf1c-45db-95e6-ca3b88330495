use actix_web::web;
use erp_core::Module;

// 导出 ructe 生成的模板（包含 statics 和各模块模板）
include!(concat!(env!("OUT_DIR"), "/templates.rs"));

pub mod service;
pub mod pages;

pub struct BaseModule;

impl Default for BaseModule { fn default() -> Self { Self } }

impl Module for BaseModule {
    fn name(&self) -> &'static str { "base" }

    fn configure_routes(&self, cfg: &mut web::ServiceConfig) {
        // 基础页面（顶层）：/ 与 /login
        cfg
            .service(pages::index)
            .service(pages::login);
    }
}
