[package]
name = "modules-common"
version = "0.1.0"
edition = "2021"
build = "build.rs"

[lib]
name = "modules_common"
path = "src/lib.rs"

[dependencies]
erp-core = { path = "../../core" }
actix-web = "4"
actix-identity = "0.4"
semver = "1"

deadpool-postgres = { version = "0.12.1", features = ["serde"] }

actix-files = "0.6"
actix-multipart = "0.6"
futures = "0.3"
image = "0.24"
rust_xlsxwriter = "0.7"
time = "0.1"

serde = { version = "1", features = ["derive"] }
tokio = { version = "1", features = ["rt-multi-thread", "macros", "fs"] }


[build-dependencies]
ructe = "0.15"
