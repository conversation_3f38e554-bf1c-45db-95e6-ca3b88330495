use ructe::{<PERSON><PERSON><PERSON>, <PERSON>uc<PERSON>Error};
use std::path::Path;

fn main() -> Result<(), RucteError> {
    // 监听静态与模板目录变化
    println!("cargo:rerun-if-changed=../../static");
    println!("cargo:rerun-if-changed=../../templates");
    println!("cargo:rerun-if-changed=templates");

    let mut ructe = Ructe::from_env()?;
    let mut statics = ructe.statics()?;
    // 工作区根 static 目录（当前 build.rs 位于 modules/common）
    statics.add_files("../../static")?;

    // 编译工作区根 templates（供 home/login 等通用模板使用）
    if Path::new("../../templates").exists() {
        ructe.compile_templates("../../templates")?;
    }

    // 编译本模块 templates
    if Path::new("templates").exists() {
        ructe.compile_templates("templates")?;
    }

    Ok(())
}

