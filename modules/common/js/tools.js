var SPLITER = '<`*_*`>';
var regInt = /^[+]{0,1}(\d+)$/;
var regReal = /^-?\d+(\.\d+)?$/;
var regDate = /(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)/;

//左侧补零
function padZero(num, length) {
    return (Array(length).join('0') + num).slice(-length);
}

// 非加密的生成UUID（不推荐用于安全用途，但可用于生成文档 ID）
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0;
        // 关键逻辑在这里
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// HTML反转义函数
function unescapeHtml(str) {
    if (!str) return str;

    // 处理常见的转义字符
    return str
        .replace(/\\"/g, '"')           // 转义的双引号
        .replace(/\\'/g, "'")           // 转义的单引号
        .replace(/\\\\/g, '\\')         // 转义的反斜杠
        .replace(/\\n/g, '\n')          // 转义的换行符
        .replace(/\\r/g, '\r')          // 转义的回车符
        .replace(/\\t/g, '\t')          // 转义的制表符
        .replace(/&quot;/g, '"')        // HTML实体的双引号
        .replace(/&apos;/g, "'")        // HTML实体的单引号
        .replace(/&lt;/g, '<')          // HTML实体的小于号
        .replace(/&gt;/g, '>')          // HTML实体的大于号
        .replace(/&amp;/g, '&');        // HTML实体的和号（最后处理）
}

//获得控件的高度
function getHeight() {
    let content_height = document.body.clientHeight - 138;  //138 是 header，footer 和 top-title 的高度和
    var sum = 0;
    for (let i = 0; i < arguments.length; i++) {
        sum += arguments[i];
    }
    return content_height - sum;
}

//下载文件，url 是下载地址
function download_file(url) {
    var downloadLink = document.createElement("a");
    downloadLink.href = url;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

//检查上传文件类型，input 是输入控件
function checkFileType(input) {
    var acceptType = input.getAttribute('accept');
    var selectedFile = input.value;
    var fileType = selectedFile.substring(selectedFile.indexOf('.') + 1, selectedFile.length);
    var location = acceptType.indexOf(fileType);
    if (location > -1) {
        return true;
    } else {
        return false;
    }
}

// 补空行
function append_blanks(len, m) {
    let trs = "";
    for (let i = 0; i < len; i++) {
        trs += '<tr>';
        for (let j = 0; j < m; j++) {
            trs += '<td>　</td>';   //此处是全角空格，使空行高度与内容行一致
        }
        trs += '</tr>';
    }
    return trs;
}

//追加空单元格，用在打印中
function append_cells(m) {
    let tds = "";
    for (let j = 0; j < m; j++) {
        tds += '<td></td>';
    }
    return tds;
}

// 聚焦到指定 tabindex 的 input。由 enterToTab() 等函数调用
// 返回聚焦的 input
function goto_tabindex(row, idx) {
    var inputs = row.getElementsByTagName('input');
    for (var i = 0, j = inputs.length; i < j; i++) {
        if (inputs[i].getAttribute('idx') == idx) {
            inputs[i].focus();
            break;
        }
    }
    return inputs[i];
}

/// 回车变成tab键功能
/// row 是容器 Dom，里面有很多 input
/// input 是本身
function enterToTab(row, input, max_idx) {
    var tabindex = input.getAttribute('idx');
    goto_tabindex(row, ++tabindex);
    return tabindex;
}

/// 用于表格头部字段的键移动控制
/// all_input 所有包含的 input 输入元素
/// form 是 all_input 容器
/// max_n 是最大个数
function set_key_move(all_input, form, max_n) {
    all_input.forEach((input) => {
        input.onkeydown = function (e) {
            var e = event ? event : window.event;
            if (e.code == 'Enter' || e.code == 'NumpadEnter') {
                let idx = enterToTab(form, input, all_input.length);
                // idx 是返回值，最后一个是 max_n + 1
                if (idx == max_n + 1) {
                    goto_tabindex(form, 1);
                }
            }
            // 与自动完成有冲突
            // else if (e.code == 'ArrowUp') {
            //     let tabindex = input.getAttribute('idx')
            //     if (tabindex != '1') {
            //         goto_tabindex(form, --tabindex);
            //     } else {
            //         goto_tabindex(form, max_n);
            //     }
            // } else if (e.code == 'ArrowDown') {
            //     let tabindex = input.getAttribute('idx');
            //     if (tabindex != max_n) {
            //         goto_tabindex(form, ++tabindex);
            //     } else {
            //         goto_tabindex(form, 1);
            //     }
            // }
        }
    })
}

//获取距屏幕左边值
function getLeft(element, parent) {
    var left = element.offsetLeft;
    var current = element.offsetParent;

    while (current !== null) {
        left += current.offsetLeft;
        current = current.offsetParent;
    }

    return left - parent.scrollLeft;
}

//获取距屏幕上边值
function getTop(element, parent) {
    var actualTop = element.offsetTop;
    var current = element.offsetParent;

    while (current !== null) {
        actualTop += current.offsetTop;
        current = current.offsetParent;
    }

    return actualTop - parent.scrollTop;
}

//金额转中文大写
function moneyUppercase(n) {
    var fraction = ['角', '分', '厘', '毫'];
    var digit = [
        '零', '壹', '贰', '叁', '肆',
        '伍', '陆', '柒', '捌', '玖'
    ];
    var unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟']
    ];
    var head = n < 0 ? '欠' : '';
    n = Math.abs(n);
    var s = '';
    for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);
    for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return head + s.replace(/(零.)*零元/, '元')
        .replace(/(零.)+/g, '零')
        .replace(/^整$/, '零元整');
};

// 通用筛选过滤器初始化函数
// 用法：
// const tableFilter = initTableFilter({
//   getThs: () => document.querySelectorAll('.table-container thead th'),
//   has_filter: ['规格', ...],
//   url: '/fetch_filter_items',
//   state: { filter_conditions: new Map(), filter_sqls: [] },
//   buildFetchPostData: ({ name, baseFilterSql }) => ({ name: ..., filter_name: name, filter: baseFilterSql, ...更多字段 }),
//   onRefreshTable: (filterSql) => { ... 刷新表格 ... },
//   position: 'relative' | 'cursor',
//   positionOffset: { top: 0, left: 0 },
//   thDecorator: (th) => { ... 可选，设置 th 的样式 ... },
//   selectors: { container: '.filter-container', ok: '#f-ok', cancel: '#f-cancel', checkAll: '#f-check-all', choose: '.f-choose', name: '#filter-name' }
// });
// tableFilter.ensureButtons();
// tableFilter.updateButtonColors();
function initTableFilter(options) {
    const opts = Object.assign({
        selectors: {
            container: '.filter-container',
            ok: '#f-ok',
            cancel: '#f-cancel',
            checkAll: '#f-check-all',
            choose: '.f-choose',
            name: '#filter-name',
        },
        position: 'relative', // 'relative' | 'cursor'
        positionOffset: { top: 0, left: 0 },
    }, options || {});

    const state = opts.state; // 期望包含 filter_conditions(Map) 与 filter_sqls(Array)

    function getFilterContainer() {
        return document.querySelector(opts.selectors.container);
    }

    function buildFilterString() {
        let filter = 'AND (';
        for (const [key, value] of state.filter_conditions) {
            filter += `${value} AND (`;
        }
        filter = filter.slice(0, -6); // 去掉最后一个 ' AND ('
        return filter;
    }

    function updateButtonColors() {
        document.querySelectorAll('.filter_button').forEach(button => {
            let name = button.parentNode.textContent.trim();
            let has = false;
            for (let item of state.filter_sqls) {
                if (item.name == name) {
                    button.classList.add('red');
                    has = true;
                    break;
                }
            }
            if (!has) {
                button.classList.remove('red');
            }
        });
    }

    function setCommonEventBindingsOnce() {
        const container = getFilterContainer();
        if (!container || container.dataset.bound === '1') return;

        // 确定
        document.querySelector(opts.selectors.ok).addEventListener('click', (e) => {
            e.stopPropagation();
            e.preventDefault();

            container.style.display = 'none';

            let checked = container.querySelector(opts.selectors.choose).querySelectorAll('.form-check');
            let filter_name = document.querySelector(opts.selectors.name).textContent;
            let f_sql = '', check_now = '';

            // 全选状态
            let all_checked = document.querySelector(opts.selectors.checkAll).checked;
            if (all_checked) {
                container.querySelector(opts.selectors.choose).innerHTML = '';

                if (state.filter_sqls.length > 0 && state.filter_sqls[0].name == filter_name) {
                    state.filter_conditions.delete(filter_name);
                    state.filter_sqls.shift();
                    let filter = state.filter_sqls.length == 0 ? '' : state.filter_sqls[0].sql;

                    opts.onRefreshTable && opts.onRefreshTable(filter);
                }
                return;
            }

            checked.forEach(ch => {
                const ch_name = ch.parentNode.textContent.trim();
                if (ch.checked) {
                    f_sql += `${filter_name} = '${ch_name}' OR `;
                    check_now += `<${ch_name}>, `;
                }
            });

            if (check_now != '') {
                let f_sql2 = f_sql.slice(0, -4) + ')';
                state.filter_conditions.set(filter_name, f_sql2);
                let filter = buildFilterString();

                if ((state.filter_sqls.length == 0 || state.filter_sqls[0].name != filter_name) &&
                    check_now != '' && check_now.split(',').length != checked.length + 1) {
                    let orig = '';
                    checked.forEach(ch => {
                        orig += `${ch.parentNode.textContent.trim()}, `;
                    });

                    let sql = {
                        name: filter_name,
                        sql: filter,
                        origin: orig,
                        now: check_now,
                    };
                    state.filter_sqls.unshift(sql);
                } else if (state.filter_sqls.length > 0 && state.filter_sqls[0].name == filter_name) {
                    if (check_now == state.filter_sqls[0].origin || check_now.split(',').length == checked.length + 1) {
                        state.filter_sqls.shift();
                        filter = state.filter_sqls.length == 0 ? '' : state.filter_sqls[0].sql;
                    } else {
                        state.filter_sqls[0].sql = filter;
                        state.filter_sqls[0].now = check_now;
                    }
                }

                opts.onRefreshTable && opts.onRefreshTable(filter);
            } else {
                // 全不选的情况 => 等价于全选
                document.querySelector(opts.selectors.checkAll).click();
                document.querySelector(opts.selectors.ok).click();
            }
        });

        // 取消
        document.querySelector(opts.selectors.cancel).addEventListener('click', () => {
            container.style.display = 'none';
            container.querySelector(opts.selectors.choose).innerHTML = '';
        });

        // 全选
        document.querySelector(opts.selectors.checkAll).addEventListener('click', () => {
            let checked = document.querySelector(opts.selectors.checkAll).checked;
            container.querySelector(opts.selectors.choose).querySelectorAll('.form-check').forEach(input => {
                input.checked = !!checked;
            });
        });

        // 点击空白区域关闭 filter
        document.querySelector('body').addEventListener('click', (e) => {
            let filters = ['filter-container', 'f-title', 'f-choose', 'f-sumit', 'f-items',
                'checkmark', 'check-radio', 'form-check', 'all-choose', 'f-button'];
            if (filters.indexOf(e.target.className) == -1) {
                container.style.display = 'none';
                container.querySelector(opts.selectors.choose).innerHTML = '';
            }
        });

        // Esc 按键关闭 filter
        document.addEventListener('keydown', (event) => {
            const keyName = event.key;
            if (keyName === 'Escape') {
                container.style.display = 'none';
                container.querySelector(opts.selectors.choose).innerHTML = '';
            }
        }, false);

        container.dataset.bound = '1';
    }

    function openFilterPanel(button, e, filterName) {
        const container = getFilterContainer();
        if (!container) return;

        // 位置设置
        if (opts.position === 'cursor' && e) {
            container.style.top = (e.clientY + (opts.cursorOffsetY || 20)) + 'px';
            container.style.left = (e.clientX - (button.parentNode ? button.parentNode.clientWidth : 0) + (opts.cursorOffsetX || 20)) + 'px';
        } else {
            // relative to button
            const rect = button.getBoundingClientRect();
            container.style.top = (rect.bottom + (opts.positionOffset.top || 0)) + 'px';
            container.style.left = (rect.left + (opts.positionOffset.left || 0)) + 'px';
        }

        document.querySelector(opts.selectors.checkAll).checked = false;
        container.style.display = 'block';
        document.querySelector(opts.selectors.name).textContent = filterName;
    }

    function ensureButtons() {
        setCommonEventBindingsOnce();

        const ths = typeof opts.getThs === 'function' ? opts.getThs() : (opts.ths || []);
        // 防止重复绑定
        for (let th of ths) {
            if (th.querySelector && th.querySelector('.filter_button')) {
                return false;
            }
        }

        ths.forEach(th => {
            if (opts.has_filter.indexOf(th.textContent) != -1) {
                th.innerHTML = `${th.textContent} <button class="filter_button"><i class="fa fa-filter"></i></button>`;
                if (typeof opts.thDecorator === 'function') {
                    opts.thDecorator(th);
                }
            }
        });

        document.querySelectorAll('.filter_button').forEach(button => {
            button.addEventListener('click', function (e) {
                e.stopPropagation();
                const na = button.parentNode.textContent.trim();
                openFilterPanel(button, e, na);

                let filter_sql = '';
                if (state.filter_sqls.length > 1 && na == state.filter_sqls[0].name) {
                    filter_sql = state.filter_sqls[1].sql;
                } else if (state.filter_sqls.length > 0 && na == state.filter_sqls[0].name) {
                    filter_sql = '';
                } else if (state.filter_sqls.length == 0) {
                    filter_sql = '';
                } else if (state.filter_sqls.length > 0 && na != state.filter_sqls[0].name) {
                    filter_sql = state.filter_sqls[0].sql;
                }

                const post_data = opts.buildFetchPostData({ name: na, baseFilterSql: filter_sql });

                fetch(opts.url, {
                    method: 'post',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(post_data),
                })
                .then(response => response.json())
                .then(content => {
                    let html = '';
                    let n = 0;
                    for (let row of content) {
                        if (row.trim() == '' && n == 0) {
                            row = '(空白)';
                            n++;
                        } else if (row.trim() == '' && n == 1) {
                            continue;
                        }
                        html += `
                            <label class="check-radio">
                                <input class="form-check" type="checkbox">
                                <span class="checkmark"></span>
                                <span class="all-choose">${row}</span>
                            </label>
                        `;
                    }
                    const container = getFilterContainer();
                    container.querySelector(opts.selectors.choose).innerHTML = html;

                    // 选中已选择项
                    let now_select = [];
                    for (let item of state.filter_sqls) {
                        if (item.name.trim() == na) {
                            now_select = item.now;
                            break;
                        }
                    }
                    for (let ch of container.querySelectorAll('.form-check')) {
                        if (now_select.indexOf(`<${ch.parentNode.textContent.trim()}>`) != -1) {
                            ch.checked = true;
                        }
                    }
                });
            });
        });

        updateButtonColors();
    }

    return {
        ensureButtons,
        updateButtonColors,
        buildFilterString,
    };
}
