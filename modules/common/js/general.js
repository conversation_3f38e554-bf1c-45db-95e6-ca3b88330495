let page_general = function () {
    document.querySelector('#quick-1').onclick = () => {
        window.location.href = "/buy_in/new";
    }
    document.querySelector('#quick-2').onclick = () => {
        window.location.href = "/sale/new";
    }
    document.querySelector('#quick-3').onclick = () => {
        window.location.href = "/transport/new";
    }
    document.querySelector('#quick-4').onclick = () => {
        window.location.href = "/product_set";
    }
    document.querySelector('#quick-5').onclick = () => {
        window.location.href = "/material_in/new";
    }
    document.querySelector('#quick-6').onclick = () => {
        window.location.href = "/material_out/new";
    }
    document.querySelector('#quick-7').onclick = () => {
        window.location.href = "/stockin_items";
    }
    document.querySelector('#quick-8').onclick = () => {
        window.location.href = "/stockout_items";
    }
    document.querySelector('#quick-9').onclick = () => {
        window.location.href = "/kp/new";
    }

    // 反审单据
    document.querySelector('#anti-shen').onclick = () => {
        if (!document.querySelector('#dh')) {
            notifier.show('请先进入单据页面', 'danger');
        } else {
            let dh = document.querySelector('#dh');
            if (dh.textContent == "新单据" || document.querySelector('#remember-button').textContent == "审核") {
                notifier.show('单据还未审核', 'danger');
                return false;
            } else {
                alert_confirm("确认反审核吗？", {
                    confirmText: "确认",
                    cancelText: "取消",
                    confirmCallBack: () => {
                        fetch(`/anti_formal`, {
                            method: 'post',
                            headers: {
                                "Content-Type": "application/json",
                            },
                            body: dh.textContent,
                        })
                            .then(response => response.json())
                            .then(content => {
                                if (content != -1) {
                                    location.reload();
                                } else {
                                    notifier.show('权限不够', 'danger');
                                }
                            });
                    }
                });
            }
        }
    }
}();
