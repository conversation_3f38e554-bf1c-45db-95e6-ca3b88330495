.notifier-container {
  z-index: 9999;
  position: fixed;
  top: 65px;
  right: 0;
  padding: 0 15px;
  width: 230px;
  max-width: 98%;

  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;

  * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    box-sizing: border-box;
  }
}

.notifier {
  position: relative;
  width: 100%;
  min-height: 40px;
  margin-bottom: 12px;
  padding: 8px;
  color: white;
  border-radius: 3px;
  box-shadow: 0 1px 10px 0 rgba(0, 0, 0, 0.1), 0 2px 15px 0 rgba(0, 0, 0, 0.05);

  opacity: 0;
  -webkit-transform: translateX(100%);
  -moz-transform: translateX(100%);
  -ms-transform: translateX(100%);
  transform: translateX(100%);

  -webkit-transition: all 0.6s cubic-bezier(0.5, -0.5, 0.3, 1.4), opacity 0.6s ease;
  -moz-transition: all 0.6s cubic-bezier(0.5, -0.5, 0.3, 1.4), opacity 0.6s ease;
  -ms-transition: all 0.6s cubic-bezier(0.5, -0.5, 0.3, 1.4), opacity 0.6s ease;
  transition: all 0.6s cubic-bezier(0.5, -0.5, 0.3, 1.4), opacity 0.6s ease;

  &.info {
    background-color: rgba(57, 57, 212, 0.616);
  }

  &.success {
    background-color: green;
  }

  &.warning {
    color: red;
    background-color: yellow;
  }

  &.danger {
    background-color: #BB0100;
  }

  &.shown {
    opacity: 1;
    -webkit-transform: translateX(0);
    -moz-transform: translateX(0);
    -ms-transform: translateX(0);
    transform: translateX(0);
  }
  &：hover { 
    opacity: 1;
  }
}

.notifier-body {
  font-size: 13px;
  margin-left: 10px;
}

.notifier-close {
  position: absolute;
  top: 2px;
  right: 4px;
  width: 16px;
  height: 30px;
  padding: 0;
  color: white;
  font-weight: bold;
  font-size: 17px;
  background: transparent;
  cursor: pointer;
  opacity: 0.5;
  border: 0;

  &:hover,
  &:focus {
    opacity: 1;
  }
}