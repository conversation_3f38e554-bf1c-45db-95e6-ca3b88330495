$mini-wd: 280px;
.tree-show {
  height: 95%;
  width: $mini-wd;

  // .autocomplete {
  //   margin-bottom: 10px;
  // }

  button {
    color: white;
    background-color: $color-primary;
    margin-left: 3px;
    margin-bottom: 2px;
    vertical-align: middle;

    &:hover {
      background-color: $primary-600;
      border-color: $primary-700;
    }
  }
}

.tree-title {
  color: white;
  background-color: $color-primary;;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 20px;
  padding-bottom: 3px;
//   margin-top: 40px;
  margin-bottom: -2px;
  border: 1px solid $primary-600;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
//   cursor: pointer;

  &:hover {
    background-color: $primary-600;
  }

  a {
    color: white;
    margin-right: 20px;
    &:hover {
      color: white;
    }
  }
}

.tree-container {
  border: 1px solid $primary-400;
  border-top: 0;
  border-radius: 3px;
  height: 90%;
  overflow-y: auto;
  overflow-x: hidden;
  // max-height: 560px;
  max-width: $mini-wd;
}

#tree {
  position: relative;
  list-style-type: none;
  padding: 0 0 0 1rem;
  margin-top: 8px;
  height: 95%;

  ul {
    list-style-type: none;
    margin-left: 20px;
    padding: 0;
  }

  li {
    position: relative;
    padding: 2px 22px;
    margin: 0;
    cursor: pointer;
    width: 240px;

    input {
      position: absolute;
      top: 5px;
      font-size: 14px;
      width: 160px;
      height: $node-height * 0.99;
      z-index: 1002; //值比遮罩高一点
    }

    &::before {
      content: "";
      left: 0;
      position: absolute;
      right: auto;
      border-left: 1px dotted $primary-400;
      bottom: 50px;
      height: 100%;
      top: 0px;
      width: 10px;
    }

    &:last-child::before {
      height: 18px;
    }

    &::after {
      content: "";
      left: 0px;
      position: absolute;
      right: auto;
      border-top: 1px dotted $primary-400;
      height: 20px;
      top: 17px;
      width: 20px;
    }
  }

  .item,
  .item-down {
    user-select: none;
    vertical-align: middle;

    &:hover {
      font-weight: 600;
    }
  }

  .item::before,
  .item-down::before {
    margin-right: 6px;
    vertical-align: middle;
    display: inline-block;
  }

  .item::before {
    content: url("/assets/img/folder_blue.png");
  }

  .item-down::before {
    content: url("/assets/img/folder_blue2.png");
  }

  .nested {
    display: none;
  }

  .active {
    display: block;
  }

  .leaf {
    height: $node-height;
    overflow: visible;

    &:hover {
      color: white;
      background-color: $primary-300;
    }

    &.is-selected {
      font-weight: bold;
    }
  }

  .selected {
    color: white;
    background-color: $primary-400;
  }

  // .selected.is-selected {
  //   font-weight: normal;
  // }

  .found-color {
    color: red;
    font-style: italic;
    font-weight: bold;
  }

  // #zhezhao {
  //   width: 100%;
  //   height: 100%;
  //   -moz-opacity: 0;
  //   opacity: 0.3;
  //   left: 0;
  //   top: 0;
  //   z-index: 1000;
  //   background-color: black;
  // }
}
