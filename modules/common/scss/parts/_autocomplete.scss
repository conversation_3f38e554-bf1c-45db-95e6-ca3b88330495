.autocomplete {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 1000;

  $auto-width: 200px; //按需修改
  input {
    width: $auto-width;
  }

  button {
    margin-left: 3px;
    // margin-bottom: 2px;
  }

  span {
    position: absolute;
    top: 2px;
    right: -5px;
  }

  .autocomplete-items {
    position: absolute;
    font-size: 14px;
    width: $auto-width * 0.99;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    margin-top: -2px;

    div {
      padding: 5px;
      cursor: pointer;
      border: 1px solid rgb(219, 236, 246);
      border-top: none;
      width: $auto-width * 0.94;
      height: 18px;
      display: block !important;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
      white-space: nowrap;

      &:hover {
        color: white;
        background-color: $primary-300;
      }
    }
  }

  .autocomplete-active {
    color: white;
    background-color: $primary-400;
  }
}
