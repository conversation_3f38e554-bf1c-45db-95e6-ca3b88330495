.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    outline: 0;
    overscroll-behavior: none;
    padding: 0 !important;
    overflow: hidden;
    transition: opacity 0.15s linear;

    background: rgba(0, 0, 0, 0.3); //这种背景色和透明度设置方式，才能不会使子元素也透明，起到遮罩作用

    input,
    select {
        width: 300px;
        border-color: #e5e5e5;
    }

    input {
        padding: 0 8px;
        height: 30px;
    }

    .form-group {
        display: flex;
        align-items: center;
    }
    .check-radio {
        margin-top: -17px;
    }

    .table-top {
        height: 30px;
    }

    .autocomplete {
        position: relative;
        $auto-width: 215px; //按需修改
        input {
            width: $auto-width;
            height: 30px;
        }

        .autocomplete-items {
            width: $auto-width * 0.998;
            top: 33px;
            left: 0;

            div {
                width: $auto-width * 0.94;
            }
        }
    }

    .tree-title {
        margin-top: 11px;
    }

    .table-customer {
        overflow-x: auto;
    }

    .form-out {
        padding-top: 20px;
        padding-left: 20px;
    }

    .fields-out {
        margin-bottom: 25px;
        &:hover {
            font-weight: bold;
            color: #1a5176;
        }

        input {
            width: 200px;
        }

        input:checked + span {
            font-weight: bold;
            color: #235779;
        }
    }

    .table-address {
        td {
            text-overflow: unset;
        }
        .check-radio {
            top: 0;
            padding-left: 0;
        }
        .checkmark {
            position: relative;
            display: block;
            top: 5px;
        }
    }
}

.modal-dialog {
    position: relative;
    width: auto;
    pointer-events: none;
    transform: none;
    transition:
        transform 0.3s ease-out,
        -webkit-transform 0.3s ease-out;
    -webkit-transform: translate(0, -50px);
    max-height: calc(100% - 3.5rem);
    max-width: 500px;
    margin: 1.75rem auto;
    margin-top: 130px;

    opacity: 1;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
    max-height: calc(100vh - 150px);
}

.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-bottom: 1px solid #dee2e6;
    // border-bottom-color: rgb(222, 226, 230);
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
    flex-shrink: 0;
    background-color: #eff3f8;
    border-bottom-color: #e4ebf2;
    padding: 3px 20px;
    cursor: move;

    .modal-title {
        font-size: 14px;
        // color: #106cb6 !important;
        margin-bottom: 8px;
        line-height: 1.5;
    }
    .close {
        float: right;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
        color: #000;
        text-shadow: 0 1px 0 #fff;
        opacity: 0.5;
        background-color: transparent;
        border: 0;
        cursor: pointer;
        padding: 5px 16px;
        margin: 1px -1rem -1rem auto;

        &:hover {
            opacity: 1;
        }
    }
}

.modal-body {
    position: relative;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #9dbad5 #dce3ea;
    overscroll-behavior: none;

    ol {
        list-style-type: none;
        counter-reset: sectioncounter;
        width: 90%;
        padding-left: 30px;
        margin-top: 0;

        li {
            margin: 5px 0;

            &:before {
                content: counter(sectioncounter) "、";
                counter-increment: sectioncounter;
            }
        }
    }
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-top-color: rgb(222, 226, 230);
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
    border-top-color: #e4e9ee;
    background-color: #eff3f8;
    flex-shrink: 0;

    button {
        margin: 0 10px;
    }
}

#modal-info {
    margin-right: 60px;
    color: $color-danger;
}
