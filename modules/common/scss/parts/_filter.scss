.filter-container {
    position: fixed;
    display: none;
    flex-direction: column;
    width: 200px;
    height: 350px;
    background-color: #f3f3f3;

    .f-title {
        font-size: 16px;
        padding-top: 10px;
        padding-left: 30px;
        padding-bottom: 10px;
        margin-bottom: 10px;
        border-bottom: 1px solid #e8e8e8;

        label:hover {
            cursor: pointer;
        }
    }
    .f-choose {
        display: flex;
        flex-direction: column;
        height: 230px;
        background-color: white;
        margin: 8px;
        padding: 5px;
        overflow-y: auto;

        label {
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            padding-left: 0;
            
            &:hover {
                background-color: $table-hover-color;
                cursor: pointer;
            }
            
            .checkmark {
                position: relative;
                top: 0;
                left: 0;
                margin-right: 8px;
                flex-shrink: 0;
            }
            
            .all-choose {
                line-height: 16px;
                vertical-align: middle;
            }
        }
    }
    .f-sumit {
        float: right;
        margin: 5px 20px 0 20px;
        button {
            padding-left: 10px;
            padding-right: 10px;
        }
    }

    #f-ok {
        margin-left: 20px;
    }
}