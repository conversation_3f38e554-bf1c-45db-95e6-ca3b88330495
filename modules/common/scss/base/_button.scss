button {
    cursor: pointer;
    border-radius: 4px;

    &.btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        vertical-align: middle;
        -webkit-user-select: none;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1.125rem;
        font-size: 0.8125rem;
        line-height: 1.47;
        transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
            box-shadow 0.15s ease-in-out, -webkit-box-shadow 0.15s ease-in-out;

        color: white;
        background-color: $color-primary;
        border-color: $color-primary;
        box-shadow: 0 2px 6px 0 rgba($color-primary, 0.5);

        &:hover {
            background-color: $primary-600;
            border-color: $primary-700;
            text-decoration: none;
        }
    }

    &.btn-primary {
        color: white;
        background-color: $color-primary;
        border-color: $color-primary;
        box-shadow: 0 2px 6px 0 rgba($color-primary, 0.5);

        &:hover {
            background-color: $primary-600;
            border-color: $primary-700;
            text-decoration: none;
        }
    }

    &.btn-info {
        color: white;
        background-color: $color-primary;
        border-color: $color-primary;
        box-shadow: 0 2px 6px 0 rgba($color-primary, 0.5);

        &:hover {
            background-color: $primary-600;
            border-color: $primary-700;
            text-decoration: none;
        }
    }

    &.btn-second {
        color: white;
        background-color: $color-danger;
        border-color: $color-danger;
        box-shadow: 0 2px 6px 0 rgba($color-danger, 0.5);

        &:hover {
            background-color: $danger-700;
            border-color: $danger-700;
            text-decoration: none;
        }
    }

    &.btn-third {
        color: white;
        background-color: rgb(222, 51, 51);
        border-color: rgb(222, 51, 51);
        box-shadow: 0 2px 6px 0 rgba($color-danger, 0.5);

        &:hover {
            background-color: rgb(200, 51, 51);
            border-color: rgb(222, 51, 51);
            text-decoration: none;
        }
    }

    &.btn-normal {
        height: 30px;
        font-size: 0.9rem;
        padding: 3px 20px;
    }

    &.btn-sm {
        height: 30px;
        font-size: 0.9rem;
        padding: 0.25rem 0.5rem 0.275rem;
    }

    &:disabled {
        background-color: $primary-50;
        border: none;
        cursor: default;
        &:hover {
            background-color: $primary-50;
        }
    }
}
