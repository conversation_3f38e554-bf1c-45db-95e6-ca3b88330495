@use 'sass:color';
//颜色变量
// $color-primary: #2574a9;
//  $color-primary: #25a946;
 $color-primary: #0077C2;
$color-success: #1dc9b7;
$color-info: #2493ea;
$color-warning: #ffc241;
$color-danger: #F5844B;
$color-fusion: color.adjust(color.adjust(color.adjust($color-primary, $hue: 5deg), $saturation: -80%), $lightness: -25%);

$body-background: #f3f3f3;
$div-background: white;
$top-line-color: #e2e2e2;
// $form-border-color: color.adjust(color.adjust($color-primary, $saturation: -60%), $lightness: 30%);
$form-border-color: #e2e2e2;

$header-grade-color: rgba(51, 148, 225, 0.18);
$header-shadow-color: rgba(0, 0, 0, 0.13);
// $page-bg: color.adjust(color.adjust($color-primary, $lightness: 41.7%), $saturation: -5%) !default;
$base-text-color: color.adjust(white, $lightness: -70%) !default;
$menu-hover-color: color.adjust($color-primary, $lightness: 55%) !default;

$editable-color: tomato;
$table-hover-color: color.adjust($color-primary, $lightness: 50%) !default;
$table-focus-color: color.adjust($color-primary, $lightness: 42%) !default;
$table-border-color: $form-border-color;

$primary-50: color.adjust($color-primary, $lightness: 25%) !default;
$primary-100: color.adjust($color-primary, $lightness: 20%) !default;
$primary-200: color.adjust($color-primary, $lightness: 15%) !default;
$primary-300: color.adjust($color-primary, $lightness: 10%) !default;
$primary-400: color.adjust($color-primary, $lightness: 5%) !default;
$primary-500: $color-primary !default;
$primary-600: color.adjust($color-primary, $lightness: -5%) !default;
$primary-700: color.adjust($color-primary, $lightness: -10%) !default;
$primary-800: color.adjust($color-primary, $lightness: -15%) !default;
$primary-900: color.adjust($color-primary, $lightness: -20%) !default;

/* the emeralds */
$success-50: color.adjust($color-success, $lightness: 25%) !default;
$success-100: color.adjust($color-success, $lightness: 20%) !default;
$success-200: color.adjust($color-success, $lightness: 15%) !default;
$success-300: color.adjust($color-success, $lightness: 10%) !default;
$success-400: color.adjust($color-success, $lightness: 5%) !default;
$success-500: $color-success !default;
$success-600: color.adjust($color-success, $lightness: -5%) !default;
$success-700: color.adjust($color-success, $lightness: -10%) !default;
$success-800: color.adjust($color-success, $lightness: -15%) !default;
$success-900: color.adjust($color-success, $lightness: -20%) !default;

/* the amethyths */
$info-50: color.adjust($color-info, $lightness: 25%) !default;
$info-100: color.adjust($color-info, $lightness: 20%) !default;
$info-200: color.adjust($color-info, $lightness: 15%) !default;
$info-300: color.adjust($color-info, $lightness: 10%) !default;
$info-400: color.adjust($color-info, $lightness: 5%) !default;
$info-500: $color-info !default;
$info-600: color.adjust($color-info, $lightness: -5%) !default;
$info-700: color.adjust($color-info, $lightness: -10%) !default;
$info-800: color.adjust($color-info, $lightness: -15%) !default;
$info-900: color.adjust($color-info, $lightness: -20%) !default;

/* the topaz */
$warning-50: color.adjust($color-warning, $lightness: 25%) !default;
$warning-100: color.adjust($color-warning, $lightness: 20%) !default;
$warning-200: color.adjust($color-warning, $lightness: 15%) !default;
$warning-300: color.adjust($color-warning, $lightness: 10%) !default;
$warning-400: color.adjust($color-warning, $lightness: 5%) !default;
$warning-500: $color-warning !default;
$warning-600: color.adjust($color-warning, $lightness: -5%) !default;
$warning-700: color.adjust($color-warning, $lightness: -10%) !default;
$warning-800: color.adjust($color-warning, $lightness: -15%) !default;
$warning-900: color.adjust($color-warning, $lightness: -20%) !default;

/* the rubies */
$danger-50: color.adjust($color-danger, $lightness: 25%) !default;
$danger-100: color.adjust($color-danger, $lightness: 20%) !default;
$danger-200: color.adjust($color-danger, $lightness: 15%) !default;
$danger-300: color.adjust($color-danger, $lightness: 10%) !default;
$danger-400: color.adjust($color-danger, $lightness: 5%) !default;
$danger-500: $color-danger !default;
$danger-600: color.adjust($color-danger, $lightness: -5%) !default;
$danger-700: color.adjust($color-danger, $lightness: -10%) !default;
$danger-800: color.adjust($color-danger, $lightness: -15%) !default;
$danger-900: color.adjust($color-danger, $lightness: -20%) !default;

$fusion-50: color.adjust($color-fusion, $lightness: 25%) !default;
$fusion-100: color.adjust($color-fusion, $lightness: 20%) !default;
$fusion-200: color.adjust($color-fusion, $lightness: 15%) !default;
$fusion-300: color.adjust($color-fusion, $lightness: 10%) !default;
$fusion-400: color.adjust($color-fusion, $lightness: 5%) !default;
$fusion-500: $color-fusion !default;
$fusion-600: color.adjust($color-fusion, $lightness: -5%) !default;
$fusion-700: color.adjust($color-fusion, $lightness: -10%) !default;
$fusion-800: color.adjust($color-fusion, $lightness: -15%) !default;
$fusion-900: color.adjust($color-fusion, $lightness: -20%) !default;

//设备尺寸
$grid-breakpoints: (
    xs: 380px,
    sm: 576px,
    md: 768px,
    lg: 992px,
    xl: 1399px,
);

//内距
$p-1: 0.25rem;
$p-2: 0.5rem;
$p-3: 1rem;
$p-4: 1.5rem;
$p-5: 2rem;

//表单
$input-height: calc(2.25rem + 1px);
$input-border-color: #e5e5e5;
$input-focus-border-color: $primary-300;
$input-btn-focus-color: transparent;
$input-padding-y: 0.5rem;
$input-padding-x: 0.875rem;
$label-margin-bottom: 0.3rem;
$form-group-margin-bottom: 1.5rem;

//link
$link-color: $primary-500;
$link-hover-color: $primary-400;

// checkbox
$check-roadio-backround: #eee;
$check-roadio-backround-hover: $primary-300;
$check-roadio-mark: white;
$check-roadio-checked: $primary-700; // #7a59ad;

//边框圆角
$border-radius: 4px;
$border-radius-plus: 8px;

//button 内距
$input-btn-padding-y-sm: 0.375rem;
$input-btn-padding-x-sm: 0.844rem;
$input-btn-padding-y: 0.5rem;
$input-btn-padding-x: 1.125rem;
$input-btn-padding-y-lg: 0.75rem;
$input-btn-padding-x-lg: 1.5rem;

//表格
// $table-hover-bg: color.adjust(color.adjust($primary-900, $saturation: -70%), $lightness: 63%);
// $table-accent-bg: rgba($fusion-500, 0.02);

//下拉菜单
// $dropdown-border-width: $theme-border-utility-size;
$dropdown-padding-y: 0.3125rem;
$dropdown-item-padding-y: 0.75rem;
$dropdown-item-padding-x: 1.5rem;
$dropdown-link-active-bg: color.adjust($primary-50, $lightness: 13%);
$dropdown-link-active-color: $primary-900;
$dropdown-link-hover-color: $primary-700;

$dropdown-xl-width: 21.875rem !default;
$dropdown-lg-width: 17.5rem !default;
$dropdown-md-width: 14rem !default;
$dropdown-sm-width: 8rem !default;
$dropdown-shadow: 0 0 15px 1px rgba(color.adjust($primary-900, $saturation: -20%), calc(20/100));

//字体尺寸
$h1-font-size: 1.5rem;
$h2-font-size: 1.375rem;
$h3-font-size: 1.1875rem;
$h4-font-size: 1.0625rem;
$h5-font-size: 0.9375rem;
$h6-font-size: 0.875rem;

$fs-base: 14px !default;
$fs-nano: $fs-base - 2; /* 11px   */
$fs-xs: $fs-base - 1; /* 12px   */
$fs-sm: $fs-base - 0.5; /* 12.5px */
$fs-md: $fs-base + 1; /* 14px   */
$fs-lg: $fs-base + 2; /* 15px   */
$fs-xl: $fs-base + 3; /* 16px   */
$fs-xxl: $fs-base + 15; /* 28px   */

$fs-menu: 0.925rem;

//树
$node-height: 23px;
