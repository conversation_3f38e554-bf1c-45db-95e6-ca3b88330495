.inout-top {
    height: 110px;
    padding: 6px 8px;
    display: flex;
    justify-content: space-between;

    .fields-show {
        padding-top: 3px;
        margin-top: 10px;
        display: flex;
        align-items: first baseline;
        flex-wrap: wrap;
        overflow-y: auto;
        overflow-x: hidden;
        margin-left: -15px;

        // .form-group {
        //     display: flex;
        //     justify-content: center;
        // }

        .form-label {
            width: 80px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .form-input {
            display: inline-block;
        }

        .input-sm {
            margin-top: -2.6px;
        }

        .select-sm {
            margin-top: -3px;
        }
    }

    .buy-buttons {
        width: 290px;
        margin-bottom: 8px;

        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex-shrink: 0;

        .buttons {
            margin-bottom: 6px;
        }

        button {
            padding: 4px 9px;
            margin: 4px 2px;
        }
    }

    .has-dh {
        margin-top: 6px;
        margin-left: 6px;
        font-size: 18px;
        font-weight: bold;
        color: $fusion-700;
    }

    #dh {
        font-size: 15px;
    }

    #supplier-input {
        width: 200px;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    #supplier-serach {
        margin-top: -2px;
        margin-left: -9px;
        height: 28px;
        // border-radius: 3px;

        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        // border-top-right-radius: 3px;
    }

    // .has-auto {
    //     position: absolute;
    //     // + div {
    //     //     margin-left: 410px;
    //     // }
    // }
}
