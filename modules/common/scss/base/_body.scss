a,
a:active,
a:focus,
button,
button:focus,
button:active,
.btn,
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn.focus:active,
.btn.active.focus {
  outline: none;
  outline: 0;
}

input::-moz-focus-inner {
  border: 0;
}

input {
  outline: none;
}

html,
body {
  height: 100%;
}

html {
  scroll-behavior: smooth;

  body {
    background: $body-background;
    font-size: $fs-base;
    padding: 0;
    margin: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    a {
      color: $link-color;
      text-decoration: none;
      background-color: transparent;

      &:hover {
        color: $link-hover-color;
        text-decoration: none;
      }
    }
  }
}

.content {
  width: 96%;
  height: 0%;
  flex: 1;
}

.has-bak {
  background-color: $div-background;
  border-radius: 8px;
  padding-top: 8px;
}