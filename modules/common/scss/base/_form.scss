input {
    border-radius: 4px;
    &:focus {
        border: 1px solid $color-primary;
    }
}

.form-control {
    font-size: $fs-base;
    height: calc(1.47em + 1rem + 2px);
    padding: 0.5rem 0.875rem;
    font-weight: 400;
    line-height: 1.47;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid $form-border-color;
}

.form-group {
    margin-bottom: 15px;

    label,
    input {
        margin-right: 6px;
    }
}

.form-label {
    display: inline-block;
    width: 110px;
    text-align: right;
}

.check-label {
    cursor: pointer;
}

.input-sm {
    height: 20px;
    padding: 3px 5px;
    width: 90%;
    // text-align: center;
    // border: 1px solid $info-50;
    &:focus {
        border-color: $color-primary;
    }
    &:disabled {
        border-color: #e8e8e8;
    }
}

.select-sm {
    font-size: $fs-base;
    height: 28px;
    width: 100%;
    border: 1px solid $form-border-color;
    border-radius: 4px;
    background-color: white;
    outline: none;
    padding-left: 6px;

    option:hover {
        background: #666;
    }
    &:focus {
        border-color: $color-primary;
    }
}
