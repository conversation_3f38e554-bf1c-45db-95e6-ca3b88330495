.header {
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    flex-direction: row;
    height: 50px;
    width: 100%;
    background-image: linear-gradient(270deg, $header-grade-color, transparent);
    background-color: $primary-800;
    box-shadow: 0 1rem 3rem $header-shadow-color;
}

#logo {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 40px;

    a {
        display: flex;
        flex-direction: row;
        align-items: center;

        img {
            margin-right: 8px;
        }

        #title {
            font-size: 16px;
            color: white;
        }

        &:hover {
            font-weight: bold;

            #title {
                color: $menu-hover-color;
            }
        }
    }
}

#menu {
    display: flex;
    flex: 1;
    flex-direction: row;
    justify-content: space-between;
    margin-left: 30px;
}

.menu-ul {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    text-align: center;
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-li {
    width: auto;
    position: relative;
    padding: 0;
    margin: 0 0 0 30px;

    a {
        padding-left: 0.375rem;
        padding-right: 0.375rem;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-shadow: none;
        text-decoration: none;
        white-space: nowrap;
        transition: background-color 150ms, color 150ms;
    }
}

.caret {
    font-size: 18px;
    font-weight: 900;
    opacity: 0.92;
    display: none;
    position: absolute;
    margin-left: 15px;
    margin-top: -13px;
    transition: transform 150ms, color 150ms, -webkit-transform 150ms;
}

.sub-menu {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 30px;
    margin-left: 6px;
    margin-top: 0;
    display: none;
    height: auto !important;
    box-shadow: 2px 1px 2px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #d1d1d1;
    border-radius: 0.375rem;
    // animation: 1s navItemHidden;
    z-index: 9999;
    transition: margin 150ms, opacity 150ms, visibility 0ms;
    transition-delay: 600ms, 600ms, 750ms;
    background-color: white;
}

.menu-link {
    color: white;
}

.menu-li:hover {
    .sub-menu {
        display: block;
    }

    .nav-icon,
    .menu-text {
        font-weight: bold;
        color: $menu-hover-color;
    }

    .caret {
        color: white;
        display: block;
    }
}

.sub-ul {
    list-style: none;
    text-align: left;
    padding-top: 10px;
    padding-bottom: 15px;
    border-width: 0 !important;
    margin-left: -36px;
    overflow: visible;
}

.nav-item {
    position: relative;
    padding: 3px 20px;
    margin-top: -5px;
    margin-left: -4px;

    &:not(:last-child) {
        margin-bottom: 7px;
    }
    &:hover {
        font-weight: bold;
        background-color: $primary-700;
        .nav-text {
            color: white;
        }
    }
}

.nav-icon {
    margin-right: 5px;
    margin-left: 0.125em;
    width: 1.25rem;
    color: white;
    font-size: 1.25em;
    transition: color 150ms;
}

.menu-text,
.nav-text {
    font-size: $fs-menu;
    font-weight: 500;
}

.nav-text {
    color: $fusion-700;
}

#user {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 30px;
}

#user-name {
    font-size: $fs-menu;
    color: white;
    margin-right: 20px;
    padding-bottom: 2px;

    &:hover {
        font-weight: bold;
        color: $menu-hover-color;
    }
}

#exit {
    font-size: 16px;
    margin-left: 4px;
    color: $menu-hover-color;

    &:hover {
        .nav-icon {
            font-weight: bold;
            color: $menu-hover-color;
        }
    }
}

.show-chosed {
    font-weight: bold;
    color: $menu-hover-color;
}

.top-title {
    font-size: 16px;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-bottom: 10px;
    border-bottom: 1px solid $top-line-color;

    .t1 {
        font-size: 14px;
    }

    i {
        margin-left: 5px;
        margin-right: 5px;
    }

    p {
        font-size: 14px;
        float: right;
        padding: 0;
        margin-right: 10px;
        margin-top: 0;
        cursor: pointer;
    }
}
