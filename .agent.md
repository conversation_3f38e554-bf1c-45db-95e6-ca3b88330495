# 项目开发说明

## 项目概况
- 业务领域：石油设备材料行业的仓储销售管理系统（ERP）

## 技术栈
- 后端：Rust (web 框架： Actix-web，html 模板框架：ructe (https://github.com/samlink/ructe.git))
- 前端：原生 JavaScript + HTML/CSS
- 样式：SCSS
- 数据库：Postgresql

## 项目结构
- `/assets/js/pages/` - 页面特定的 JavaScript 文件
- `/assets/js/parts/` - 通用组件和工具函数
- `/assets/js/parts/service.js` - 共享函数
- `/assets/js/parts/tools.js` - 共享工具函数
- `/assets/js/plugins/` - 项目中使用的第三方插件
- `/src/` - Rust 后端代码
- `/templates/` - HTML 模板文件
- `/scss/pages/` - 页面相关的 scss 文件
- `/scss/base/` - 通用和基础变量设置的 scss 文件
- `/scss/parts/` - 组件的专用样式表 scss 文件

## 主要业务模块
- 用户管理 (user.rs, usermanage.js) 
- 产品管理 (product.rs, productset.js) 
- 客户供应商管理 (customer.rs, customer.js) 
- 库存管理 (rk.rs, ck.rs, stockin.js, stockout.js)
- 销售管理 (buysale.rs, sale.js)
- 采购管理 (buysale.rs, buy.js) 
- 价格管理 (price.rs, price.js)
- 统计分析 (statis.rs, 首页图表展示)
- 运输管理 (fh.rs, transport.js)  

## 常用开发命令
- SCSS 编译： scss/min.sh (自定义脚本, 使用 watch 模式的命令：sass --watch --style compressed ./:../static --no-source-map)
- JavaScript 编译为静态文件： assets/js/min.sh (自定义脚本，使用 terser 命令)
- 编译构建：`cargo build` （编译构建项目前，须先完成 SCSS 和 Javascript 的编译）
- 运行项目：`cargo run`

## 重要提醒
- **不要读取/修改 `/static` 目录中的内容**：这些文件都是自动生成的静态文件

## 开发注意事项
- 弹出框：使用 `/assets/js/parts/modal.js` 中的定义，并保证每个使用 modal 的页面须调用一次 modal_init()
- 通知：使用 `/assets/js/parts/notifier.js` 中的定义
- 确认框：使用 `/assets/js/parts/alert.js` 中的定义
- 导出功能参考 `productinfo.js` 的实现方式
- 下载文件：使用 `tools.js 中的 download_file()` 的定义