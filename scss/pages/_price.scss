.price-manage {
    .customer-content {
        display: flex;
        gap: 20px;

        .table-tools {
            margin-left: 35px;
        }

        .customer-search {
            width: 300px;
        }

        #search-input {
            width: 150px;
        }

        #search-input-customer {
            width: 100px;
        }

        #search-customer-button,
        #serach-button {
            width: 50px;
        }

        .table-top {
            justify-content: space-between;
            align-items: center;
        }

        .table-price {
            td,
            th {
                border: none;
                cursor: default;
            }
        }

        .table-items {
            td,
            th {
                cursor: default;
            }
        }

        .autocomplete {
            z-index: unset;
        }
    }

    #price-show {
        width: 430px;
    }

    .table-container {
        // max-height: 500px;
        overflow-y: auto;
    }

    #category-table {
        width: 100%;
        border-collapse: collapse;

        thead {
            position: sticky;
            top: 0;
            background-color: #fff;
            z-index: 1;

            th {
                border-bottom: 1px solid #eee;
                background-color: #fff;
            }
        }

        tbody {
            tr {
                &:hover {
                    background-color: #f5f5f5;
                }
            }
        }
    }
}

.modal-content {
    #price-form,
    #product-info-form {
        .form-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;

            label {
                width: 80px;
                text-align: right;
                margin-right: 10px;
                flex-shrink: 0;
            }

            input.form-control,
            select.select-sm {
                width: 220px;
                box-sizing: border-box;
                height: 30px;
                line-height: 30px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            select.select-sm {
                padding: 0 5px;
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
                background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/></svg>");
                background-position: right 5px center;
                background-repeat: no-repeat;
                padding-right: 25px;
            }

            .form-label {
                width: 80px;
                text-align: right;
                margin-right: 10px;
                flex-shrink: 0;
            }

            input.form-control:focus {
                border-color: #0077c2;
                outline: 0;
            }
        }
    }

    #product-info-form {
        .form-group {
            label {
                width: 100px;
            }

            .form-label {
                width: 100px;
            }
        }
    }

    // 历史价格图表样式
    .history-chart-container {
        padding: 20px;

        .chart-header {
            text-align: center;
            margin-bottom: 20px;

            h4 {
                margin: 0 0 5px 0;
                font-size: 18px;
                font-weight: bold;
                color: #333;
            }

            .chart-subtitle {
                margin: 0;
                font-size: 14px;
                color: #666;
            }
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-top: 10px;

            #priceHistoryChart {
                max-width: 100%;
                max-height: 100%;
            }
        }
    }
}
