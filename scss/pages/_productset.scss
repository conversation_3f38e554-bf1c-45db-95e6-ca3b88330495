.product-content {
    display: flex;
    align-items: flex-start;
    padding-top: 10px;
    width: 100%;
    overflow-x: hidden;

    .tree-show {
        flex-grow: 0;
        flex-shrink: 0;
        height: calc(100% - 226px);
    }

    .tree-container {
        height: calc(100vh - 268px);
    }

    #product-show {
        flex-grow: 1;
        margin-left: 20px;
        width: calc(100% - 370px);
        .table-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .product-search {
            display: flex;
            align-items: center;
            input {
                width: 300px;
            }
        }
    }

    .table-product {
        overflow-x: auto;
    }

    .product-select {
        display: flex;
        align-items: center;
        select {
            width: 90px;
            height: 30px;
            margin-right: 20px;
        }
    }

    .filter_button {
        width: 25px;
        height: 25px;
        float: right;
        margin-right: 5px;
        background-color: rgb(229, 248, 253);
        border: 1px solid $table-border-color;
        &:hover {
            background-color: white;
        }
    }

    .table-tools {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 500px;
    }

    .table-edit {
        min-width: 270px;
        button {
            margin-left: 10px;
        }
    }

    .data-button {
        // margin-left: 100px;
        margin-right: 20px;
        min-width: 300px;

        button {
            margin-left: 10px;
        }
    }

    #product-name {
        font-weight: bold;
        position: relative;
        margin-left: 30px;
        top: 0;
    }

    #product-id {
        position: relative;
        font-weight: normal;
        color: rgb(242, 246, 243);
        top: 0;
        margin-left: 10px;
    }

    td:nth-child(2),
    th:nth-child(2) {
        border-left: none !important;
    }

    table th {
        padding: 0;

        .red {
            background-color: #f8c8c8;
        }
    }

    .autocomplete {
        .autocomplete-items {
            width: 230px;

            div {
                width: 215px;
            }
        }
    }
}
