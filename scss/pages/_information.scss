.information {
    #infor-show {
        margin: auto;
        width: 50%;
        .title {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            input {
                width: 95%;
            }
            span {
                width: 40px;
                font-weight: bold;
            }
        }

        .content {
            display: flex;
            width: 100%;
            margin-bottom: 10px;

            textarea {
                width: 100%;
                font-size: 14px;
                outline: none;
                padding: 5px 15px;
                line-height: 1.5;
                border: 1px solid $input-border-color;
                border-radius: 4px;
                white-space: pre-wrap;

                &:focus {
                    border: 1px solid $color-primary;
                }
            }
            span {
                width: 40px;
                font-weight: bold;
            }
        }
        
        .sumit-button {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-bottom: 5px;

            button {
                margin-left: 20px;
                margin-right: 10px;
            }
        }
    }
}