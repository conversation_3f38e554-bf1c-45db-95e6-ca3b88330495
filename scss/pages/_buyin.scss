@use 'sass:color';
.buy-in {
  .info-model {
    height: 25px;
    width: 100%;
    margin-right: 10px;
    margin-bottom: 3px;
    display: flex;
  }

  #supplier-info {
    width: 100%;
    color: darkgreen;
    padding: 3px 10px;
  }

  #history-info {
    width: 360px;
    height: 25px;
    padding-top: 3px;
    padding-left: 10px;
    // background-color: color.adjust($color-info, $lightness: 42%);
  }

  #buy-cate {
    margin-left: 15px;
    cursor: pointer;
  }

  .button-buy {
    margin-right: 20px !important;
    width: 100px;
  }

  .button-sale {
    margin-right: 10px !important;
    min-width: 60px;
  }

  .remembered {
    color: green;
    background-color: white;
    border-color: green;
    cursor: default;

    &:hover {
      background-color: transparent;
    }
  }

  #owner {
    font-size: smaller;
    font-weight: normal;
    margin-left: 10px;
  }

  .button-shen {
    height: 30px;
    padding: 4px 9px;
    margin-left: 10px;
  }

  .info-and-choose {
    margin-top: 5px;
    margin-bottom: 5px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  #choose-div {
    width: 180px;
    margin-left: 10px;
  }

  .buy-items {
    display: flex;
  }

  .table-items {
    overflow: auto;
    margin-right: 12px;

    & > table {
      thead,
      tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
        overflow-x: hidden;
      }

      th {
        cursor: default;
        // border: none;
      }

      tbody {
        display: block;
        overflow-x: hidden;
        overflow-y: auto;

        tr {
          height: 32px;

          td:nth-child(1) {
            text-align: center;
          }

          &:hover {
            border-bottom: 1.5px solid $color-primary;
            background-color: transparent;
          }
        }
      }

      td {
        padding-top: 0;
        padding-bottom: 0;
        text-overflow: unset;
      }
    }

    input {
      border: none;

      &:focus {
        background-color: #fffcc8;

        & + button {
          color: blue;

          &:hover {
            cursor: pointer;
            box-shadow: 0 2px 6px 0 rgba(33, 150, 243, 0.5);
          }
        }
      }
    }

    select {
      border: none;
    }

    .td-check {
      top: -12px;
      left: 8px;
    }

    .auto-edit {
      position: absolute;
      z-index: 0;
    }

    .product-search-button {
      width: 20px;
      height: 28px;
      font-weight: bold;
      margin-left: -1px;
      padding: 0;
      background-color: transparent;
      color: transparent;
      border: 0;
      box-shadow: none;
      cursor: default;
    }

    .inputting {
      .editable {
        border-bottom: 1px solid $editable-color;
      }
    }

    .autocomplete {
      margin-left: 2px;

      input {
        width: 100%;
      }
    }

    .position {
      .autocomplete-items {
        width: 130px;

        div {
          width: 115px;
        }
      }
    }

    .table-auto {
      table {
        width: auto;
        border: 1px solid $primary-300;
      }

      thead {
        tr {
          &:hover {
            border-bottom: 0;
          }
        }

        th {
          height: 28px;
          color: white;
          background-color: $primary-600;
          border: none;
        }
      }

      tbody {
        height: 300px;
        background-color: white;
        overflow-y: auto;

        tr {
          height: 28px;
          cursor: pointer;

          td {
            border: none !important;
          }

          td:nth-child(1) {
            text-align: left;
          }

          &:hover {
            border-left: 0;
            background-color: color.adjust($color-primary, $lightness: 32%);
            border-bottom: none;
          }
        }

        .autocomplete-active {
          color: black;
          background-color: color.adjust($color-primary, $lightness: 32%);
        }
      }
    }
  }

  .table-button {
    margin-left: 20px;

    button {
      margin-left: 10px;
    }
  }

  #sum-money {
    min-width: 200px;
    text-align: right;
  }

  #print-choose {
    width: 98%;
  }

  .table-history {
    width: 320px;
    margin-left: 5px;
    flex-grow: 1;
    height: 100%;

    th {
      cursor: default;
    }

    tr {
      height: 32px;
      cursor: pointer;
    }

    td {
      border: none;
    }

    td:nth-child(1) {
      text-align: center;
    }

    .table-info {
      width: 100%;
      text-align: center;
      padding: 0;
    }
  }

  .autocomplete {
    position: relative;
    margin-left: -6.5px;
    $auto-width: 276px; //按需修改
    input {
      width: $auto-width;
    }

    .autocomplete-items {
      width: $auto-width * 0.998;
      top: 28px;
      left: 0;

      div {
        width: $auto-width * 0.94;
      }
    }

    .table-auto {
      top: 290px;
      left: 450px;
      position: fixed;
    }
  }

  .ku_danger {
    background-color: rgb(248, 248, 199);
  }

  .right-tables {
    display: flex;
    flex-direction: column;
    margin-right: 10px;
    margin-left: 5px;
  }

  .table-note {
    height: 200px;
    width: 320px;
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;

    th:hover {
      cursor: default;
    }

    tr:hover {
      cursor: pointer;
    }

    td {
      border: none;
    }

    tbody p {
      padding-left: 10px;
    }
  }

  // .ch-width {
  //   width: auto;
  //   min-width: 60px;
  // }
}

.table-product .has-choosed {
  width: 0;
}