.material {
    .fields-show {
        padding-left: 15px;
    }

    .table-history {
        margin-right: 10px;
    }

    .table-items {
        margin-right: 0;
    }

    .table-head {
        display: flex;
        flex-wrap: wrap;
    }

    .material-doc {
        display: flex;
        flex-wrap: wrap;
        margin-left: 18px;
        align-items: baseline;
    }

    .material-value {
        margin-right: 10px;
        border-top: 0;
        border-left: 0;
        border-right: 0;
        border-radius: 0;
    }

    #lu_button {
        margin-left: -8px;
        margin-right: 8px;
    }

    .check-radio .checkmark {
        top: -14px;
    }

    .autocomplete {
        position: relative;
        margin-left: 1px;

        $auto-width: 210px; //按需修改
        input {
            width: $auto-width;
        }

        .autocomplete-items {
            width: $auto-width * 0.998;
            top: 28px;
            left: 0;

            div {
                width: $auto-width * 0.94;
            }
        }
    }

    .left-tables {
        display: flex;
        flex-direction: column;
        overflow-y: hidden;
    }

    .table-save {
        height: 280px;
        width: 260px;
        border-top: none;
        border-top-left-radius: 0;
        border-top-right-radius: 0;

        th:hover {
            cursor: default;
        }

        tr:hover {
            cursor: pointer;
        }

        td {
            border: none;
        }

        tbody {
            height: 230px !important;
            p {
                padding-left: 10px;
            }
        }
    }

    .table-history,
    .table-docs {
        width: 320px;

        tbody {
            tr {
                cursor: pointer;
            }
            td {
                text-align: left;
                border: none;
                // border-: none;
                // border-bottom: none;
            }
        }
    }

    .documents-show {
        display: flex;
        //flex-direction: column;
        // width: 600px;
        margin-left: 5px;
        margin-right: 10px;

        div {
            // width: 340px;
            margin-left: 0;
            margin-right: 0;
        }

        .table-docs {
            width: 260px;
            margin-right: 10px;
            flex-grow: 1;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;

            tbody {
                height: calc(100vh - 630px);
            }

            td {
                text-align: center;
            }
        }
        tbody {
            display: block;
            padding-left: 10px;
            height: calc(100vh - 350px);
            overflow-y: auto;
        }
        .has-bak {
            background-color: $table-hover-color;
        }
    }

    .show-pic2 {
        border: 1px solid #b0b4b7;
        height: 42px;
        width: 60px;
        margin-top: 40px;
        margin-right: 20px;
        cursor: pointer;
    }

    .yellow {
        background-color: rgb(235, 235, 137);
    }

    .buy-buttons button {
        min-width: 56px;
        margin-right: 6px;
    }
}
