.customer-content {
    height: 95%;
    padding-top: 10px;

    .customer-search {
        width:400px;
    }

    #serach-button {
        width: 60px;
    }

    #cate-select {
        width: 90px;
        margin-left: 30px;
    }

    .table-customer {
        overflow-x: auto;
    }

    .table-tools {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-left: 30%;
    }

    .table-edit {
        min-width: 300px;
        button {
            margin-left: 10px;
        }
    }

    .data-button {
        margin-left: 60px;
        margin-right: 20px;
        min-width: 300px;

        button {
            margin-left: 10px;
        }
    }

    .autocomplete-items,
    #search-input {
        width: 260px;
    }

    .autocomplete-items div {
        width: 249px;
    }

    td:nth-child(2),
    th:nth-child(2) {
        border-left: none !important;
    }

    table th {
        padding: 0;
    }
}
