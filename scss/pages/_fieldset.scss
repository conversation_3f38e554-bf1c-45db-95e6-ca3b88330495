.fields-set {
    .system-content {
        display: flex;
        padding-top: 10px;
    }
    #product-show {
        width: 68%;
    }

    #inout-show {
        width: 30%;
        margin-left: 30px;
    }

    .table-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30px 0 10px;
    }

    .table-title {
        font-size: 15px;
        margin-left: 10px;
    }

    #table-choose {
        width: 200px;
        height: 30px;
        border-color: #e5e5e5;
        &:hover {
            border-color: $primary-100;
        }
    }

    .cate-title {
        margin-left: -40%;
    }

    table thead,
    tbody tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

    table thead {
        width: inherit;
        -webkit-width:calc(100% - 1.07em);
        
        th {
            cursor: default;
        }
    }

    table tbody {
        display: block;
        height: calc(100vh - 320px);
        overflow-x: hidden;
        overflow-y: scroll;

        td {
            text-overflow: unset;
        }

        input {
            width: 90%;
        }

        td:nth-child(2),
        td:nth-last-child(1) {
            text-align: center;
        }

        tr:hover {
            background-color: transparent;
            border-bottom: 1.5px solid $primary-400;
        }
    }

    .table-button {
        display: none;
    }

    .checkmark {
        top: -14px;
    }

    .table-tip {
        margin-left: 20px;
    }

    .table-inout tr {
        height: 32.75px;
    }

    #choose-info {
        position: fixed;
        top: 40%;
        left: 24%;
        width: 200px;
        height: 60px;
        font-size: large;
        display: flex;
        align-items: center;
        padding: 0 15px;
        color: $color-danger;
        background-color: antiquewhite;
        text-align: center;
    }
}
