#print,
#print-tag {
    font-size: 13px;

    table tr {
        height: 20px;
    }

    table td,
    table th {
        font-weight: normal;
        color: black;
        vertical-align: middle;
        text-align: center;
        font-size: 13px;
    }

    .print-title {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        //text-align: center;
        margin-bottom: 10px;

        img {
            width: 25px;
            margin-right: 10px;
        }
    }

    .print-header {
        display: flex;
        justify-content: space-between;
        padding-left: 10px;
        padding-right: 10px;
    }

    .print-table {
        table-layout: fixed;
        text-overflow: unset;

        border: 1.5px solid black;
        border-radius: 0;        

        th,
        td {
            border: 1px solid black;
            white-space: unset !important;
            overflow-wrap: break-word;
        }

        .sum-cell {
            td {
                border-bottom: none;
            }
        }
    }
    .no-bottom {
        td {
            border-bottom: none;
        }
    }

    .print-foot {
        width: 80%;
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        padding-left: 10px;
    }
}

#print-tag {
    .print-table {
        page-break-after: always;
    }
}

// @media print {
//     .print-table {
//         page-break-after: always;
//     }
// }
