.techbuy {
    .top-title {
        padding-top: 0;
    }
    
    #fei-pic {
        position: fixed;
        top: 110px;
        right: 40px;
    }

    .techbuy-form {
        background: $div-background;
        border: 1px solid $form-border-color;
        border-radius: $border-radius;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .form-section {
            .form-row {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: 15px;
                align-items: center;

                .form-group {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    margin-bottom: 0;

                    .form-label {
                        width: 100px;
                        text-align: right;
                        margin-right: 15px;
                        font-weight: 500;
                        color: $base-text-color;

                        label {
                            margin: 0;
                        }
                    }

                    .form-input {
                        flex: 1;

                        .form-control {
                            width: 100%;
                            border: 1px solid $form-border-color;
                            border-radius: $border-radius;
                            padding: 8px 12px;
                            font-size: $fs-base;
                            transition: border-color 0.3s ease;

                            &:focus {
                                border-color: $color-primary;
                                box-shadow: 0 0 0 2px rgba($color-primary, 0.1);
                                outline: none;
                            }
                        }

                        .select-sm {
                            height: 36px;
                        }
                    }
                }
            }
        }

        #product-type {
            width: 120px;
        }

        #material {
            width: 100px;
        }

        #spec-name {
            width: 240px;
        }

        #spec-note {
            width: 210px;
        }

        #build-date {
            width: 80px;
        }
    }

    #pass-button {
        box-shadow: 0 2px 6px 0 rgba(15, 146, 61, 0.5);
    }

    #reject-button {
        box-shadow: 0 2px 6px 0 rgba(245, 132, 75, 0.5);
    }

    #supplier-list {
        display: flex;
        // flex-direction: column;
        gap: 30px;
        margin-left: 60px;
    }

    // 响应式设计
    @media (max-width: 768px) {
        .techbuy-form {
            .form-section .form-row {
                flex-direction: column;
                gap: 15px;

                .form-group {
                    width: 100%;

                    .form-label {
                        width: auto;
                        text-align: left;
                        margin-right: 0;
                        margin-bottom: 5px;
                    }
                }
            }

            .button-section {
                justify-content: center;
            }
        }

        .techbuy-editor {
            .editor-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;

                .editor-toolbar {
                    width: 100%;
                    justify-content: center;
                    flex-wrap: wrap;
                }
            }

            .editor-footer {
                flex-direction: column;
                gap: 10px;
                align-items: center;
            }
        }
    }
}
