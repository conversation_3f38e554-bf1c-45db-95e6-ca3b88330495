.home-page {
  .home-content {
    display: flex;
    flex-direction: row;
    padding: 10px 0 30px 20px;
    height: calc(100vh - 210px);
  }

  #help-info {
    display: flex;
    align-items: center;
  }

  .show-chart {
    display: flex;
    flex-direction: column;
    width: 50%;
    margin-bottom: 80px;
  }

  .chart {
    height: 300px;
    margin-left: 20px;
    margin-right: 20px;
  }

  #chart-sale {
    margin-bottom: 70px;
  }

  .show-tip {
    display: flex;
    flex-direction: column;
    width: 50%;
    margin-top: 10px;
    h4 {
      font-size: 18px;
    }
  }

  #show-01,
  #show-02 {
    display: flex;
  }

  .word {
    h3 {
      font-size: 16px;
      margin-top: 14px;
    }
  }

  .tip {
    display: flex;
    align-items: center;
    color: white;
    height: 45px;
    border-radius: 4px;
    margin-right: 15px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;

    i {
      display: block;
      font-size: 20px;
      // margin-top: 14px;
      margin-right: 6px;
      margin-left: 20px;
      opacity: 0.6;
    }
  }

  .show-div {
    height: 50%;

    .show-first {
      min-width: 230px;
      max-width: 230px;
      .reminder {
        padding-left: 10px;
        border-right: none;
        border-bottom-left-radius: 4px;
      }
    }

    .show-second {
      width: 100%;
      .reminder {
        border-left-style: dashed;
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;

        padding-left: 10px;
        margin-left: -16px;
      }
    }

    #todo-first {
      .tip {
        background-color: #6ab8f7;
        cursor: default;
      }

      .reminder {
        border-top-color: #6ab8f7;
        padding-left: 10px;
      }
    }

    #shen-first {
      .tip {
        background-color: #eb72aa;
        // background-color: #34c6b8;
        cursor: default;
      }
      // .reminder {
      //   border-top-color: #34c6b8;
      // }
    }

    .reminder {
      height: 80%;
      border: 1px solid #d7d7d7;
      border-radius: 0;
      padding-top: 5px;
      margin-right: 15px;
      overflow-y: auto;

      table {
        tr {
          &:hover {
            cursor: pointer;
          }
        }
        td {
          text-align: left;
          border: 0;
        }
      }
    }

    .red0 {
      color: rgb(188, 47, 47);
      font-weight: bold;
    }

    .red1 {
      color: rgb(188, 47, 47);
    }
  }
}
