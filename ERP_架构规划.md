# 企业 ERP 模块化架构规划（基于现有进销存项目）

本规划旨在将当前“进销存系统”升级为类似 Odoo 的模块化企业 ERP 平台，充分复用现有技术栈与代码资产（Rust + Actix-web + ructe + 原生 JS + PostgreSQL）。目标是在保持单体部署的开发效率基础上，逐步演进为“模块化单体（Modular Monolith）”，并为未来的服务化与扩展留出清晰路径。

---

## 1. 目标与原则
- 可插拔模块：功能按模块交付，支持按需启用/禁用与依赖管理。
- 统一内核：统一的安全、鉴权、菜单、日志、配置、事件总线、工作流、审计、租户管理能力。
- 一致开发体验：保留 Actix + ructe + 原生 JS 的栈，抽象统一接口，降低学习成本。
- 渐进式重构：先“模块化单体”，再按业务边界松耦合，最终可选服务化（必要时）。
- 可测试与可观测：模块级单元/集成测试、审计日志与指标、结构化日志。

---

## 2. 总体架构图（逻辑）

- Core（内核）
  - 配置中心、数据库连接池、缓存（可选）、RBAC 权限、用户与组织、菜单导航、事件总线、审计日志、工作流引擎、模块注册与生命周期管理、模板与静态资源管线。
- Modules（功能模块）
  - 基础数据：产品、分类、单位、仓库、价格表
  - 业务模块：采购、销售、库存、财务（凭证/应收应付/固定资产）、CRM、项目、生产、物流、HR ...
- Web 接入层
  - Actix 路由注册（模块化 scope）、API（REST/JSON）、页面模板（ructe）、静态资源。
- 数据存储层
  - PostgreSQL（多租户、RLS）、模块化迁移脚本、审计/事件表。

---

## 3. 目录与工程结构（Cargo Workspace）

采用 Workspace + 多 crate 的模块化单体结构：

```
/ (workspace root)
├─ Cargo.toml                 # workspace 定义
├─ core/                      # 内核 crate（公共能力）
│  ├─ src/
│  │  ├─ config.rs
│  │  ├─ db.rs                # PgPool 管理、事务封装
│  │  ├─ rbac.rs              # 角色/权限模型与校验
│  │  ├─ auth.rs              # 登录、会话、JWT（可选）
│  │  ├─ tenant.rs            # 多租户支持（RLS/隔离）
│  │  ├─ events.rs            # 事件总线（in-proc）
│  │  ├─ workflow.rs          # 简单工作流引擎与状态机
│  │  ├─ audit.rs             # 审计/操作日志
│  │  ├─ menu.rs              # 模块菜单注册与聚合
│  │  ├─ module.rs            # Module Trait & 注册器
│  │  ├─ templates.rs         # ructe 集成（扫描模块模板）
│  │  └─ web.rs               # Actix 公共中间件、响应包装
│  └─ build.rs                # 统一模板编译（聚合 modules/*/templates）
├─ web/                       # Web 入口（actix-web 启动）
│  └─ src/main.rs
├─ modules/
│  ├─ base/                   # 基础数据（用户、组织、客户、供应商、产品、分类、单位）
│  ├─ stock/                  # 库存（入库/出库/调拨/盘点/RFID 扩展）
│  ├─ purchase/               # 采购（请购/订购/收货/发票/应付）
│  ├─ sales/                  # 销售（报价/订单/发货/发票/应收）
│  ├─ pricing/                # 价格与折扣策略
│  ├─ logistics/              # 运输与发运（现有 fh.rs 迁移）
│  ├─ finance/                # 财务（凭证、应收应付、固定资产）
│  ├─ crm/                    # 客户关系
│  ├─ project/                # 项目管理
│  └─ ...
├─ templates/                 # 过渡期：保留，逐步迁移到各模块内
├─ assets/                    # 过渡期：前端资源（逐步模块化）
└─ scripts/                   # 编译、迁移、发布脚本
```

说明：
- 短期内保持“单进程单二进制”部署，所有模块静态链接入主程序，使用模块注册器解耦。
- 长期可将某些模块独立为服务（通过 HTTP/gRPC），由网关或反向代理统一暴露。

---

## 4. 模块系统设计

### 4.1 模块接口（Rust Trait）

```rust
pub trait Module: Send + Sync {
    fn name(&self) -> &'static str;
    fn version(&self) -> semver::Version;
    fn dependencies(&self) -> &'static [ModuleDependency];

    // 依赖注入/服务注册（如仓储、用例、定时任务）
    fn register_services(&self, c: &mut ServiceContainer);

    // 路由注册（Actix）
    fn configure_routes(&self, cfg: &mut actix_web::web::ServiceConfig);

    // 权限定义
    fn permissions(&self) -> &'static [Permission];

    // 菜单项
    fn menu(&self) -> &'static [MenuItem];

    // 数据迁移（SQL 或代码）
    fn migrations(&self) -> &'static [Migration];

    // 启动与关闭生命周期（可选）
    fn on_start(&self, _ctx: &AppContext) {}
    fn on_stop(&self) {}
}
```

- ModuleDependency: { name: &'static str, min_version: VersionReq }
- Permission: { key: "module:resource:action", label: i18n_key }
- MenuItem: { key, label, path, icon, required_permissions: [] }
- Migration: 包含 up/down 脚本或函数指针，按模块独立维护。

### 4.2 模块清单（Manifest）

参考 Odoo `__manifest__.py` 的思路，使用 `module.toml`：

```toml
name = "sales"
version = "0.1.0"
display_name = "销售管理"
description = "报价、订单、发货、发票、应收"
dependencies = ["base>=0.1.0", "pricing>=0.1.0"]
```

- 运行时由内核扫描 `modules/*/module.toml` 聚合元数据。
- 通过配置文件或数据库控制启用/禁用模块。

### 4.3 路由与模板注册
- 每个模块提供 `configure_routes`，在主 `App` 中以 `scope("/mod/sales")` 挂载。
- ructe 模板编译：内核 `build.rs` 扫描 `modules/*/templates/**/*.rs.html`，统一生成模板绑定代码，命名空间包含模块名避免冲突。

### 4.4 前端资源模块化
- 目录：`modules/<mod>/assets/js/pages/*.js`，`modules/<mod>/scss/pages/*.scss`。
- 统一打包脚本：扫描各模块 assets，输出到 `/static/<mod>/...`（仍遵守现有“static 为生成目录”的约束）。
- 页面级 JS 初始化规范：每个页面导出 `init()`，在模板中按需引入。

---

## 5. 核心能力设计（Core）

### 5.1 鉴权与 RBAC
- 用户、角色、权限三层模型。
- 权限粒度：`module:resource:action`（如 `sales:order:create`）。
- Actix 中间件：解析会话/JWT，注入 `UserContext`；路由 handler 使用宏或装饰器校验权限。

### 5.2 多租户（Tenant）
- 策略一（推荐）：列级隔离 + PostgreSQL RLS（Row Level Security）。
  - 所有业务表包含 `tenant_id`，开启 RLS，登录后设置 `set local app.tenant_id = ...`。
  - 示例策略：`USING (tenant_id = current_setting('app.tenant_id')::uuid)`。
- 策略二：Schema per tenant（复杂度高，迁移与资源消耗较大，按需考虑）。

### 5.3 事件总线（Domain Events）
- 轻量 in-process 事件调度（同步/异步），模块间解耦。
- 事件类型示例：`ProductCreated`, `StockMoved`, `SalesOrderConfirmed`。
- 后续可无缝升级到外部消息系统（NATS/Kafka）以实现跨进程集成。

### 5.4 工作流引擎
- 通用状态机：草稿 -> 审核中 -> 已批准 -> 已拒绝 -> 归档。
- 支持自定义流程定义（JSON/YAML），节点权限、条件、回退与加签。
- 将关键单据（采购单、销售单、出入库、发票）流程统一抽象，模块按需绑定。

### 5.5 审计与日志
- 审计：关键操作写入 `audit_log`（actor, action, resource, payload, ts）。
- 数据级审计：重要表使用触发器记录变更历史（history tables）。
- 日志：结构化日志（tracing），附带 `tenant_id`, `user_id`, `module`。

### 5.6 配置与字典
- 系统配置中心：模块可注册配置项（键空间按模块划分）。
- 业务字典/编码规则：统一编码器（如单据编号规则、流水号），模块可扩展。

### 5.7 报表与导出
- 统一导出接口（CSV/Excel/PDF），前端复用 `tools.js` 的下载能力。
- 报表模板化（ructe + 表格），复杂报表可接入服务端 PDF 渲染（wkhtmltopdf/typst）。

---

## 6. 数据库设计与迁移

### 6.1 模块化迁移
- 每个模块维护自己的迁移（SQL 脚本或 `refinery/sea-orm/SQLx` 嵌入）。
- 内核在启动时按依赖顺序执行迁移；记录在全局表 `schema_migrations`（含 module、version、checksum）。

### 6.2 命名约定
- 表名：`<module>_<entity>`（如 `sales_order`）。
- 公共列：`id`, `tenant_id`, `created_at`, `updated_at`, `created_by`, `updated_by`。
- 软删：按需 `deleted_at`；严肃财务数据建议禁用软删，仅允许冲正/作废。

### 6.3 性能与索引
- 针对高频查询建立组合索引（如 `(tenant_id, status, created_at)`）。
- 大表分区（按 `tenant_id` 或 `created_at`）。

---

## 7. Web/API 设计

- API 风格：REST（短期），为复杂查询预留 GraphQL 网关（长期可选）。
- 路由：`/api/<module>/...` 与 `/web/<module>/...`（页面渲染）。
- 统一响应包装：`{ code, message, data }`。
- 错误处理：统一错误类型 -> HTTP 状态码映射。
- 静态资源：仍通过 `/static` 提供（由 assets 管线生成，不直接手工修改 `/static`）。

---

## 8. 前端模块化与规范

- JS 目录：`modules/<mod>/assets/js/pages/*.js` 与 `modules/<mod>/assets/js/parts/*.js`。
- SCSS 目录：`modules/<mod>/scss/pages/*.scss` 与 `modules/<mod>/scss/parts/*.scss`。
- 构建脚本：
  - 继续使用 `assets/js/min.sh` 与 `scss/min.sh` 的思路，扩展为遍历模块目录构建到 `/static/<mod>/...`。
  - 保持页面初始化 `init()` 约定；Modal/Notifier/Alert 按现有 parts 实现统一调用。
- i18n：引入简单的字典表（JSON），按模块维护；页面模板与 JS 通过 key 取词。

---

## 9. 安全与合规

- 统一认证（可选 JWT/Session），HTTPS 部署，CSRF 防护（表单 token）。
- RBAC 权限与数据域（租户、组织、仓库）叠加控制。
- 审计与留痕满足财务合规要求（特别是冲红、作废、审批流）。

---

## 10. 监控与可观测

- tracing/metrics：为核心路径与关键操作打点；暴露 Prometheus 指标。
- 请求日志与慢查询日志；错误聚合（Sentry 可选）。

---

## 11. 渐进式迁移计划（基于现有代码）

阶段 0：梳理与冻结
- 盘点现有模块与代码：`src/` 下 user.rs、product.rs、customer.rs、rk.rs、ck.rs、buysale.rs、price.rs、statis.rs、fh.rs、tech.rs 等。
- 保持现有功能稳定，不对 `/static` 做直接修改。

阶段 1：内核与模块抽象
- 新建 `core` crate：抽出 db、rbac、auth、tenant、events、audit、module 注册器、统一错误与响应。
- 新建 `web` crate：作为二进制入口（Actix App），加载模块并装配路由与中间件。
- 引入 `Module` Trait 与注册器；实现 `base` 模块（用户/组织/客户/供应商/产品）。

阶段 2：模板与资源模块化
- ructe build.rs 扩展：扫描 `modules/*/templates`；模板命名空间按模块区分。
- 前端资源调整：将部分页面 JS/SCSS 移动到对应模块目录，通过构建脚本写入 `/static/<mod>`。

阶段 3：业务模块迁移
- 将 `buysale.rs` 拆分为 `purchase` 与 `sales`；`rk.rs/ck.rs` 整合至 `stock`；`fh.rs` 至 `logistics`；`price.rs` 至 `pricing`。
- 迁移对应模板与页面 JS（如 `sale.js`, `stockin.js` 等）至模块目录。

阶段 4：公共能力深化
- 加入工作流引擎、审计日志、事件总线对齐；梳理单据审批与联动（出库触发库存与应收，应付与发票联动等）。
- 引入简单报表与导出统一接口。

阶段 5：多租户与性能
- 按推荐方案开启 RLS；为大表加索引与分区；压测与调优。

阶段 6：对外接口与生态
- 规划公共 API、Webhook、外部系统对接（财税、WMS、物流）。
- 预留服务化改造路径（网关、消息总线）。

---

## 12. 关键技术实现要点

- ructe 模板聚合：在 `core/build.rs` 中递归扫描 `modules/*/templates` 并调用 ructe 生成器，生成 `templates.rs` 供各模块使用。
- 依赖注入：简单 ServiceContainer（TypeMap）或使用轻量 DI（shaku），结合 `AppContext` 传递。
- 事务与一致性：对跨模块事务采用 Saga/事件最终一致（如销售确认 -> 生成出库任务 -> 异步扣减库存）。
- 任务/定时：tokio 定时任务或接入任务队列（apalis）。
- 数据访问：继续使用现有 SQL 习惯；建议逐步采用 SQLx（异步）或 SeaORM（按需）。

---

## 13. 权限与菜单示例

- 权限：
  - `sales:order:create` / `sales:order:view` / `sales:order:approve` / `sales:order:cancel`
  - `stock:move:create` / `stock:move:confirm`
- 菜单（层级）：
  - 销售管理
    - 报价单
    - 销售订单
    - 发货单
    - 发票与应收

---

## 14. 配置与发布

- 配置：`.env` + 启用模块列表（`APP_MODULES=sales,stock,purchase,...`）。
- 构建：先构建 SCSS/JS（模块化脚本），再 cargo 构建。
- 部署：单二进制 + 静态资源目录；数据库迁移自动执行。

---

## 15. 风险与缓解

- Rust 动态插件复杂：短期坚持“静态链接模块 + 注册器”，避免不必要的动态加载。
- 模板与资源迁移成本：采用增量迁移，保留兼容层，优先迁销售/库存等核心模块。
- 多租户与 RLS：严格测试，提供租户隔离回退开关（仅列过滤）。

---

## 16. 里程碑与验收

- M1（2-4 周）：完成 core/web 骨架、Module Trait、base 模块、ructe 聚合构建、资产管线雏形。
- M2（4-6 周）：迁移销售/库存/采购模块，权限与菜单生效，完成主要页面迁移。
- M3（3-5 周）：接入工作流、审计日志、统一导出；多租户（RLS）灰度开启。
- M4（2-4 周）：性能优化、报表完善、对外 API；形成第一版模块化 ERP 可用交付。

---

## 17. 附：与现有代码的映射建议

- `src/user.rs` -> modules/base（用户、角色、组织）
- `src/product.rs` -> modules/base（产品、分类、单位）
- `src/customer.rs` -> modules/base 或拆为 base + crm（客户/供应商）
- `src/rk.rs` + `src/ck.rs` -> modules/stock（入库/出库/调拨）
- `src/buysale.rs` -> modules/purchase + modules/sales
- `src/price.rs` -> modules/pricing（价格与折扣）
- `src/fh.rs` -> modules/logistics（运输/发运）
- `src/statis.rs` -> 报表与统计（可做 cross-module 报表服务）
- `src/tech.rs` -> 视业务并入生产或技术支持模块

---

以上规划在不改变核心技术栈的前提下，提供模块化单体的架构路径，兼顾短期可落地与长期可演进。后续可根据业务优先级选择迁移顺序，并在关键节点进行回归测试与文档沉淀。

- 创建代码脚手架：按规划新建 core/web/modules 的目录与基础代码骨架（Module Trait、注册器、ructe 扫描脚本、模块 manifest 等）
- 拆分首批模块：优先把销售/库存/采购从现有 src 中抽离为 modules/sales、modules/stock、modules/purchase 的初版 
