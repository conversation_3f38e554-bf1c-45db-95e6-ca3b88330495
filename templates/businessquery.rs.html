@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<link rel="stylesheet" href="/assets/plugins/laydate/theme/default/laydate.css">

<div class="docment-query business_query">
    <div class="top-title"><span><i class="fa fa-table"></i> 业务报表 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 业务往来 </span>
        <p id="auto_cate" hidden></p>
    </div>
    <div class="query-content has-bak">
        <div id="query-show">
            <div class="table-top">
                <div class="query-search">
                    <div class="search-div">
                        <input type="text" class="form-control search-input" id="search-fields" placeholder="模糊搜索" />
                        <input type="text" class="form-control search-input date-input" id="search-date1"
                            placeholder="起始日期">
                        _
                        <input type="text" class="form-control search-input date-input" id="search-date2"
                            placeholder="终止日期">
                        <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                    </div>
                </div>
                <div class="data-button">
                    <button class="btn btn-info btn-sm" id="data-out">导出数据</button>
                </div>
            </div>

            <div class="table-container table-documents" id="table-business">
                <table>
                    <thead>
                        <tr>
                            <th width="4%">序号</th>
                            <th>日期</th>
                            <th width="6%">单号</th>
                            <th width="10%">客户名称</th>
                            <th>合同编号</th>
                            <th>类别</th>
                            <th>单据金额</th>
                            <th>商品名称</th>
                            <th width="7%">材质</th>
                            <th width="7%">规格</th>
                            <th width="9%">状态</th>
                            <th width="4%">长度</th>
                            <th width="4%">数量</th>
                            <th width="4%">价格</th>
                            <th width="4%">重量</th>
                            <th width="10%">备注</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        金额共：<span id="other-info"></span> 元， 共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="/assets/plugins/laydate/laydate.js"></script>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@businessquery_js.name"></script>
}, user.show.clone())