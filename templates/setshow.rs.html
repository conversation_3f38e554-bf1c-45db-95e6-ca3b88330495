@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="set-show">
    <div class="top-title"><span><i class="fa fa-wrench"></i> 功能设置 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 商品显示 </span>
    </div>
    <div class="system-content has-bak">
        <div class="tree-show">
            <div class="autocomplete table-top">
                <input type="text" class="form-control search-input" id="auto_input" placeholder="商品搜索">
                <button id="auto_search" class="btn btn-info btn-sm"><img src="/assets/img/zoom.png" width=24></button>
            </div>
            <div class="tree-title">商品显示设置</div>
            <div class="tree-container">
                <ul id="tree"></ul>
                <div>
                    <ul id="context-menu">
                        <li><a href="javascript:;" id="context-add">新增</a></li>
                        <li><a href="javascript:;" id="context-edit">编辑</a></li>
                    </ul>
                </div>

                <div id="zhezhao"></div>
            </div>
        </div>
        <div class="table-tools">
            <button class="btn btn-info btn-sm" id="submit-button">保存设置</button>
        </div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@setshow_js.name"></script>
}, user.show.clone())