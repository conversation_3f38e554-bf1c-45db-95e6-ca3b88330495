@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData, setup: Vec<&str>)

    @:base_html({
    <link rel="stylesheet" href="/assets/plugins/laydate/theme/default/laydate.css">

    <div class="buy-in material stock">
        <div class="top-title"><span><i class="fa fa-retweet"></i> 仓储管理 </span>
            <span class="t1" id="document-bz"><i class="fa fa-angle-double-right"></i> @setup[0] </span>
        </div>
        <div class="buy-content">
            <div class="inout-top has-bak" style="width: 99%;">
                <div class="fields-show">
                    <div class="table-head"></div>
                </div>
                <div class="buy-buttons">
                    <div class="has-dh">№: <span id="dh">@setup[3]</span> <span id="owner"></span></div>
                    <div class="buttons">
                        <button class="btn btn-info btn-sm" id="save-button">保存</button>
                        <button class="btn btn-info btn-sm" id="remember-button">审核</button>
                    </div>
                </div>
            </div>
            <div class="buy-items has-bak">
                <div class="table-container table-history" hidden="hidden">
                </div>
                <div class="table-container table-items not-scroll-left">
                    <table>
                        <thead>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <div class="table-ctrl">
                        <div class="table-button">
                            <button class="btn btn-info btn-sm" id="row-insert" title="插入"><i
                                    class="fa fa-plus-square"></i></button>
                            <button class="btn btn-info btn-sm" id="row-del" title="删除"><i
                                    class="fa fa-trash"></i></button>
                            <button class="btn btn-info btn-sm" id="row-up" title="上移"><i
                                    class="fa fa-arrow-up"></i></button>
                            <button class="btn btn-info btn-sm" id="row-down" title="下移"><i
                                    class="fa fa-arrow-down"></i></button>
                        </div>

                        <div class="table-tips" id="sum-money">

                        </div>

                        <div class="table-info">
                            共 <span id="total-records"></span> 条记录
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/assets/plugins/laydate/laydate.js"></script>
    <script src="/static/@tools_service_js.name"></script>
    <script src="/static/@stockin_js.name"></script>
    }, user.show.clone())