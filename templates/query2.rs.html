@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData, menu: &str, cate: &str, database:&str, limit: &str)

@:base_html({
<div class="docment-query">
    <div class="top-title"><span><i class="fa fa-search"></i> @menu </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> @cate </span>
        <span id="category" hidden>@cate</span>
        <span id="base" hidden>@database</span>
        <span id="limit" hidden>@limit</span>
    </div>
    <div class="query-content">
        <div id="query-show" class="has-bak">
            <div class="table-top">
                <div class="query-search">
                    <input type="text" class="form-control search-input" id="search-input" placeholder="文本字段模糊搜索">
                    <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                </div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="edit-button">编辑查阅</button>
                        @if user.duty == "总经理" {
                        <button class="btn btn-info btn-sm" id="del-button">删除单据</button>
                        }
                    </div>
                </div>
            </div>

            <div class="table-container table-documents">
                <table>
                    <thead>
                        <tr>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@query2_js.name"></script>
}, user.show.clone())