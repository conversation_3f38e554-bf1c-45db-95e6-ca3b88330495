@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<link rel="stylesheet" href="/assets/plugins/laydate/theme/default/laydate.css">

<div class="customer-set price-manage">
    <div class="top-title"><span><i class="fa fa-cubes"></i> 基础信息 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 价格体系 </span>
    </div>
    <div class="customer-content has-bak">
        <div id="price-show">
            <div class="table-top">
                <div class="autocomplete customer-search">
                    <input type="text" class="form-control search-input" id="search-input-customer" placeholder="名称搜索">
                    <button class="btn btn-info btn-sm" id="search-customer-button">搜索</button>
                </div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="add-customer-button">增加</button>
                        <button class="btn btn-info btn-sm" id="edit-customer-button">编辑</button>
                        <button class="btn btn-info btn-sm" id="del-customer-button">删除</button>
                    </div>
                </div>
            </div>
            <div class="table-container table-price">
                <table>
                    <thead>
                        <tr style="border-bottom: 1px solid #eee;">
                            <th hidden>id</th>
                            <th>客户协议名称</th>
                            <th>开始执行日期</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        <div id="customer-show">
            <div class="table-top">
                <div class="autocomplete customer-search">
                    <input type="text" class="form-control search-input" id="search-input" placeholder="综合搜索">
                    <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                </div>
                <div id="customer-name"></div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="add-button">增加</button>
                        <button class="btn btn-info btn-sm" id="edit-button">编辑</button>
                        <button class="btn btn-info btn-sm" id="del-button">删除</button>
                    </div>
                    <div class="data-button">
                        <button class="btn btn-info btn-sm" id="history-button">历史价格</button>
                        <input type="file" id="choose_file" class="hide" accept="xls,xlsx" />
                        <!-- <button class="btn btn-info btn-sm" id="data-in">批量导入</button> -->
                        <button class="btn btn-info btn-sm" id="data-out">导出数据</button>
                    </div>
                </div>
            </div>

            <div class="table-container table-items">
                <table>
                    <thead>
                        <tr>
                            <th hidden>id</th>
                            <th width="5%">序号</th>
                            <th width="10%">类别</th>
                            <th width="6%">名称</th>
                            <th width="15%">钢种</th>
                            <th width="10%">规格</th>
                            <th width="15%">状态</th>
                            <th width="12%">产地</th>
                            <th width="6%">整支单价</th>
                            <th width="6%">切分单价</th>
                            <th width="6%">锯料费</th>
                            <th width="10%">备注</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="/assets/plugins/laydate/laydate.js"></script>
<script src="/assets/plugins/chart/Chart2.min.js"></script>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@price_js.name"></script>
}, user.show.clone())