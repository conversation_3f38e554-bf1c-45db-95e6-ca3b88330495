@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="customer-set price-manage">
    <div class="top-title"><span><i class="fa fa-cubes"></i> 基础信息 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 商品类别 </span>
    </div>
    <div class="customer-content has-bak">
        <div id="price-show" style="margin: 0 auto;">
            <div class="table-top">
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="add-button">增加</button>
                        <button class="btn btn-info btn-sm" id="edit-button">编辑</button>
                    </div>
                    <div class="say" style="margin-left: -50px; color: #c72c1e;">可拖动排序</div>
                </div>
            </div>
                <div class="table-container table-price">
                <table id="category-table">
                    <thead>
                        <tr style="border-bottom: 1px solid #eee;">
                            <th hidden>id</th>
                            <th width="15%">顺序</th>
                            <th width="70%">类别名称</th>
                        </tr>
                    </thead>
                    <tbody id="sortable-list">
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@category_js.name"></script>
}, user.show.clone())