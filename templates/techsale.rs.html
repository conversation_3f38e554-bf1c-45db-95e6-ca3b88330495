@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData, cate: String)

@:base_html({
<link rel="stylesheet" href="/assets/plugins/laydate/theme/default/laydate.css">
<link rel="stylesheet" href="/assets/plugins/ckeditor5/style.css">
<link rel="stylesheet" href="/assets/plugins/ckeditor5/ckeditor5.css">

<div class="techbuy techsale">
    <div class="top-title"><span><i class="fa fa-cubes"></i> 基础信息 </span>
        <span class="t1" id="document-bz"><i class="fa fa-angle-double-right"></i>客户技术规范</span>
    </div>
    <p id="document-id" hidden>@cate</p>
    <div id="fei-pic" hidden><img src="/assets/img/fei.png"></div>

    <div class="techbuy-form has-bak">
        <div class="form-section">
            <div class="form-row">
                <div class="form-group">
                    <div class="form-label">
                        <label for="customer">协议来源</label>
                    </div>
                    <div class="form-input autocomplete" style="z-index: 900;">
                        <input class="form-control input-sm" type="text" id="customer" placeholder="请输入客户名称" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">
                        <label for="product-type">产品类型</label>
                    </div>
                    <div class="form-input">
                        <select class="form-control select-sm" id="product-type">
                            <option value="">请选择产品</option>
                            <option value="圆钢">圆钢</option>
                            <option value="无缝钢管">无缝钢管</option>
                            <option value="圆钢-无缝钢管">圆钢-无缝钢管</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">
                        <label for="material">牌号</label>
                    </div>
                    <div class="form-input autocomplete" style="z-index: 900;">
                        <input class="form-control input-sm" type="text" id="material" placeholder="请输入牌号" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">
                        <label for="spec-name">规范名称</label>
                    </div>
                    <div class="form-input">
                        <input class="form-control input-sm" type="text" id="spec-name" placeholder="请输入规范名称" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">
                        <label for="spec-number">规范编号</label>
                    </div>
                    <div class="form-input">
                        <input class="form-control input-sm" type="text" id="spec-number" placeholder="请输入编号" />
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label">
                        <label for="build-date">日期</label>
                    </div>
                    <div class="form-input">
                        <input class="form-control input-sm" type="text" id="build-date" />
                    </div>
                </div>
            </div>
        </div>

        <div class="main-container">
            <!-- 编辑器容器 -->
            <div class="editor-container editor-container_document-editor editor-container_include-fullscreen"
                id="editor-container">
                <div class="editor-container__menu-bar" id="editor-menu-bar"></div>
                <div class="editor-container__toolbar" id="editor-toolbar"></div>
                <div class="editor-container__editor-wrapper">
                    <div class="editor-container__editor">
                        <div id="editor"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="editor-footer">
            <div class="editor-actions">
                <div id="customer-pdf">
                    暂未上传协议
                </div>
                <div id="save-dom" hidden></div>
            </div>
            <div class="editor-actions" style="margin-left: auto;">
                <button class="btn btn-info btn-sm" id="save-button">
                    <i class="fa fa-save"></i> 保存
                </button>
                <button class="btn btn-info btn-sm" id="uppdf-button">
                    <i class="fa fa-book"></i> 上传协议
                </button>
                <input type="file" id="pdf_upload" accept="application/pdf" hidden="hidden">
            </div>
        </div>
    </div>
</div>

<script src="/assets/plugins/laydate/laydate.js"></script>
<script src="/static/@tools_service_js.name"></script>
<script src="/assets/plugins/ckeditor5/ckeditor5.umd.js"></script>
<script src="/assets/plugins/ckeditor5/translations/zh-cn.umd.js"></script>
<script src="/static/@techsale_js.name"></script>
}, user.show.clone())