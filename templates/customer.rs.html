@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData, cate: &str)

@:base_html({
<div class="customer-set">
    <div class="top-title"><span><i class="fa fa-cubes"></i> 基础信息 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> @cate管理 </span>
        <span id="category" hidden>@cate</span>
    </div>
    <div class="customer-content has-bak">
        <div id="customer-show">
            <div class="table-top">
                <div class="autocomplete customer-search">
                    <input type="text" class="form-control search-input" id="search-input" placeholder="@cate搜索">
                    <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                    @if cate == "客户" {
                    <select class="select-sm" id="cate-select">
                        <option value="全部" selected>全部</option>
                        <option value="制造厂">制造厂</option>
                        <option value="贸易商">贸易商</option>
                        <option value="出口商">出口商</option>
                    </select>
                    }
                </div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="add-button">增加</button>
                        <button class="btn btn-info btn-sm" id="edit-button">编辑</button>
                        @if cate == "客户" {
                        <button class="btn btn-info btn-sm" id="address-button">编辑收货信息</button>
                        <button class="btn btn-info btn-sm" id="reset-button">重置密码</button>
                        }
                    </div>
                    <div class="data-button">
                        <input type="file" id="choose_file2" class="hide" accept="xls,xlsx" />
                        <button class="btn btn-info btn-sm" id="data-update">批量更新</button>
                        <input type="file" id="choose_file" class="hide" accept="xls,xlsx" />
                        <button class="btn btn-info btn-sm" id="data-in">批量导入</button>
                        <button class="btn btn-info btn-sm" id="data-out">导出数据</button>
                    </div>
                </div>
            </div>

            <div class="table-container table-customer">
                <table>
                    <thead>
                        <tr>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@customer_js.name"></script>
}, user.show.clone())