@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="docment-query">
    <div class="top-title"><span><i class="fa fa-wrench"></i> 功能设置 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 反审查询 </span>
    </div>
    <div class="query-content">
        <div id="query-show" class="has-bak">
            <div class="table-top">
                <div class="query-search">
                    <div class="search-div">
                        <input type="text" class="form-control search-input" id="search-input" placeholder="综合搜索">
                        <input type="text" class="form-control search-input date-input" id="search-date1" placeholder="起始日期">
                        -
                        <input type="text" class="form-control search-input date-input" id="search-date2" placeholder="终止日期">
                        <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                    </div>
                </div>
                <div class="table-tools">
                </div>
            </div>

            <div class="table-container table-documents">
                <table>
                    <thead>
                        <tr>
                            <th width="5%">序号</th>
                            <th width="10%">单号</th>
                            <th width="10%">类别</th>
                            <th width="10%">日期</th>
                            <th width="7%">经办人</th>
                            <th width="6%">提交审核</th>
                            <th width="7%">审核</th>
                            <th width="7%">反审人</th>
                            <th width="8%">反审日期</th>
                            <th width="7%">区域</th>
                            <th width="10%">备注</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<script src="/assets/plugins/laydate/laydate.js"></script>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@antiquery_js.name"></script>
}, user.show.clone())