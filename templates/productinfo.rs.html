@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="docment-query business_query stockinout product_info">
    <div class="top-title"><span><i class="fa fa-cubes"></i> 基础信息 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 产品信息 </span>
        <p id="auto_cate" hidden></p>
    </div>
    <div class="query-content has-bak">
        <div id="query-show">
            <div class="table-top">
                <div class="query-search">
                    <div class="search-div">
                        <input type="text" class="form-control search-input" id="search-fields" placeholder="综合搜索" />
                        <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                    </div>
                </div>
                <div class="data-button">
                    <button class="btn btn-info btn-sm" id="button-add">增加</button>
                    <button class="btn btn-info btn-sm" id="button-edit">编辑</button>
                    <!-- <button class="btn btn-info btn-sm" id="button-price">价格参考</button> -->
                    <button class="btn btn-info btn-sm" id="button-out">导出数据</button>
                </div>
            </div>

            <div class="table-container table-documents" id="table-stockout">
                <table>
                    <thead>
                        <tr>
                            <th width="5%">序号</th>
                            <th width="8%">名称</th>
                            <th width="10%">牌号</th>
                            <th width="10%">规格</th>
                            <th width="12%">状态</th>
                            <th width="15%">技术编号</th>
                            <th width="8%">供应商</th>
                            <th width="8%">库存支数</th>
                            <th width="8%">库存长度</th>
                            <th width="8%">库存重量</th>
                            <th width="8%">安全库存预警</th>
                            <th width="8%">最高库存预警</th>
                            <th width="8%">在途长度</th>
                            <th width="12%">在途到货日</th>
                            <th width="10%">备注</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div class="filter-container">
        <div class="f-title">
            <label class="check-radio" for="f-check-all">
                <input class="form-check" type="checkbox" id="f-check-all">
                <span class="checkmark"></span>
                <span class="all-choose">全选</span>
            </label>
        </div>
        <div class="f-choose"></div>
        <div class="f-sumit">
            <button class="btn btn-info btn-sm f-button" id="f-cancel">取消</button>
            <button class="btn btn-info btn-sm f-button" id="f-ok">确定</button>
        </div>
        <div id="filter-name" hidden></div>
    </div>
</div>

<script src="/assets/plugins/laydate/laydate.js"></script>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@productinfo_js.name"></script>
}, user.show.clone())