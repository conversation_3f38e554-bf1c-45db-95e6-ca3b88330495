@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="product-set">
    <div class="top-title"><span><i class="fa fa-retweet"></i> 仓储管理 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 库存状态 </span>
    </div>
    <div class="product-content has-bak">
        <div class="tree-show">
            <div class="autocomplete table-top">
                <input type="text" class="form-control search-input" id="auto_input" placeholder="商品搜索">
                <button id="auto_search" class="btn btn-info btn-sm"><img src="/assets/img/zoom.png" width=24></button>
            </div>
            <div class="tree-title">商品分类　</div>
            <div class="tree-container">
                <ul id="tree"></ul>
                <div>
                    <ul id="context-menu">
                        <li><a href="javascript:;" id="context-add">新增</a></li>
                        <li><a href="javascript:;" id="context-edit">编辑</a></li>
                    </ul>
                </div>

                <div id="zhezhao"></div>
            </div>
        </div>
        <div id="product-show">
            <div class="table-top">
                <div class="product-select">
                    <select class="select-sm " id="p-select">
                        <option value="正常销售" selected>正常销售</option>
                        <option value="销售锁定">销售锁定</option>
                        <option value="自用库">自用库</option>
                        <option value="不合格品">不合格品</option>
                        <option value="已切完">已切完</option>
                    </select>
                    <input type="text" class="form-control search-input" id="search-input" placeholder="联合搜索">
                    <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                    <span id="product-name"></span><span id="product-id"></span>                    
                </div>
                <div class="info-show">
                </div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm hide" id="add-button">增加</button>
                        <button class="btn btn-info btn-sm" id="find-button">查阅出库</button>
                        <button class="btn btn-info btn-sm" id="all-button">全部属性</button>
                        <button class="btn btn-info btn-sm" id="edit-button">编辑物料</button>
                    </div>
                    <div class="data-button">
                        <input type="file" id="choose_file2" class="hide" accept="xls,xlsx" />
                        <button class="btn btn-info btn-sm hide" id="data-update">批量更新</button>
                        <input type="file" id="choose_file" class="hide" accept="xls,xlsx" />
                        <button class="btn btn-info btn-sm hide" id="data-in">批量导入</button>
                        <button class="btn btn-info btn-sm" id="data-out">分类导出</button>
                        <button class="btn btn-info btn-sm" id="data-all-out">全部导出</button>
                    </div>
                </div>
            </div>

            <div class="table-container table-product">
                <table>
                    <thead>
                        <tr>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div class="filter-container">
        <div class="f-title">
            <label class="check-radio" for="f-check-all">
                <input class="form-check" type="checkbox" id="f-check-all">
                <span class="checkmark"></span>
                <span class="all-choose">全选</span>
            </label>
        </div>
        <div class="f-choose"></div>
        <div class="f-sumit">
            <button class="btn btn-info btn-sm f-button" id="f-cancel">取消</button>
            <button class="btn btn-info btn-sm f-button" id="f-ok">确定</button>
        </div>
        <div id="filter-name" hidden></div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@productset_js.name"></script>
}, user.show.clone())