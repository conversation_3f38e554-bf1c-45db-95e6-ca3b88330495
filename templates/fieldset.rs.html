@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="fields-set">
    <div class="top-title"><span><i class="fa fa-wrench"></i> 功能设置 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 字段设置 </span>
    </div>
    <div class="system-content has-bak">
        <div id="product-show">
            <div class="table-top">
                <div class="table-title">
                    <select name="table-choose" class="select-sm" id="table-choose">
                        <option value="" selected disabled hidden>请选择数据表格</option>
                        <option value="商品规格">商品规格</option>
                        <option value="客户">客户</option>
                        <option value="供应商">供应商</option>
                        <option value="采购单据">采购单据</option>
                        <option value="销售单据">销售单据</option>
                        <option value="销售开票">销售开票</option>
                        <option value="入库单据">入库单据</option>
                        <option value="出库单据">出库单据</option>
                        <option value="发货单据">发货单据</option>
                        <option value="库存调入">库存调入</option>
                        <option value="库存调出">库存调出</option>
                    </select>
                </div>
                <div class="cate-title">编辑显示</div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="sumit-button" disabled=true>提交修改</button>
                    </div>
                </div>
            </div>

            <div class="table-container table-product">
                <table>
                    <thead>
                        <tr>
                            <th class="hide"></th>
                            <th width=6%>序号</th>
                            <th width="15%">字段</th>
                            <th width=10%>数据类型</th>
                            <th width=15%>显示名称</th>
                            <th width=8%>宽度</th>
                            <th width=15%>控件类型</th>
                            <th width=20%>可选值</th>
                            <th width=10%>默认值</th>
                            <th width=8%>使用</th>
                            <th width=8%>显示</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="table-tip">提示：按住鼠标拖拽，改变字段显示顺序。“可选值”用下划线 _ 隔开</div>
                    <div class="table-button">
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>
                </div>
            </div>
        </div>
        <div id="inout-show">
            <div class="table-top">
                <div>出入库显示</div>
                <div class="table-tools">
                    <div class="table-edit">
                        <button class="btn btn-info btn-sm" id="sumit-button2" disabled=true>提交修改</button>
                    </div>
                </div>
            </div>
            <div class="table-container table-inout">
                <table>
                    <thead>
                        <tr>
                            <th class="hide"></th>
                            <th width=20%>序号</th>
                            <th>显示名称</th>
                            <th width=20%>宽度</th>
                            <th width=20%>显示</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="table-tip">提示：按住鼠标拖拽，改变字段显示顺序</div>
                    <div class="table-button">
                    </div>

                    <div class="table-info">
                        共 <span id="total-records2"></span> 条记录
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="choose-info">
        请于左上角选择数据表格
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@fieldset_js.name"></script>
}, user.show.clone())