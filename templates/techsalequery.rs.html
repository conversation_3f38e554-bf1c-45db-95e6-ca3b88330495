@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData)

@:base_html({
<div class="docment-query business_query stockinout techbuyquery">
    <div class="top-title"><span><i class="fa fa-cubes"></i> 基础信息 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 客户规范查询 </span>
        <p id="auto_cate" hidden></p>
    </div>
    <div class="query-content has-bak">
        <div id="query-show">
            <div class="table-top">
                <div class="query-search">
                    <div class="search-div">
                        <input type="text" class="form-control search-input" id="search-fields" placeholder="名称搜索" />
                        <button class="btn btn-info btn-sm" id="serach-button">搜索</button>
                    </div>
                </div>
                <div class="data-button">
                    <button class="btn btn-info btn-sm" id="button-fei">作废协议</button>
                </div>
            </div>

            <div class="table-container table-documents" id="table-stockout">
                <table>
                    <thead>
                        <tr>
                            <th width="5%">序号</th>
                            <th width="20%">客户</th>
                            <th width="20%">协议名称</th>
                            <th width="10%">编号</th>
                            <th width="8%">产品类型</th>
                            <th width="8%">牌号</th>
                            <th width="8%">编辑人</th>
                            <th width="10%">编辑日期</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <div class="table-ctrl">
                    <div class="tools-button"></div>
                    <div class="table-button">
                        <button class="page-button btn" id="first" title="首页"><img src="/assets/img/backward.png"
                                width="12px"></button>
                        <button class="page-button btn" id="pre" title="前一页"><img src="/assets/img/backward2.png"
                                width="12px"></button>
                        <p class="seperator"></p>
                        <span>第</span><input type="text" class="form-control" id="page-input" value="1">
                        <span>页，共</span><span id="pages"></span><span>页</span>
                        <p class="seperator"></p>
                        <button class="page-button btn" id="aft" title="后一页"><img src="/assets/img/forward2.png"
                                width="12px"></button>
                        <button class="page-button btn" id="last" title="尾页"><img src="/assets/img/forward.png"
                                width="12px"></button>
                    </div>

                    <div class="table-info">
                        共 <span id="total-records"></span> 条记录
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="/assets/plugins/laydate/laydate.js"></script>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@techsalequery_js.name"></script>
}, user.show.clone())