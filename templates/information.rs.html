@use super::base_html;
@use crate::service::UserData;
@use super::statics::*;
@(user: UserData, cate: &str)

@:base_html({
<div class="information">
    <div class="top-title"><span><i class="fa fa-user"></i> 客户供应商 </span>
        <span class="t1"><i class="fa fa-angle-double-right"></i> 信息通知 </span>
        <p id="help-info" title="帮助信息"><img></p>
        <span id="category" hidden>@cate</span>
    </div>
    <div class="infor-content has-bak">
        <div id="infor-show">
            <div class="title">
                <span>标题</span>
                <input type="text" class="form-control search-input" id="infor-title" placeholder="请输入标题">
            </div>

            <div class="content">
                <span>内容</span>
                <textarea id="infor-textarea" placeholder="请输入内容，缩进需输入全角空格"></textarea>
            </div>
            <div class="sumit-button">
                <label class="check-radio">
                    <input type="checkbox" id="show-check">
                    <span>显示</span>
                    <span class="checkmark"></span>
                </label>
                <button class="btn btn-info btn-sm" id="info-submit">提交</button>
            </div>
        </div>
    </div>
</div>
<script src="/static/@tools_service_js.name"></script>
<script src="/static/@information_js.name"></script>
}, user.show.clone())